Looking in indexes: https://repo.huaweicloud.com/repository/pypi/simple/
Requirement already satisfied: pydantic-settings in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (2.8.1)
Requirement already satisfied: accelerate in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (1.0.1)
Requirement already satisfied: pydantic>=2.7.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from pydantic-settings) (2.10.6)
Requirement already satisfied: python-dotenv>=0.21.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from pydantic-settings) (1.0.1)
Requirement already satisfied: numpy<3.0.0,>=1.17 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from accelerate) (1.24.4)
Requirement already satisfied: packaging>=20.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from accelerate) (25.0)
Requirement already satisfied: psutil in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from accelerate) (7.0.0)
Requirement already satisfied: pyyaml in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from accelerate) (6.0.2)
Requirement already satisfied: torch>=1.10.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from accelerate) (2.4.1+cu121)
Requirement already satisfied: huggingface-hub>=0.21.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from accelerate) (0.33.1)
Requirement already satisfied: safetensors>=0.4.3 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from accelerate) (0.5.3)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from huggingface-hub>=0.21.0->accelerate) (3.13.1)
Requirement already satisfied: fsspec>=2023.5.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from huggingface-hub>=0.21.0->accelerate) (2024.6.1)
Requirement already satisfied: requests in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from huggingface-hub>=0.21.0->accelerate) (2.32.4)
Requirement already satisfied: tqdm>=4.42.1 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from huggingface-hub>=0.21.0->accelerate) (4.67.1)
Requirement already satisfied: typing-extensions>=3.7.4.3 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from huggingface-hub>=0.21.0->accelerate) (4.12.2)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from pydantic>=2.7.0->pydantic-settings) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from pydantic>=2.7.0->pydantic-settings) (2.27.2)
Requirement already satisfied: sympy in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from torch>=1.10.0->accelerate) (1.13.3)
Requirement already satisfied: networkx in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from torch>=1.10.0->accelerate) (3.0)
Requirement already satisfied: jinja2 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from torch>=1.10.0->accelerate) (3.1.4)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from tqdm>=4.42.1->huggingface-hub>=0.21.0->accelerate) (0.4.6)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from jinja2->torch>=1.10.0->accelerate) (2.1.5)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from requests->huggingface-hub>=0.21.0->accelerate) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from requests->huggingface-hub>=0.21.0->accelerate) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from requests->huggingface-hub>=0.21.0->accelerate) (2.2.3)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from requests->huggingface-hub>=0.21.0->accelerate) (2025.6.15)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from sympy->torch>=1.10.0->accelerate) (1.3.0)
