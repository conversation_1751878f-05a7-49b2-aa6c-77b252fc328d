# Web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# AI/ML related
diffusers>=0.21.0,<0.30.0  # Compatible with peft 0.13.2
transformers>=4.35.0
peft>=0.13.0,<0.15.0  # Parameter-Efficient Fine-Tuning for LoRA support (compatible with Python 3.8)
safetensors>=0.4.0  # Safe tensor format
accelerate>=0.25.0  # Model acceleration

# GSA object detection
groundingdino-py>=0.1.0
segment-anything>=1.0
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Image processing
Pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.21.0  # Compatible with Python 3.13
scikit-image>=0.19.0  # Compatible with Python 3.13
scipy>=1.9.0  # Compatible with Python 3.13
matplotlib>=3.5.0  # Compatible with Python 3.13
rembg>=2.0.50  # Background removal
albumentations>=1.3.0  # Image augmentation

# Enhanced image processing
imageio>=2.31.0  # Advanced image I/O
imageio-ffmpeg>=0.4.8  # Video support
Wand>=0.6.11  # ImageMagick binding for advanced effects
rawpy>=0.18.1  # RAW image support
pillow-heif>=0.13.0  # HEIF support for Pillow
webcolors>=1.13  # Color name/hex conversion

# Data processing
pydantic>=2.4.0
pydantic-settings>=2.0.3
python-multipart>=0.0.6

# Async task processing
celery>=5.3.0
redis>=5.0.0  # or use RabbitMQ

# Utility libraries
requests>=2.31.0
aiofiles>=23.2.0
python-dotenv>=1.0.0

# Performance monitoring and optimization
psutil>=5.9.0
memory-profiler>=0.61.0  # Memory usage profiling
line-profiler>=4.1.0  # Line-by-line profiling
cachetools>=5.3.0  # Advanced caching utilities

# Development tools
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.9.0
flake8>=6.1.0

# Logging
loguru>=0.7.0

# Database (optional)
sqlalchemy>=2.0.0
alembic>=1.12.0

# Monitoring (optional)
prometheus-client>=0.18.0
