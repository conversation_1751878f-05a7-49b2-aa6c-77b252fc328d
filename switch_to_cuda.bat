@echo off
:: 设置代码页为UTF-8，同时处理可能的错误
chcp 65001 >nul 2>&1
if errorlevel 1 (
    echo Setting UTF-8 encoding failed, using default encoding
)

:: 设置控制台标题
title Switch to CUDA Mode - Military Target Dataset Generation Platform

:: 设置环境变量确保Python使用UTF-8编码
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set LANG=zh_CN.UTF-8

echo ========================================
echo    Switch AI Mode to CUDA 12.1
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found, please install Python 3.8+
    pause
    exit /b 1
)

echo [INFO] Python environment detected
echo.

:: 检查网络连接
echo [INFO] Checking network connection...
ping -n 1 pypi.org >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Network connection to pypi.org failed
    echo [TIP] Please check your internet connection or proxy settings
    echo.
) else (
    echo [INFO] Network connection OK
    echo.
)

:: 显示菜单
echo Available options:
echo 1. Full switch to CUDA mode (recommended)
echo 2. Force switch (skip NVIDIA driver check)
echo 3. Skip uninstall CPU PyTorch
echo 4. Verify current installation only
echo 5. Manual troubleshooting commands
echo 0. Exit
echo.

:menu
set /p choice="Please select an option (0-5): "

if "%choice%"=="0" (
    echo Exiting...
    exit /b 0
)

if "%choice%"=="1" (
    echo [INFO] Starting full CUDA mode switch...
    echo [TIP] This may take 10-20 minutes depending on your network speed
    echo.
    python scripts/switch_to_cuda.py
    goto end
)

if "%choice%"=="2" (
    echo [INFO] Starting forced CUDA mode switch...
    echo [WARNING] Skipping NVIDIA driver check
    echo.
    python scripts/switch_to_cuda.py --force
    goto end
)

if "%choice%"=="3" (
    echo [INFO] Starting CUDA mode switch without CPU uninstall...
    echo.
    python scripts/switch_to_cuda.py --skip-uninstall
    goto end
)

if "%choice%"=="4" (
    echo [INFO] Verifying current installation...
    python scripts/switch_to_cuda.py --verify-only
    goto end
)

if "%choice%"=="5" (
    echo [INFO] Manual troubleshooting commands:
    echo.
    echo 1. Upgrade pip:
    echo    python -m pip install --upgrade pip --trusted-host pypi.org
    echo.
    echo 2. Install CUDA PyTorch manually:
    echo    python -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121 --trusted-host download.pytorch.org
    echo.
    echo 3. Install from PyPI (may be CPU version):
    echo    python -m pip install torch torchvision torchaudio --trusted-host pypi.org
    echo.
    echo 4. Use Chinese mirror:
    echo    python -m pip install torch torchvision torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn
    echo.
    echo Press any key to return to menu...
    pause >nul
    cls
    goto menu
)

echo [ERROR] Invalid choice, please try again.
echo.
goto menu

:end
echo.
echo ========================================
echo Operation completed!
echo.
echo [TIP] If installation failed due to network issues:
echo 1. Check your internet connection
echo 2. Try running as administrator
echo 3. Temporarily disable antivirus/firewall
echo 4. Use option 5 for manual commands
echo.
echo Press any key to close this window...
echo ========================================
pause >nul 