"""
批量编辑对话框
"""
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox, QCheckBox,
    QMessageBox, QProgressBar, QGroupBox, QFrame, QScrollArea, QWidget
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

# 修复导入路径问题
import sys
import os

# 添加src目录到Python路径，确保可以找到services模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# 使用绝对导入，与main_window.py保持一致
from services.image_management_client import ImageManagementClient
from styles.button_styles import ButtonStyleManager


class BatchEditWorker(QThread):
    """批量编辑工作线程"""
    edit_finished = pyqtSignal(dict)  # 编辑结果
    edit_failed = pyqtSignal(str)  # 错误信息
    progress_updated = pyqtSignal(str)  # 进度更新
    
    def __init__(self, client: ImageManagementClient, image_ids: List[int], updates: Dict[str, Any]):
        super().__init__()
        self.client = client
        self.image_ids = image_ids
        self.updates = updates
    
    def run(self):
        try:
            self.progress_updated.emit(f"正在批量更新 {len(self.image_ids)} 张图片...")
            result = self.client.batch_update_images(self.image_ids, **self.updates)
            
            if result.get('success'):
                self.edit_finished.emit(result)
            else:
                self.edit_failed.emit(result.get('message', '批量编辑失败'))
        except Exception as e:
            self.edit_failed.emit(f"批量编辑失败: {str(e)}")


class BatchEditDialog(QDialog):
    """批量编辑对话框"""
    edit_completed = pyqtSignal(dict)  # 编辑完成信号
    
    def __init__(self, image_ids: List[int], parent=None):
        super().__init__(parent)
        self.client = ImageManagementClient()
        self.image_ids = image_ids
        self.edit_worker = None
        
        self.setWindowTitle("批量编辑")
        self.setModal(True)
        self._setup_dialog_size()
        
        self._setup_ui()
        self._connect_signals()

    def _setup_dialog_size(self):
        """根据屏幕尺寸设置对话框大小"""
        from PyQt6.QtGui import QGuiApplication

        screen = QGuiApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # 根据屏幕尺寸调整对话框大小
        if screen_width <= 1280:
            # 小屏幕：使用更紧凑的尺寸
            dialog_width = min(450, int(screen_width * 0.7))
            dialog_height = min(500, int(screen_height * 0.75))
        else:
            # 大屏幕：使用标准尺寸
            dialog_width = 500
            dialog_height = 600

        self.resize(dialog_width, dialog_height)

        # 设置最小尺寸确保可用性
        self.setMinimumSize(400, 450)
    
    def _setup_ui(self):
        """设置UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 创建内容widget
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(12)  # 减少间距
        layout.setContentsMargins(15, 15, 15, 15)  # 减少边距
        
        # 标题
        title_label = QLabel(f"批量编辑 {len(self.image_ids)} 张图片")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明
        info_label = QLabel("只有勾选的字段会被更新，未勾选的字段保持原值不变")
        info_label.setStyleSheet("color: #888; font-size: 12px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 编辑区域
        edit_group = QGroupBox("编辑字段")
        edit_layout = QVBoxLayout(edit_group)
        edit_layout.setSpacing(15)
        
        # 描述
        desc_frame = QFrame()
        desc_layout = QVBoxLayout(desc_frame)
        desc_layout.setContentsMargins(0, 0, 0, 0)
        desc_layout.setSpacing(5)
        
        self.description_checkbox = QCheckBox("更新描述")
        desc_layout.addWidget(self.description_checkbox)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("输入新的描述...")
        self.description_edit.setEnabled(False)
        desc_layout.addWidget(self.description_edit)
        
        edit_layout.addWidget(desc_frame)
        
        # 标签
        tags_frame = QFrame()
        tags_layout = QVBoxLayout(tags_frame)
        tags_layout.setContentsMargins(0, 0, 0, 0)
        tags_layout.setSpacing(5)
        
        self.tags_checkbox = QCheckBox("更新标签")
        tags_layout.addWidget(self.tags_checkbox)
        
        self.tags_edit = QLineEdit()
        self.tags_edit.setPlaceholderText("输入标签，用逗号分隔...")
        self.tags_edit.setEnabled(False)
        tags_layout.addWidget(self.tags_edit)
        
        edit_layout.addWidget(tags_frame)
        
        # 分类
        category_frame = QFrame()
        category_layout = QVBoxLayout(category_frame)
        category_layout.setContentsMargins(0, 0, 0, 0)
        category_layout.setSpacing(5)
        
        self.category_checkbox = QCheckBox("更新分类")
        category_layout.addWidget(self.category_checkbox)
        
        self.category_combo = QComboBox()
        self.category_combo.addItems(["", "训练集", "验证集", "测试集", "其他"])
        self.category_combo.setEnabled(False)
        category_layout.addWidget(self.category_combo)
        
        edit_layout.addWidget(category_frame)
        
        # 收藏状态
        favorite_frame = QFrame()
        favorite_layout = QVBoxLayout(favorite_frame)
        favorite_layout.setContentsMargins(0, 0, 0, 0)
        favorite_layout.setSpacing(5)
        
        self.favorite_checkbox = QCheckBox("更新收藏状态")
        favorite_layout.addWidget(self.favorite_checkbox)
        
        self.favorite_combo = QComboBox()
        self.favorite_combo.addItems(["取消收藏", "设为收藏"])
        self.favorite_combo.setEnabled(False)
        favorite_layout.addWidget(self.favorite_combo)
        
        edit_layout.addWidget(favorite_frame)
        
        layout.addWidget(edit_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #888; font-size: 12px;")
        layout.addWidget(self.status_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.cancel_btn = ButtonStyleManager.create_button("取消", "secondary", min_width=70)
        self.apply_btn = ButtonStyleManager.create_button("应用", "primary", min_width=70)
        self.apply_btn.setEnabled(False)

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.apply_btn)

        layout.addLayout(button_layout)

        # 将内容widget放入滚动区域
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
    
    def _connect_signals(self):
        """连接信号"""
        # 复选框状态改变时启用/禁用对应的编辑控件
        self.description_checkbox.toggled.connect(self.description_edit.setEnabled)
        self.description_checkbox.toggled.connect(self._update_apply_button)
        
        self.tags_checkbox.toggled.connect(self.tags_edit.setEnabled)
        self.tags_checkbox.toggled.connect(self._update_apply_button)
        
        self.category_checkbox.toggled.connect(self.category_combo.setEnabled)
        self.category_checkbox.toggled.connect(self._update_apply_button)
        
        self.favorite_checkbox.toggled.connect(self.favorite_combo.setEnabled)
        self.favorite_checkbox.toggled.connect(self._update_apply_button)
        
        # 按钮信号
        self.apply_btn.clicked.connect(self._apply_changes)
        self.cancel_btn.clicked.connect(self.reject)
    
    def _update_apply_button(self):
        """更新应用按钮状态"""
        has_changes = (
            self.description_checkbox.isChecked() or
            self.tags_checkbox.isChecked() or
            self.category_checkbox.isChecked() or
            self.favorite_checkbox.isChecked()
        )
        
        is_working = self.edit_worker and self.edit_worker.isRunning()
        self.apply_btn.setEnabled(has_changes and not is_working)
    
    def _apply_changes(self):
        """应用更改"""
        # 收集要更新的字段
        updates = {}
        
        if self.description_checkbox.isChecked():
            description = self.description_edit.toPlainText().strip()
            updates['description'] = description
        
        if self.tags_checkbox.isChecked():
            tags_text = self.tags_edit.text().strip()
            if tags_text:
                tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()]
            else:
                tags = []
            updates['tags'] = tags
        
        if self.category_checkbox.isChecked():
            category = self.category_combo.currentText()
            updates['category'] = category if category else None
        
        if self.favorite_checkbox.isChecked():
            is_favorite = self.favorite_combo.currentIndex() == 1
            updates['is_favorite'] = is_favorite
        
        if not updates:
            QMessageBox.warning(self, "警告", "请至少选择一个要更新的字段")
            return
        
        # 确认对话框
        reply = QMessageBox.question(
            self, "确认批量编辑", 
            f"确定要对 {len(self.image_ids)} 张图片应用这些更改吗？\n\n"
            f"将要更新的字段: {', '.join(updates.keys())}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        # 禁用按钮并显示进度
        self.apply_btn.setEnabled(False)
        self.apply_btn.setText("应用中...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        # 启动编辑工作线程
        self.edit_worker = BatchEditWorker(self.client, self.image_ids, updates)
        self.edit_worker.edit_finished.connect(self._on_edit_finished)
        self.edit_worker.edit_failed.connect(self._on_edit_failed)
        self.edit_worker.progress_updated.connect(self._on_progress_updated)
        self.edit_worker.start()
    
    def _on_edit_finished(self, result):
        """编辑完成"""
        self.progress_bar.setVisible(False)
        self.apply_btn.setText("应用")
        self._update_apply_button()
        
        updated_count = result.get('data', {}).get('updated_count', 0)
        QMessageBox.information(
            self, "批量编辑完成", 
            f"成功更新了 {updated_count} 张图片！"
        )
        
        self.edit_completed.emit(result)
        self.accept()
    
    def _on_edit_failed(self, error_message):
        """编辑失败"""
        self.progress_bar.setVisible(False)
        self.apply_btn.setText("应用")
        self._update_apply_button()
        
        QMessageBox.critical(self, "批量编辑失败", f"批量编辑失败:\n{error_message}")
    
    def _on_progress_updated(self, message):
        """进度更新"""
        self.status_label.setText(message)
