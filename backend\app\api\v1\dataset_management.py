"""
数据集管理API路由 - 文件系统存储方案
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Form, UploadFile, File
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from ...services.common.dataset_manager import DatasetManager
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/datasets", tags=["数据集管理"])

# 初始化数据集管理器，使用相对于backend目录的路径
dataset_manager = DatasetManager("data/datasets")

# Pydantic模型
class DatasetCreateRequest(BaseModel):
    name: Optional[str] = None
    naming_format: Optional[str] = "timestamp"  # "timestamp" 或 "sequential"

class DatasetRenameRequest(BaseModel):
    old_name: str
    new_name: str

class ImageRenameRequest(BaseModel):
    folder_name: str
    old_filename: str
    new_filename: str

class ImageDeleteRequest(BaseModel):
    folder_name: str
    filename: str

class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None

@router.get("/list")
async def list_datasets():
    """列出所有数据集文件夹"""
    try:
        datasets = dataset_manager.list_datasets()
        return {
            "success": True,
            "message": f"获取到 {len(datasets)} 个数据集",
            "data": {
                "datasets": datasets,
                "total": len(datasets)
            }
        }
    except Exception as e:
        logger.error(f"列出数据集失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"列出数据集失败: {str(e)}")

@router.post("/create")
async def create_dataset(request: DatasetCreateRequest):
    """创建新的数据集文件夹"""
    try:
        folder_name, folder_path = dataset_manager.create_dataset_folder(
            name=request.name,
            naming_format=request.naming_format
        )
        return {
            "success": True,
            "message": "数据集文件夹创建成功",
            "data": {
                "folder_name": folder_name,
                "folder_path": str(folder_path),
                "naming_format": request.naming_format
            }
        }
    except Exception as e:
        logger.error(f"创建数据集失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建数据集失败: {str(e)}")

@router.post("/rename")
async def rename_dataset(request: DatasetRenameRequest):
    """重命名数据集文件夹"""
    try:
        success = dataset_manager.rename_dataset_folder(request.old_name, request.new_name)
        if success:
            return {
                "success": True,
                "message": "数据集文件夹重命名成功"
            }
        else:
            raise HTTPException(status_code=400, detail="重命名失败")
    except Exception as e:
        logger.error(f"重命名数据集失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重命名数据集失败: {str(e)}")

@router.delete("/{folder_name}")
async def delete_dataset(folder_name: str):
    """删除数据集文件夹"""
    try:
        success = dataset_manager.delete_dataset_folder(folder_name)
        if success:
            return {
                "success": True,
                "message": "数据集文件夹删除成功"
            }
        else:
            raise HTTPException(status_code=404, detail="数据集文件夹不存在")
    except Exception as e:
        logger.error(f"删除数据集失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除数据集失败: {str(e)}")

@router.get("/{folder_name}/images")
async def list_images_in_dataset(
    folder_name: str,
    include_annotated: bool = True
):
    """列出数据集中的所有图片（包括标注图片）"""
    try:
        images = dataset_manager.list_images_in_dataset(folder_name, include_annotated)

        # 统计不同类型的图片数量
        total_images = len(images)
        annotated_count = len([img for img in images if img.get("source") == "annotation"])
        regular_count = total_images - annotated_count

        return {
            "success": True,
            "message": f"获取到 {total_images} 张图片（常规: {regular_count}, 标注: {annotated_count}）",
            "data": {
                "images": images,
                "total": total_images,
                "regular_count": regular_count,
                "annotated_count": annotated_count,
                "folder_name": folder_name
            }
        }
    except Exception as e:
        logger.error(f"列出数据集图片失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"列出数据集图片失败: {str(e)}")

@router.get("/annotated/list")
async def list_all_annotated_images():
    """列出所有标注图片"""
    try:
        annotated_images = dataset_manager._scan_annotated_images()
        return {
            "success": True,
            "message": f"获取到 {len(annotated_images)} 张标注图片",
            "data": {
                "images": annotated_images,
                "total": len(annotated_images)
            }
        }
    except Exception as e:
        logger.error(f"列出标注图片失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"列出标注图片失败: {str(e)}")

@router.post("/{folder_name}/upload")
async def upload_image_to_dataset(
    folder_name: str,
    file: UploadFile = File(...),
    description: Optional[str] = Form(None),
    military_target: Optional[str] = Form(None),
    weather: Optional[str] = Form(None),
    scene: Optional[str] = Form(None),
    new_filename: Optional[str] = Form(None)
):
    """上传图片到数据集"""
    try:
        # 验证文件类型
        if not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="只支持图片文件")
        
        # 保存临时文件
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # 构建描述信息
            image_description = {
                "description": description or "",
                "military_target": military_target,
                "weather": weather,
                "scene": scene,
                "original_filename": file.filename,
                "content_type": file.content_type
            }
            
            # 添加到数据集
            success = dataset_manager.add_image_to_dataset(
                folder_name,
                temp_file_path,
                image_description,
                new_filename if new_filename else None
            )
            
            if success:
                return {
                    "success": True,
                    "message": "图片上传成功"
                }
            else:
                raise HTTPException(status_code=400, detail="添加图片到数据集失败")
                
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
    except Exception as e:
        logger.error(f"上传图片到数据集失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"上传图片失败: {str(e)}")

@router.post("/images/rename")
async def rename_image(request: ImageRenameRequest):
    """重命名数据集中的图片"""
    try:
        success = dataset_manager.rename_image(
            request.folder_name, 
            request.old_filename, 
            request.new_filename
        )
        if success:
            return {
                "success": True,
                "message": "图片重命名成功"
            }
        else:
            raise HTTPException(status_code=400, detail="重命名失败")
    except Exception as e:
        logger.error(f"重命名图片失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重命名图片失败: {str(e)}")

@router.post("/images/delete")
async def delete_image(request: ImageDeleteRequest):
    """删除数据集中的图片"""
    try:
        success = dataset_manager.delete_image(request.folder_name, request.filename)
        if success:
            return {
                "success": True,
                "message": "图片删除成功"
            }
        else:
            raise HTTPException(status_code=404, detail="图片文件不存在")
    except Exception as e:
        logger.error(f"删除图片失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除图片失败: {str(e)}")

@router.get("/statistics")
async def get_dataset_statistics():
    """获取数据集统计信息"""
    try:
        stats = dataset_manager.get_dataset_statistics()
        return {
            "success": True,
            "message": "获取统计信息成功",
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取数据集统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.post("/{folder_name}/add-generated")
async def add_generated_image_to_dataset(
    folder_name: str,
    image_path: str,
    description: dict
):
    """将生成的图片添加到数据集（内部API）"""
    try:
        success = dataset_manager.add_image_to_dataset(
            folder_name, 
            image_path, 
            description
        )
        if success:
            return {
                "success": True,
                "message": "生成的图片已添加到数据集"
            }
        else:
            raise HTTPException(status_code=400, detail="添加图片到数据集失败")
    except Exception as e:
        logger.error(f"添加生成图片到数据集失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加图片失败: {str(e)}")
