"""
简化的图片合成器
实现背景图片和军事目标的简单叠加，支持位置、大小和透明度调整
"""
import random
import logging
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image, ImageDraw, ImageFilter
import numpy as np

logger = logging.getLogger(__name__)


class SimpleCompositor:
    """简化的图片合成器"""
    
    def __init__(self):
        """初始化合成器"""
        self.last_composition_info = {}
        logger.info("简化图片合成器初始化完成")
    
    def composite_images(
        self,
        background_img: Image.Image,
        target_img: Image.Image,
        **kwargs
    ) -> Tuple[Image.Image, Dict[str, Any]]:
        """
        合成图片
        
        Args:
            background_img: 背景图片
            target_img: 目标图片
            **kwargs: 合成参数
            
        Returns:
            Tuple[Image.Image, Dict[str, Any]]: 合成后的图片和合成信息
        """
        try:
            # 获取合成参数
            position = kwargs.get('position')  # 指定位置
            random_position = kwargs.get('random_position', True)  # 是否随机位置
            add_shadow = kwargs.get('add_shadow', False)  # 是否添加阴影
            shadow_offset = kwargs.get('shadow_offset', (5, 5))  # 阴影偏移
            shadow_blur = kwargs.get('shadow_blur', 3)  # 阴影模糊
            shadow_opacity = kwargs.get('shadow_opacity', 0.3)  # 阴影透明度
            
            # 计算放置位置
            position_info = self._calculate_position(
                background_img.size, target_img.size, position, random_position
            )
            
            # 创建合成图片
            composite_img = background_img.copy()
            
            # 添加阴影（如果需要）
            if add_shadow:
                composite_img = self._add_shadow(
                    composite_img, target_img, position_info, 
                    shadow_offset, shadow_blur, shadow_opacity
                )
            
            # 粘贴目标图片
            composite_img = self._paste_target(composite_img, target_img, position_info)
            
            # 保存合成信息
            composition_info = {
                "target_position": position_info,
                "target_size": target_img.size,
                "background_size": background_img.size,
                "has_shadow": add_shadow,
                "shadow_config": {
                    "offset": shadow_offset,
                    "blur": shadow_blur,
                    "opacity": shadow_opacity
                } if add_shadow else None
            }
            
            self.last_composition_info = composition_info
            
            return composite_img, composition_info
            
        except Exception as e:
            logger.error(f"图片合成失败: {str(e)}")
            return background_img, {}
    
    def _calculate_position(
        self,
        background_size: Tuple[int, int],
        target_size: Tuple[int, int],
        position: Optional[Tuple[int, int]],
        random_position: bool
    ) -> Tuple[int, int]:
        """
        计算目标图片的放置位置
        
        Args:
            background_size: 背景图片尺寸
            target_size: 目标图片尺寸
            position: 指定位置
            random_position: 是否随机位置
            
        Returns:
            Tuple[int, int]: 计算出的位置 (x, y)
        """
        try:
            bg_width, bg_height = background_size
            target_width, target_height = target_size
            
            if position:
                # 使用指定位置
                x, y = position
                # 确保位置在有效范围内
                x = max(0, min(x, bg_width - target_width))
                y = max(0, min(y, bg_height - target_height))
            elif random_position:
                # 随机位置，确保目标完全在背景内
                max_x = max(0, bg_width - target_width)
                max_y = max(0, bg_height - target_height)
                x = random.randint(0, max_x)
                y = random.randint(0, max_y)
            else:
                # 居中放置
                x = (bg_width - target_width) // 2
                y = (bg_height - target_height) // 2
            
            return (x, y)
            
        except Exception as e:
            logger.error(f"计算位置失败: {str(e)}")
            return (0, 0)
    
    def _paste_target(
        self,
        composite_img: Image.Image,
        target_img: Image.Image,
        position: Tuple[int, int]
    ) -> Image.Image:
        """
        粘贴目标图片到合成图片上
        
        Args:
            composite_img: 合成图片
            target_img: 目标图片
            position: 粘贴位置
            
        Returns:
            Image.Image: 粘贴后的图片
        """
        try:
            x, y = position
            
            if target_img.mode == 'RGBA':
                # 使用alpha通道进行透明合成
                composite_img.paste(target_img, (x, y), target_img)
            else:
                # 直接粘贴
                composite_img.paste(target_img, (x, y))
            
            return composite_img
            
        except Exception as e:
            logger.error(f"粘贴目标图片失败: {str(e)}")
            return composite_img
    
    def _add_shadow(
        self,
        composite_img: Image.Image,
        target_img: Image.Image,
        position: Tuple[int, int],
        shadow_offset: Tuple[int, int],
        shadow_blur: int,
        shadow_opacity: float
    ) -> Image.Image:
        """
        添加阴影效果
        
        Args:
            composite_img: 合成图片
            target_img: 目标图片
            position: 目标位置
            shadow_offset: 阴影偏移
            shadow_blur: 阴影模糊
            shadow_opacity: 阴影透明度
            
        Returns:
            Image.Image: 添加阴影后的图片
        """
        try:
            x, y = position
            offset_x, offset_y = shadow_offset
            
            # 创建阴影图片
            shadow_img = self._create_shadow(target_img, shadow_blur, shadow_opacity)
            
            # 计算阴影位置
            shadow_x = x + offset_x
            shadow_y = y + offset_y
            
            # 确保阴影在图片范围内
            shadow_x = max(0, min(shadow_x, composite_img.width - shadow_img.width))
            shadow_y = max(0, min(shadow_y, composite_img.height - shadow_img.height))
            
            # 粘贴阴影
            if shadow_img.mode == 'RGBA':
                composite_img.paste(shadow_img, (shadow_x, shadow_y), shadow_img)
            else:
                composite_img.paste(shadow_img, (shadow_x, shadow_y))
            
            return composite_img
            
        except Exception as e:
            logger.error(f"添加阴影失败: {str(e)}")
            return composite_img
    
    def _create_shadow(
        self,
        target_img: Image.Image,
        blur_radius: int,
        opacity: float
    ) -> Image.Image:
        """
        创建阴影图片
        
        Args:
            target_img: 目标图片
            blur_radius: 模糊半径
            opacity: 透明度
            
        Returns:
            Image.Image: 阴影图片
        """
        try:
            # 创建阴影蒙版
            if target_img.mode == 'RGBA':
                # 使用alpha通道创建阴影
                alpha = target_img.split()[-1]
                shadow_mask = alpha.copy()
            else:
                # 创建简单的矩形阴影
                shadow_mask = Image.new('L', target_img.size, 255)
            
            # 应用模糊
            if blur_radius > 0:
                shadow_mask = shadow_mask.filter(ImageFilter.GaussianBlur(blur_radius))
            
            # 调整透明度
            shadow_mask = shadow_mask.point(lambda x: int(x * opacity))
            
            # 创建黑色阴影
            shadow_rgb = Image.new('RGB', target_img.size, (0, 0, 0))
            shadow_img = Image.merge('RGBA', shadow_rgb.split() + (shadow_mask,))
            
            return shadow_img
            
        except Exception as e:
            logger.error(f"创建阴影失败: {str(e)}")
            # 返回透明图片作为fallback
            return Image.new('RGBA', target_img.size, (0, 0, 0, 0))
    
    def generate_bbox_annotation(
        self,
        image_size: Tuple[int, int],
        target_size: Tuple[int, int],
        position: Tuple[int, int]
    ) -> List[int]:
        """
        生成边界框标注
        
        Args:
            image_size: 图片尺寸
            target_size: 目标尺寸
            position: 目标位置
            
        Returns:
            List[int]: 边界框 [x, y, width, height]
        """
        try:
            x, y = position
            width, height = target_size
            
            # 确保边界框在图片范围内
            x = max(0, x)
            y = max(0, y)
            width = min(width, image_size[0] - x)
            height = min(height, image_size[1] - y)
            
            return [x, y, width, height]
            
        except Exception as e:
            logger.error(f"生成边界框标注失败: {str(e)}")
            return [0, 0, 0, 0]
    
    def generate_segmentation_annotation(
        self,
        image_size: Tuple[int, int],
        target_size: Tuple[int, int],
        position: Tuple[int, int]
    ) -> List[List[int]]:
        """
        生成分割标注（简化为矩形）
        
        Args:
            image_size: 图片尺寸
            target_size: 目标尺寸
            position: 目标位置
            
        Returns:
            List[List[int]]: 分割多边形
        """
        try:
            x, y = position
            width, height = target_size
            
            # 确保坐标在图片范围内
            x = max(0, x)
            y = max(0, y)
            x2 = min(x + width, image_size[0])
            y2 = min(y + height, image_size[1])
            
            # 生成矩形分割
            segmentation = [[
                x, y,      # 左上
                x2, y,     # 右上
                x2, y2,    # 右下
                x, y2      # 左下
            ]]
            
            return segmentation
            
        except Exception as e:
            logger.error(f"生成分割标注失败: {str(e)}")
            return [[]]
    
    def get_last_composition_info(self) -> Dict[str, Any]:
        """
        获取最后一次合成的信息
        
        Returns:
            Dict[str, Any]: 合成信息
        """
        return self.last_composition_info.copy()


# 全局实例
_simple_compositor = None


def get_simple_compositor() -> SimpleCompositor:
    """获取简化合成器实例"""
    global _simple_compositor
    if _simple_compositor is None:
        _simple_compositor = SimpleCompositor()
    return _simple_compositor
