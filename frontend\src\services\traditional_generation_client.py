"""
传统图像生成客户端
"""

import requests
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class TraditionalGenerationClient:
    """传统图像生成客户端"""
    
    def __init__(self, api_base: str = "http://localhost:8000/api/v1"):
        self.api_base = api_base
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送API请求"""
        url = f"{self.api_base}{endpoint}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {method} {url}, 错误: {str(e)}")
            raise Exception(f"API请求失败: {str(e)}")
    
    def generate_images(
        self,
        military_target: str,
        weather: str,
        scene: str,
        num_images: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成传统合成图像
        
        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            num_images: 生成图像数量
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            data = {
                "military_target": military_target,
                "weather": weather,
                "scene": scene,
                "num_images": num_images,
                **kwargs
            }
            
            return self._make_request('POST', '/traditional/generate', json=data)
            
        except Exception as e:
            logger.error(f"传统图像生成失败: {str(e)}")
            return {
                "success": False,
                "message": f"传统图像生成失败: {str(e)}"
            }
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取传统生成服务状态"""
        try:
            return self._make_request('GET', '/traditional/status')
        except Exception as e:
            logger.error(f"获取传统生成服务状态失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取服务状态失败: {str(e)}",
                "data": {
                    "service_status": "error",
                    "is_initialized": False
                }
            }
    
    def get_generation_config(self) -> Dict[str, Any]:
        """获取传统生成配置选项"""
        try:
            return self._make_request('GET', '/traditional/config')
        except Exception as e:
            logger.error(f"获取传统生成配置失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取配置失败: {str(e)}",
                "data": {
                    "military_targets": ["坦克", "战机", "舰艇"],
                    "weather_conditions": ["晴天", "雨天", "雪天", "大雾", "夜间"],
                    "scenes": ["城市", "岛屿", "乡村", "沙漠", "海洋"],
                    "matting_methods": ["grabcut", "watershed", "rembg", "threshold"],
                    "blend_modes": ["normal", "multiply", "screen", "overlay", "soft_light", "hard_light"]
                }
            }
    
    def initialize_service(self) -> Dict[str, Any]:
        """初始化传统生成服务"""
        try:
            return self._make_request('POST', '/traditional/initialize')
        except Exception as e:
            logger.error(f"初始化传统生成服务失败: {str(e)}")
            return {
                "success": False,
                "message": f"初始化服务失败: {str(e)}"
            }
    
    def get_available_assets(self) -> Dict[str, Any]:
        """获取可用的素材资源"""
        try:
            return self._make_request('GET', '/traditional/available-assets')
        except Exception as e:
            logger.error(f"获取可用素材失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取素材失败: {str(e)}",
                "data": {
                    "targets": {},
                    "backgrounds": {}
                }
            }
    
    def close(self):
        """关闭客户端连接"""
        if self.session:
            self.session.close()
            logger.info("传统生成客户端连接已关闭")
