"""
简化的背景图片选择器
专注于从pic_resource目录中选择合适的背景图片
"""
import os
import random
import logging
from typing import Dict, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class SimpleBackgroundSelector:
    """简化的背景图片选择器"""
    
    def __init__(self, resource_dir: str = None):
        """
        初始化背景选择器
        
        Args:
            resource_dir: 图片资源目录路径
        """
        if resource_dir is None:
            # 默认使用pic_resource目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
            resource_dir = os.path.join(project_root, "pic_resource")
        
        self.resource_dir = Path(resource_dir)
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.webp', '.tiff'}
        
        # 简化的天气条件映射
        self.weather_mapping = {
            "晴天": "day",
            "白天": "day",
            "雨天": "rainy",
            "雪天": "snowy", 
            "大雾": "foggy",
            "夜间": "night"
        }
        
        # 简化的场景环境映射
        self.scene_mapping = {
            "城市": "city",
            "岛屿": "island", 
            "乡村": "rural"
        }
        
        # 图片缓存
        self._image_cache = {}
        self._cache_valid = False
        
        logger.info(f"简化背景选择器初始化完成，资源目录: {self.resource_dir}")
    
    def initialize(self) -> bool:
        """
        初始化选择器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            if not self.resource_dir.exists():
                logger.error(f"资源目录不存在: {self.resource_dir}")
                return False
            
            # 扫描图片
            self._scan_images()
            self._cache_valid = True
            
            logger.info("简化背景选择器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"简化背景选择器初始化失败: {str(e)}")
            return False
    
    def _scan_images(self):
        """扫描图片文件"""
        self._image_cache.clear()
        
        try:
            for weather_dir in self.resource_dir.iterdir():
                if not weather_dir.is_dir():
                    continue
                    
                weather_key = weather_dir.name
                self._image_cache[weather_key] = {}
                
                for scene_dir in weather_dir.iterdir():
                    if not scene_dir.is_dir():
                        continue
                        
                    scene_key = scene_dir.name
                    images = []
                    
                    # 扫描图片文件
                    for img_file in scene_dir.iterdir():
                        if (img_file.is_file() and 
                            img_file.suffix.lower() in self.supported_formats):
                            images.append(str(img_file))
                    
                    if images:
                        self._image_cache[weather_key][scene_key] = images
                        logger.debug(f"发现 {len(images)} 张图片: {weather_key}/{scene_key}")
            
            total_images = sum(
                len(scenes.get(scene, [])) 
                for scenes in self._image_cache.values() 
                for scene in scenes
            )
            logger.info(f"图片扫描完成，共发现 {total_images} 张图片")
            
        except Exception as e:
            logger.error(f"扫描图片失败: {str(e)}")
    
    def get_background_image(self, weather: str, scene: str) -> Optional[str]:
        """
        获取背景图片
        
        Args:
            weather: 天气条件
            scene: 场景环境
            
        Returns:
            Optional[str]: 图片文件路径，如果没有找到则返回None
        """
        try:
            # 映射中文到英文目录名
            weather_en = self.weather_mapping.get(weather)
            scene_en = self.scene_mapping.get(scene)
            
            if not weather_en or not scene_en:
                logger.warning(f"无法映射天气或场景: {weather} -> {weather_en}, {scene} -> {scene_en}")
                return None
            
            # 检查缓存是否有效
            if not self._cache_valid:
                self._scan_images()
                self._cache_valid = True
            
            # 获取匹配的图片列表
            images = self._image_cache.get(weather_en, {}).get(scene_en, [])
            
            if not images:
                logger.warning(f"未找到匹配的图片: {weather_en}/{scene_en}")
                # 尝试降级匹配
                return self._fallback_selection(weather_en, scene_en)
            
            # 随机选择一张图片
            selected_image = random.choice(images)
            logger.debug(f"选择背景图片: {selected_image}")
            
            return selected_image
            
        except Exception as e:
            logger.error(f"获取背景图片失败: {str(e)}")
            return None
    
    def _fallback_selection(self, weather_en: str, scene_en: str) -> Optional[str]:
        """
        降级选择策略
        
        Args:
            weather_en: 英文天气条件
            scene_en: 英文场景环境
            
        Returns:
            Optional[str]: 图片路径
        """
        try:
            # 1. 尝试相同天气，不同场景
            if weather_en in self._image_cache:
                for scene, images in self._image_cache[weather_en].items():
                    if images:
                        selected = random.choice(images)
                        logger.info(f"降级选择（相同天气）: {selected}")
                        return selected
            
            # 2. 尝试相同场景，不同天气
            for weather, scenes in self._image_cache.items():
                if scene_en in scenes and scenes[scene_en]:
                    selected = random.choice(scenes[scene_en])
                    logger.info(f"降级选择（相同场景）: {selected}")
                    return selected
            
            # 3. 随机选择任意图片
            for weather, scenes in self._image_cache.items():
                for scene, images in scenes.items():
                    if images:
                        selected = random.choice(images)
                        logger.info(f"降级选择（随机）: {selected}")
                        return selected
            
            logger.error("没有找到任何可用的背景图片")
            return None
            
        except Exception as e:
            logger.error(f"降级选择失败: {str(e)}")
            return None
    
    def get_available_combinations(self) -> Dict[str, List[str]]:
        """
        获取可用的天气和场景组合
        
        Returns:
            Dict[str, List[str]]: 天气条件对应的可用场景列表
        """
        try:
            if not self._cache_valid:
                self._scan_images()
                self._cache_valid = True
            
            result = {}
            for weather_cn, weather_en in self.weather_mapping.items():
                if weather_en in self._image_cache:
                    available_scenes = []
                    for scene_cn, scene_en in self.scene_mapping.items():
                        if (scene_en in self._image_cache[weather_en] and 
                            self._image_cache[weather_en][scene_en]):
                            available_scenes.append(scene_cn)
                    
                    if available_scenes:
                        result[weather_cn] = available_scenes
            
            return result
            
        except Exception as e:
            logger.error(f"获取可用组合失败: {str(e)}")
            return {}
    
    def get_image_count(self, weather: str = None, scene: str = None) -> int:
        """
        获取图片数量
        
        Args:
            weather: 天气条件（可选）
            scene: 场景环境（可选）
            
        Returns:
            int: 图片数量
        """
        try:
            if not self._cache_valid:
                self._scan_images()
                self._cache_valid = True
            
            if weather and scene:
                # 获取特定组合的图片数量
                weather_en = self.weather_mapping.get(weather)
                scene_en = self.scene_mapping.get(scene)
                if weather_en and scene_en:
                    return len(self._image_cache.get(weather_en, {}).get(scene_en, []))
            elif weather:
                # 获取特定天气的图片数量
                weather_en = self.weather_mapping.get(weather)
                if weather_en and weather_en in self._image_cache:
                    return sum(len(images) for images in self._image_cache[weather_en].values())
            else:
                # 获取总图片数量
                return sum(
                    len(images) 
                    for weather_scenes in self._image_cache.values()
                    for images in weather_scenes.values()
                )
            
            return 0
            
        except Exception as e:
            logger.error(f"获取图片数量失败: {str(e)}")
            return 0
    
    def refresh_cache(self):
        """刷新图片缓存"""
        try:
            self._scan_images()
            self._cache_valid = True
            logger.info("背景图片缓存刷新完成")
        except Exception as e:
            logger.error(f"刷新背景图片缓存失败: {str(e)}")
            self._cache_valid = False


# 全局实例
_simple_background_selector = None


def get_simple_background_selector() -> SimpleBackgroundSelector:
    """获取简化背景选择器实例"""
    global _simple_background_selector
    if _simple_background_selector is None:
        _simple_background_selector = SimpleBackgroundSelector()
        _simple_background_selector.initialize()
    return _simple_background_selector
