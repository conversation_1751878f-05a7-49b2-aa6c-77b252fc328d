"""
图片详情显示组件
显示图片的详细信息和预览
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea,
    QGroupBox, QTextEdit, QFrame, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QFont
from styles.dataset_styles import apply_dataset_styles, setup_chinese_font
import json
import logging

# 导入图片加载工作线程
from .image_preview import ImageLoadWorker

logger = logging.getLogger(__name__)

class ImageDetailWidget(QWidget):
    """图片详情显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_image_info = None
        self.image_load_worker = None

        # 设置中文字体
        setup_chinese_font(self)

        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)
        
        # 图片预览
        self.create_image_preview_section(content_layout)
        
        # 基本信息
        self.create_basic_info_section(content_layout)
        
        # 内容描述
        self.create_content_section(content_layout)
        
        # 技术信息
        self.create_technical_info_section(content_layout)
        
        # 生成参数
        self.create_generation_params_section(content_layout)
        
        # 原始JSON
        self.create_raw_json_section(content_layout)
        
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
    
    def create_image_preview_section(self, layout):
        """创建图片预览部分"""
        preview_group = QGroupBox("图片预览")
        preview_layout = QVBoxLayout(preview_group)

        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setMinimumSize(300, 250)
        apply_dataset_styles(self.image_label, "image_preview")
        self.image_label.setText("暂无图片")
        self.image_label.setScaledContents(False)

        # 添加加载状态指示器
        self.loading_label = QLabel("加载中...")
        self.loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.loading_label.setStyleSheet("""
            QLabel {
                color: #0d6efd;
                font-size: 12px;
                background-color: transparent;
                border: none;
            }
        """)
        self.loading_label.setVisible(False)

        preview_layout.addWidget(self.image_label)
        preview_layout.addWidget(self.loading_label)
        layout.addWidget(preview_group)
    
    def create_basic_info_section(self, layout):
        """创建基本信息部分"""
        basic_group = QGroupBox("基本信息")
        basic_layout = QVBoxLayout(basic_group)
        
        self.basic_info_text = QTextEdit()
        self.basic_info_text.setMaximumHeight(120)
        self.basic_info_text.setReadOnly(True)
        apply_dataset_styles(self.basic_info_text, "detail_text_edit")
        
        basic_layout.addWidget(self.basic_info_text)
        layout.addWidget(basic_group)
    
    def create_content_section(self, layout):
        """创建内容描述部分"""
        content_group = QGroupBox("内容描述")
        content_layout = QVBoxLayout(content_group)

        self.content_text = QTextEdit()
        self.content_text.setMaximumHeight(100)
        self.content_text.setReadOnly(True)
        apply_dataset_styles(self.content_text, "detail_text_edit")

        content_layout.addWidget(self.content_text)
        layout.addWidget(content_group)
    
    def create_technical_info_section(self, layout):
        """创建技术信息部分"""
        tech_group = QGroupBox("技术信息")
        tech_layout = QVBoxLayout(tech_group)

        self.tech_info_text = QTextEdit()
        self.tech_info_text.setMaximumHeight(80)
        self.tech_info_text.setReadOnly(True)
        apply_dataset_styles(self.tech_info_text, "detail_text_edit")

        tech_layout.addWidget(self.tech_info_text)
        layout.addWidget(tech_group)
    
    def create_generation_params_section(self, layout):
        """创建生成参数部分"""
        gen_group = QGroupBox("生成参数")
        gen_layout = QVBoxLayout(gen_group)

        self.generation_text = QTextEdit()
        self.generation_text.setMaximumHeight(120)
        self.generation_text.setReadOnly(True)
        apply_dataset_styles(self.generation_text, "detail_text_edit")

        gen_layout.addWidget(self.generation_text)
        layout.addWidget(gen_group)
    
    def create_raw_json_section(self, layout):
        """创建原始JSON部分"""
        json_group = QGroupBox("原始JSON数据")
        json_layout = QVBoxLayout(json_group)

        self.json_text = QTextEdit()
        self.json_text.setMaximumHeight(150)
        self.json_text.setReadOnly(True)
        apply_dataset_styles(self.json_text, "detail_text_edit")

        json_layout.addWidget(self.json_text)
        layout.addWidget(json_group)
    
    def update_image_info(self, image_info):
        """更新图片信息显示"""
        self.current_image_info = image_info
        
        if not image_info:
            self.clear_display()
            return
        
        # 更新基本信息
        self.update_basic_info(image_info)
        
        # 更新内容描述
        self.update_content_info(image_info)
        
        # 更新技术信息
        self.update_technical_info(image_info)
        
        # 更新生成参数
        self.update_generation_params(image_info)
        
        # 更新原始JSON
        self.update_raw_json(image_info)
        
        # 更新图片预览（如果有路径）
        self.update_image_preview(image_info)
    
    def update_basic_info(self, image_info):
        """更新基本信息"""
        filename = image_info.get("filename", "未知")
        file_size = image_info.get("file_size", 0)
        size_mb = round(file_size / (1024 * 1024), 2) if file_size > 0 else 0
        added_at = image_info.get("added_at", "")[:19] if image_info.get("added_at") else "未知"
        
        basic_text = f"文件名: {filename}\n"
        basic_text += f"文件大小: {size_mb} MB\n"
        basic_text += f"添加时间: {added_at}"
        
        self.basic_info_text.setPlainText(basic_text)
    
    def update_content_info(self, image_info):
        """更新内容描述"""
        source = image_info.get("source", "")

        if source == "annotation":
            # 标注图片的特殊处理
            content_text = f"类型: 标注图片\n"

            # 从原始图片的元数据中获取信息
            metadata = image_info.get("metadata", {})
            content_text += f"军事目标: {metadata.get('military_target', '无')}\n"
            content_text += f"天气: {metadata.get('weather', '无')}\n"
            content_text += f"场景: {metadata.get('scene', '无')}\n"

            # 显示标注信息
            annotation_data = image_info.get('annotation_data', {})
            if annotation_data:
                # 检查是否是单个标注对象还是COCO格式
                if 'annotations' in annotation_data:
                    # COCO格式
                    annotations = annotation_data.get('annotations', [])
                    content_text += f"检测到目标: {len(annotations)} 个"

                    # 显示第一个标注的详细信息
                    if annotations:
                        first_annotation = annotations[0]
                        bbox = first_annotation.get('bbox', [])
                        if len(bbox) >= 4:
                            content_text += f"\n主要目标位置: ({bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f})"
                else:
                    # 单个标注对象格式
                    content_text += f"检测到目标: 1 个"
                    class_name = annotation_data.get('class_name', '未知')
                    confidence = annotation_data.get('confidence', 0)
                    bbox = annotation_data.get('bbox', [])

                    content_text += f"\n目标类别: {class_name}"
                    content_text += f"\n置信度: {confidence:.2f}"
                    if len(bbox) >= 4:
                        content_text += f"\n目标位置: ({bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f})"
            else:
                content_text += "检测到目标: 0 个"
        else:
            # 常规图片的处理
            content = image_info.get("content", {})

            content_text = f"描述: {content.get('description', '无')}\n"
            content_text += f"军事目标: {content.get('military_target', '无')}\n"
            content_text += f"天气: {content.get('weather', '无')}\n"
            content_text += f"场景: {content.get('scene', '无')}"

            tags = content.get("tags", [])
            if tags:
                content_text += f"\n标签: {', '.join(tags)}"

        self.content_text.setPlainText(content_text)
    
    def update_technical_info(self, image_info):
        """更新技术信息"""
        image_info_data = image_info.get("image_info", {})
        
        tech_text = f"尺寸: {image_info_data.get('width', 0)} x {image_info_data.get('height', 0)}\n"
        tech_text += f"格式: {image_info_data.get('format', '未知')}\n"
        tech_text += f"颜色模式: {image_info_data.get('mode', '未知')}\n"
        tech_text += f"通道数: {image_info_data.get('channels', 0)}"
        
        self.tech_info_text.setPlainText(tech_text)
    
    def update_generation_params(self, image_info):
        """更新生成参数"""
        gen_params = image_info.get("generation_params", {})
        comp_params = image_info.get("composition_params", {})
        metadata = image_info.get("metadata", {})
        
        source = metadata.get("source", "未知")
        
        if source == "ai_generation":
            gen_text = f"生成方式: AI生成\n"
            gen_text += f"模型: {gen_params.get('model', '未知')}\n"
            gen_text += f"提示词: {gen_params.get('prompt', '无')[:50]}...\n" if gen_params.get('prompt') else "提示词: 无\n"
            gen_text += f"步数: {gen_params.get('steps', '未知')}\n"
            gen_text += f"CFG Scale: {gen_params.get('cfg_scale', '未知')}"
        elif source == "traditional_generation":
            gen_text = f"生成方式: 传统合成\n"
            gen_text += f"目标载具: {comp_params.get('target_vehicle', '未知')}\n"
            gen_text += f"背景场景: {comp_params.get('background_scene', '未知')}\n"
            gen_text += f"抠图方法: {comp_params.get('matting_method', '未知')}\n"
            gen_text += f"混合模式: {comp_params.get('blend_mode', '未知')}"
        elif source == "annotation":
            gen_text = f"生成方式: 标注图片\n"
            gen_text += f"原始UUID: {image_info.get('original_uuid', '未知')}\n"
            gen_text += f"原始索引: {image_info.get('original_index', '未知')}\n"
            gen_text += f"原始文件: {image_info.get('original_filename', '未知')}\n"

            # 显示标注信息
            annotation_data = image_info.get('annotation_data', {})
            if annotation_data:
                # 检查是否是单个标注对象还是COCO格式
                if 'annotations' in annotation_data:
                    # COCO格式
                    annotations = annotation_data.get('annotations', [])
                    gen_text += f"标注数量: {len(annotations)} 个"
                else:
                    # 单个标注对象格式
                    gen_text += f"标注数量: 1 个\n"
                    gen_text += f"检测方法: {annotation_data.get('generation_method', '未知')}"
            else:
                gen_text += "标注数量: 0 个"
        else:
            gen_text = f"生成方式: 手动上传\n"
            gen_text += f"原始文件名: {metadata.get('original_filename', '未知')}\n"
            gen_text += f"内容类型: {metadata.get('content_type', '未知')}"
        
        self.generation_text.setPlainText(gen_text)
    
    def update_raw_json(self, image_info):
        """更新原始JSON显示"""
        try:
            json_text = json.dumps(image_info, indent=2, ensure_ascii=False)
            self.json_text.setPlainText(json_text)
        except Exception as e:
            self.json_text.setPlainText(f"JSON解析错误: {str(e)}")
    
    def update_image_preview(self, image_info):
        """更新图片预览"""
        # 调试信息已移除

        if not image_info:
            self._show_preview_placeholder()
            return

        # 尝试多种可能的ID字段名（用于图片管理系统）
        image_id = image_info.get('id') or image_info.get('image_id') or image_info.get('_id')

        # 如果还是没有ID，尝试从嵌套结构中获取
        if not image_id:
            # 检查是否有嵌套的image_info结构
            nested_info = image_info.get('image_info', {})
            if nested_info:
                image_id = nested_info.get('id') or nested_info.get('image_id')

        # 如果有ID，使用API加载（图片管理系统）
        if image_id:
            # 显示加载状态
            self._show_loading_state()
            # 启动图片加载线程
            self._load_preview_image_by_id(image_id)
            return

        # 如果没有ID，尝试通过文件路径加载（数据集管理系统）
        image_path = image_info.get('path')
        if image_path:
            # 显示加载状态
            self._show_loading_state()
            # 直接从文件路径加载图片
            self._load_preview_image_by_path(image_path)
            return

        # 如果既没有ID也没有路径，显示错误
        available_keys = ', '.join(image_info.keys())
        self._show_preview_error(f"无法加载图片\n缺少ID或路径字段\n可用字段: {available_keys}")

    def _show_loading_state(self):
        """显示加载状态"""
        self.image_label.setText("加载中...")
        self.image_label.setStyleSheet("""
            QLabel {
                border: 2px solid #0d6efd;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: #0d6efd;
                font-size: 12px;
                padding: 20px;
            }
        """)
        self.loading_label.setVisible(True)

    def _show_preview_error(self, error_message: str):
        """显示预览错误"""
        self.image_label.setText(f"加载失败\n{error_message}")
        self.image_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #dc3545;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: #dc3545;
                font-size: 12px;
                padding: 20px;
            }
        """)
        self.loading_label.setVisible(False)

    def _show_preview_placeholder(self):
        """显示预览占位符"""
        self.image_label.setText("暂无图片")
        self.image_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #ced4da;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: #6c757d;
                font-size: 12px;
                padding: 20px;
            }
        """)
        self.loading_label.setVisible(False)

    def _load_preview_image_by_id(self, image_id: int):
        """通过ID加载预览图片（图片管理系统）"""
        # 停止之前的加载任务
        if self.image_load_worker and self.image_load_worker.isRunning():
            self.image_load_worker.terminate()
            self.image_load_worker.wait()

        # 构建图片URL
        image_url = f"http://localhost:8000/api/v1/images/{image_id}/file"

        # 创建图片加载工作线程
        self.image_load_worker = ImageLoadWorker(image_id, image_url)
        self.image_load_worker.image_loaded.connect(self._on_preview_image_loaded)
        self.image_load_worker.load_failed.connect(self._on_preview_load_failed)
        self.image_load_worker.start()

    def _load_preview_image_by_path(self, image_path: str):
        """通过文件路径加载预览图片（数据集管理系统）"""
        try:
            import os

            # 调试信息已移除

            # 简化路径处理逻辑
            possible_paths = []

            # 获取项目根目录（相对于当前工作目录）
            current_dir = os.getcwd()
            if current_dir.endswith('frontend'):
                # 如果当前在frontend目录，需要回到项目根目录
                project_root = os.path.dirname(current_dir)
            else:
                # 如果已经在项目根目录
                project_root = current_dir

            # 1. 使用项目根目录构建绝对路径
            if image_path.startswith('backend'):
                # 路径已经包含backend前缀，直接使用项目根目录拼接
                absolute_path = os.path.join(project_root, image_path)
                possible_paths.append(absolute_path)
                # 标准化版本
                normalized = absolute_path.replace('\\', '/')
                if normalized != absolute_path:
                    possible_paths.append(normalized)
            else:
                # 2. 如果不以backend开头，尝试添加backend前缀
                backend_path = os.path.join(project_root, 'backend', image_path)
                possible_paths.append(backend_path)
                # 标准化版本
                backend_normalized = backend_path.replace('\\', '/')
                if backend_normalized != backend_path:
                    possible_paths.append(backend_normalized)

                # 3. 特殊处理：如果路径以data开头，说明是传统图片数据集的路径
                if image_path.startswith('data'):
                    backend_data_path = os.path.join(project_root, 'backend', image_path)
                    possible_paths.append(backend_data_path)
                    # 标准化版本
                    backend_data_normalized = backend_data_path.replace('\\', '/')
                    if backend_data_normalized != backend_data_path:
                        possible_paths.append(backend_data_normalized)

            # 4. 也尝试相对路径（为了兼容性）
            if image_path.startswith('backend'):
                possible_paths.append(image_path)
                normalized = image_path.replace('\\', '/')
                if normalized != image_path:
                    possible_paths.append(normalized)

            # 查找有效路径
            actual_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    actual_path = path
                    break

            if not actual_path:
                # 调试信息：写入文件
                debug_info = f"文件不存在: {image_path}\n"
                debug_info += f"当前工作目录: {os.getcwd()}\n"
                debug_info += "尝试的路径:\n"
                for i, path in enumerate(possible_paths, 1):
                    exists = os.path.exists(path)
                    debug_info += f"  {i}. {path} (存在: {exists})\n"

                # 调试信息已移除

                self._on_preview_load_failed(0, debug_info)
                return

            # 直接从文件路径加载图片
            pixmap = QPixmap(actual_path)
            if not pixmap.isNull():
                # 模拟异步加载完成的信号
                self._on_preview_image_loaded(0, pixmap)  # 使用0作为占位符ID
            else:
                # QPixmap加载失败，尝试使用PIL转换
                try:
                    from PIL import Image
                    import io

                    # 使用PIL打开图片
                    with Image.open(actual_path) as pil_image:
                        # 确保图片是RGB模式
                        if pil_image.mode != 'RGB':
                            pil_image = pil_image.convert('RGB')

                        # 将PIL图片转换为字节流
                        byte_array = io.BytesIO()
                        pil_image.save(byte_array, format='PNG')
                        byte_array.seek(0)

                        # 从字节流创建QPixmap
                        pixmap = QPixmap()
                        if pixmap.loadFromData(byte_array.getvalue()):
                            self._on_preview_image_loaded(0, pixmap)
                        else:
                            self._on_preview_load_failed(0, "图片格式转换失败")

                except Exception as pil_error:
                    self._on_preview_load_failed(0, f"图片加载失败: {str(pil_error)}")
        except Exception as e:
            self._on_preview_load_failed(0, f"加载图片异常: {str(e)}")

    def _on_preview_image_loaded(self, image_id: int, pixmap: QPixmap):
        """预览图片加载完成"""
        self.loading_label.setVisible(False)

        # 获取预览区域的实际大小
        label_size = self.image_label.size()
        # 确保有合理的最小尺寸
        if label_size.width() < 100 or label_size.height() < 100:
            label_size = self.image_label.sizeHint()
            if label_size.width() < 300:
                label_size.setWidth(300)
            if label_size.height() < 250:
                label_size.setHeight(250)

        # 缩放图像以适应预览区域，保持宽高比
        scaled_pixmap = pixmap.scaled(
            label_size,
            Qt.AspectRatioMode.KeepAspectRatio,
            Qt.TransformationMode.SmoothTransformation
        )

        self.image_label.setPixmap(scaled_pixmap)
        self.image_label.setText("")
        self.image_label.setStyleSheet("""
            QLabel {
                border: 2px solid #0d6efd;
                border-radius: 8px;
                background-color: #f8f9fa;
                padding: 5px;
            }
        """)

    def _on_preview_load_failed(self, image_id: int, error_message: str):
        """预览图片加载失败"""
        self._show_preview_error(error_message)
    
    def clear_display(self):
        """清空显示"""
        # 停止图片加载任务
        if self.image_load_worker and self.image_load_worker.isRunning():
            self.image_load_worker.terminate()
            self.image_load_worker.wait()

        # 清空图片预览
        self._show_preview_placeholder()

        # 清空其他信息
        self.basic_info_text.clear()
        self.content_text.clear()
        self.tech_info_text.clear()
        self.generation_text.clear()
        self.json_text.clear()
        self.current_image_info = None
