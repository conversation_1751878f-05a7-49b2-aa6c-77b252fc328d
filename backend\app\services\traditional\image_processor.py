"""
图层混合和色彩匹配处理器
"""
import cv2
import numpy as np
from PIL import Image
from typing import Optional, Tuple, Dict, Any, List
import logging
from skimage import exposure, color
from scipy.stats import pearsonr
import time

from .core_types import (
    ImageData, BlendConfig, BlendMode, ProcessingResult, BlendProcessor
)

logger = logging.getLogger(__name__)


class AdvancedBlendProcessor(BlendProcessor):
    """高级图层混合处理器"""

    def __init__(self, config: BlendConfig):
        super().__init__(config)

    def blend_layers(self, background: ImageData, foreground: ImageData, mask: np.ndarray) -> ProcessingResult:
        """混合图层"""
        start_time = time.time()

        try:
            # 转换为numpy数组
            bg_array = self._to_numpy(background.image)
            fg_array = self._to_numpy(foreground.image)

            # 确保尺寸匹配
            bg_array, fg_array, mask = self._resize_to_match(bg_array, fg_array, mask)

            # 色彩匹配
            if self.config.color_match:
                fg_array = self.match_colors(fg_array, bg_array, mask)

            # 光照调整
            if self.config.lighting_adjust:
                fg_array = self._adjust_lighting(fg_array, bg_array, mask)

            # 生成阴影
            shadow_mask = None
            if self.config.shadow_generate:
                shadow_mask = self.generate_shadow(fg_array, mask, (0.5, -0.5))

            # 执行混合
            result_array = self._blend_with_mode(bg_array, fg_array, mask, self.config.mode)

            # 添加阴影
            if shadow_mask is not None:
                result_array = self._add_shadow(result_array, shadow_mask)

            # 应用透明度
            if self.config.opacity < 1.0:
                result_array = self._apply_opacity(bg_array, result_array, mask, self.config.opacity)

            processing_time = time.time() - start_time

            return ProcessingResult(
                success=True,
                image=ImageData(image=result_array),
                processing_time=processing_time,
                metadata={
                    'blend_mode': self.config.mode.value,
                    'opacity': self.config.opacity,
                    'color_matched': self.config.color_match,
                    'lighting_adjusted': self.config.lighting_adjust,
                    'shadow_added': self.config.shadow_generate
                }
            )

        except Exception as e:
            logger.error(f"图层混合失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def match_colors(self, source: np.ndarray, target: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """色彩匹配"""
        try:
            # 转换到LAB色彩空间进行匹配
            source_lab = cv2.cvtColor(source, cv2.COLOR_RGB2LAB).astype(np.float32)
            target_lab = cv2.cvtColor(target, cv2.COLOR_RGB2LAB).astype(np.float32)

            # 只在蒙版区域进行匹配
            mask_3d = np.stack([mask/255.0] * 3, axis=2)

            # 计算源图像和目标图像在蒙版区域的统计信息
            for channel in range(3):
                source_channel = source_lab[:, :, channel]
                target_channel = target_lab[:, :, channel]

                # 计算蒙版区域的均值和标准差
                source_masked = source_channel[mask > 0]
                target_masked = target_channel[mask > 0]

                if len(source_masked) > 0 and len(target_masked) > 0:
                    source_mean = np.mean(source_masked)
                    source_std = np.std(source_masked)
                    target_mean = np.mean(target_masked)
                    target_std = np.std(target_masked)

                    # 避免除零
                    if source_std > 0:
                        # 标准化并重新缩放
                        source_channel = (source_channel - source_mean) / source_std
                        source_channel = source_channel * target_std + target_mean
                        source_lab[:, :, channel] = source_channel

            # 转换回RGB
            result = cv2.cvtColor(source_lab.astype(np.uint8), cv2.COLOR_LAB2RGB)
            return result

        except Exception as e:
            logger.warning(f"色彩匹配失败: {str(e)}, 返回原图")
            return source

    def generate_shadow(self, foreground: np.ndarray, mask: np.ndarray, light_direction: Tuple[float, float]) -> np.ndarray:
        """生成阴影"""
        try:
            # 创建阴影蒙版
            shadow_mask = mask.copy()

            # 根据光照方向偏移阴影
            dx = int(light_direction[0] * 20)  # 阴影偏移距离
            dy = int(light_direction[1] * 20)

            # 使用仿射变换创建阴影偏移
            height, width = shadow_mask.shape
            M = np.float32([[1, 0, dx], [0, 1, dy]])
            shadow_mask = cv2.warpAffine(shadow_mask, M, (width, height))

            # 模糊阴影边缘
            shadow_mask = cv2.GaussianBlur(shadow_mask, (self.config.shadow_blur*2+1, self.config.shadow_blur*2+1), 0)

            # 应用阴影透明度
            shadow_mask = (shadow_mask * self.config.shadow_opacity).astype(np.uint8)

            # 确保阴影不与原对象重叠
            shadow_mask = np.where(mask > 0, 0, shadow_mask)

            return shadow_mask

        except Exception as e:
            logger.warning(f"阴影生成失败: {str(e)}")
            return np.zeros_like(mask)

    def _to_numpy(self, image: Any) -> np.ndarray:
        """转换为numpy数组"""
        if isinstance(image, Image.Image):
            return np.array(image)
        elif isinstance(image, np.ndarray):
            return image
        else:
            raise ValueError(f"不支持的图像类型: {type(image)}")

    def _resize_to_match(self, bg: np.ndarray, fg: np.ndarray, mask: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """调整尺寸匹配"""
        target_height, target_width = bg.shape[:2]

        # 调整前景图像尺寸
        if fg.shape[:2] != (target_height, target_width):
            fg = cv2.resize(fg, (target_width, target_height))

        # 调整蒙版尺寸
        if mask.shape != (target_height, target_width):
            mask = cv2.resize(mask, (target_width, target_height))

        return bg, fg, mask

    def _adjust_lighting(self, foreground: np.ndarray, background: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """调整光照"""
        try:
            # 计算背景的平均亮度
            bg_gray = cv2.cvtColor(background, cv2.COLOR_RGB2GRAY)
            bg_brightness = np.mean(bg_gray[mask > 0]) if np.any(mask > 0) else np.mean(bg_gray)

            # 计算前景的平均亮度
            fg_gray = cv2.cvtColor(foreground, cv2.COLOR_RGB2GRAY)
            fg_brightness = np.mean(fg_gray[mask > 0]) if np.any(mask > 0) else np.mean(fg_gray)

            # 计算亮度调整因子
            if fg_brightness > 0:
                brightness_factor = bg_brightness / fg_brightness
                brightness_factor = np.clip(brightness_factor, 0.5, 2.0)  # 限制调整范围

                # 应用亮度调整
                adjusted = foreground.astype(np.float32) * brightness_factor
                adjusted = np.clip(adjusted, 0, 255).astype(np.uint8)

                return adjusted

            return foreground

        except Exception as e:
            logger.warning(f"光照调整失败: {str(e)}")
            return foreground

    def _blend_with_mode(self, background: np.ndarray, foreground: np.ndarray, mask: np.ndarray, mode: BlendMode) -> np.ndarray:
        """根据混合模式混合图像"""
        # 归一化蒙版
        mask_norm = mask.astype(np.float32) / 255.0
        mask_3d = np.stack([mask_norm] * 3, axis=2)

        # 转换为浮点数进行计算
        bg = background.astype(np.float32) / 255.0
        fg = foreground.astype(np.float32) / 255.0

        if mode == BlendMode.NORMAL:
            result = bg * (1 - mask_3d) + fg * mask_3d
        elif mode == BlendMode.MULTIPLY:
            blended = bg * fg
            result = bg * (1 - mask_3d) + blended * mask_3d
        elif mode == BlendMode.SCREEN:
            blended = 1 - (1 - bg) * (1 - fg)
            result = bg * (1 - mask_3d) + blended * mask_3d
        elif mode == BlendMode.OVERLAY:
            blended = np.where(bg < 0.5, 2 * bg * fg, 1 - 2 * (1 - bg) * (1 - fg))
            result = bg * (1 - mask_3d) + blended * mask_3d
        elif mode == BlendMode.SOFT_LIGHT:
            blended = np.where(fg < 0.5,
                             bg - (1 - 2 * fg) * bg * (1 - bg),
                             bg + (2 * fg - 1) * (np.sqrt(bg) - bg))
            result = bg * (1 - mask_3d) + blended * mask_3d
        elif mode == BlendMode.HARD_LIGHT:
            blended = np.where(fg < 0.5, 2 * bg * fg, 1 - 2 * (1 - bg) * (1 - fg))
            result = bg * (1 - mask_3d) + blended * mask_3d
        elif mode == BlendMode.COLOR_DODGE:
            blended = np.where(fg >= 1.0, 1.0, np.minimum(1.0, bg / (1 - fg + 1e-7)))
            result = bg * (1 - mask_3d) + blended * mask_3d
        elif mode == BlendMode.COLOR_BURN:
            blended = np.where(fg <= 0.0, 0.0, np.maximum(0.0, 1 - (1 - bg) / (fg + 1e-7)))
            result = bg * (1 - mask_3d) + blended * mask_3d
        elif mode == BlendMode.DARKEN:
            blended = np.minimum(bg, fg)
            result = bg * (1 - mask_3d) + blended * mask_3d
        elif mode == BlendMode.LIGHTEN:
            blended = np.maximum(bg, fg)
            result = bg * (1 - mask_3d) + blended * mask_3d
        else:
            result = bg * (1 - mask_3d) + fg * mask_3d  # 默认正常混合

        # 转换回uint8
        result = np.clip(result * 255, 0, 255).astype(np.uint8)
        return result

    def _add_shadow(self, image: np.ndarray, shadow_mask: np.ndarray) -> np.ndarray:
        """添加阴影"""
        try:
            # 创建阴影颜色（深灰色）
            shadow_color = np.array([50, 50, 50], dtype=np.uint8)

            # 将阴影蒙版扩展为3通道
            shadow_3d = np.stack([shadow_mask] * 3, axis=2).astype(np.float32) / 255.0

            # 混合阴影
            image_float = image.astype(np.float32)
            shadow_float = shadow_color.astype(np.float32)

            # 使用multiply混合模式添加阴影
            result = image_float * (1 - shadow_3d) + (image_float * shadow_float / 255.0) * shadow_3d

            return np.clip(result, 0, 255).astype(np.uint8)

        except Exception as e:
            logger.warning(f"添加阴影失败: {str(e)}")
            return image

    def _apply_opacity(self, background: np.ndarray, blended: np.ndarray, mask: np.ndarray, opacity: float) -> np.ndarray:
        """应用透明度"""
        mask_3d = np.stack([mask.astype(np.float32) / 255.0] * 3, axis=2) * opacity

        result = background.astype(np.float32) * (1 - mask_3d) + blended.astype(np.float32) * mask_3d
        return np.clip(result, 0, 255).astype(np.uint8)


class BlendModeFactory:
    """混合模式工厂类"""

    @staticmethod
    def get_available_modes() -> List[BlendMode]:
        """获取可用的混合模式"""
        return list(BlendMode)

    @staticmethod
    def get_mode_description(mode: BlendMode) -> str:
        """获取混合模式描述"""
        descriptions = {
            BlendMode.NORMAL: "正常混合，直接覆盖",
            BlendMode.MULTIPLY: "正片叠底，颜色变暗",
            BlendMode.SCREEN: "滤色，颜色变亮",
            BlendMode.OVERLAY: "叠加，增强对比度",
            BlendMode.SOFT_LIGHT: "柔光，柔和的对比度增强",
            BlendMode.HARD_LIGHT: "强光，强烈的对比度增强",
            BlendMode.COLOR_DODGE: "颜色减淡，提亮效果",
            BlendMode.COLOR_BURN: "颜色加深，加深效果",
            BlendMode.DARKEN: "变暗，保留较暗的颜色",
            BlendMode.LIGHTEN: "变亮，保留较亮的颜色"
        }
        return descriptions.get(mode, "未知模式")

    @staticmethod
    def recommend_mode_for_scene(scene_type: str, weather: str) -> BlendMode:
        """根据场景和天气推荐混合模式"""
        # 根据场景特点推荐合适的混合模式
        if weather in ["night", "foggy"]:
            return BlendMode.MULTIPLY  # 夜间或雾天使用变暗效果
        elif weather in ["sunny", "bright"]:
            return BlendMode.SCREEN  # 明亮天气使用提亮效果
        elif scene_type in ["urban", "complex"]:
            return BlendMode.OVERLAY  # 复杂场景使用叠加增强对比
        else:
            return BlendMode.NORMAL  # 默认使用正常混合