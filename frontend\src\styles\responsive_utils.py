"""
响应式布局工具类
根据屏幕尺寸自动调整UI元素
"""

from PyQt6.QtGui import QGuiApplication
from PyQt6.QtCore import QSize
from .unified_theme import COLORS, FONTS, SPACING, RADIUS

class ResponsiveUtils:
    """响应式布局工具类"""
    
    # 屏幕尺寸断点
    BREAKPOINTS = {
        'xs': 768,    # 超小屏幕
        'sm': 1024,   # 小屏幕
        'md': 1280,   # 中等屏幕
        'lg': 1600,   # 大屏幕
        'xl': 1920,   # 超大屏幕
    }
    
    @staticmethod
    def get_screen_size():
        """获取当前屏幕尺寸"""
        screen = QGuiApplication.primaryScreen()
        if screen:
            geometry = screen.availableGeometry()
            return geometry.width(), geometry.height()
        return 1920, 1080  # 默认尺寸
    
    @staticmethod
    def get_screen_category():
        """
        获取屏幕类别
        
        Returns:
            str: 屏幕类别 ('xs', 'sm', 'md', 'lg', 'xl')
        """
        width, _ = ResponsiveUtils.get_screen_size()
        
        if width <= ResponsiveUtils.BREAKPOINTS['xs']:
            return 'xs'
        elif width <= ResponsiveUtils.BREAKPOINTS['sm']:
            return 'sm'
        elif width <= ResponsiveUtils.BREAKPOINTS['md']:
            return 'md'
        elif width <= ResponsiveUtils.BREAKPOINTS['lg']:
            return 'lg'
        else:
            return 'xl'
    
    @staticmethod
    def get_responsive_font_size(base_size: str = 'base'):
        """
        获取响应式字体大小
        
        Args:
            base_size: 基础字体大小键名
            
        Returns:
            str: 调整后的字体大小
        """
        screen_category = ResponsiveUtils.get_screen_category()
        
        # 字体大小映射
        font_adjustments = {
            'xs': {
                'xs': FONTS['size_xs'],
                'sm': FONTS['size_xs'], 
                'base': FONTS['size_sm'],
                'lg': FONTS['size_base'],
                'xl': FONTS['size_lg'],
                'xxl': FONTS['size_xl'],
            },
            'sm': {
                'xs': FONTS['size_xs'],
                'sm': FONTS['size_sm'],
                'base': FONTS['size_base'],
                'lg': FONTS['size_lg'],
                'xl': FONTS['size_xl'],
                'xxl': FONTS['size_xxl'],
            },
            'md': FONTS,  # 使用默认字体大小
            'lg': FONTS,  # 使用默认字体大小
            'xl': FONTS,  # 使用默认字体大小
        }
        
        category_fonts = font_adjustments.get(screen_category, FONTS)
        return category_fonts.get(f'size_{base_size}', FONTS[f'size_{base_size}'])
    
    @staticmethod
    def get_responsive_spacing(base_spacing: str = 'base'):
        """
        获取响应式间距
        
        Args:
            base_spacing: 基础间距键名
            
        Returns:
            str: 调整后的间距
        """
        screen_category = ResponsiveUtils.get_screen_category()
        
        # 间距调整映射
        spacing_adjustments = {
            'xs': {
                'xs': '2px',
                'sm': '3px',
                'base': '4px',
                'md': '6px',
                'lg': '8px',
                'xl': '10px',
                'xxl': '12px',
                'xxxl': '16px',
            },
            'sm': {
                'xs': '3px',
                'sm': '4px',
                'base': '6px',
                'md': '8px',
                'lg': '10px',
                'xl': '12px',
                'xxl': '16px',
                'xxxl': '20px',
            },
            'md': SPACING,  # 使用默认间距
            'lg': SPACING,  # 使用默认间距
            'xl': SPACING,  # 使用默认间距
        }
        
        category_spacing = spacing_adjustments.get(screen_category, SPACING)
        return category_spacing.get(base_spacing, SPACING[base_spacing])
    
    @staticmethod
    def get_responsive_window_size():
        """
        获取响应式窗口尺寸
        
        Returns:
            tuple: (width, height, min_width, min_height)
        """
        screen_width, screen_height = ResponsiveUtils.get_screen_size()
        screen_category = ResponsiveUtils.get_screen_category()
        
        # 窗口尺寸配置
        window_configs = {
            'xs': {
                'width_ratio': 0.95,
                'height_ratio': 0.85,
                'min_width': 700,
                'min_height': 500,
            },
            'sm': {
                'width_ratio': 0.90,
                'height_ratio': 0.80,
                'min_width': 800,
                'min_height': 550,
            },
            'md': {
                'width_ratio': 0.85,
                'height_ratio': 0.80,
                'min_width': 900,
                'min_height': 600,
            },
            'lg': {
                'width_ratio': 0.80,
                'height_ratio': 0.75,
                'min_width': 1000,
                'min_height': 650,
            },
            'xl': {
                'width_ratio': 0.75,
                'height_ratio': 0.70,
                'min_width': 1200,
                'min_height': 700,
            },
        }
        
        config = window_configs.get(screen_category, window_configs['md'])
        
        width = int(screen_width * config['width_ratio'])
        height = int(screen_height * config['height_ratio'])
        min_width = config['min_width']
        min_height = config['min_height']
        
        # 确保窗口不小于最小尺寸
        width = max(width, min_width)
        height = max(height, min_height)
        
        return width, height, min_width, min_height
    
    @staticmethod
    def get_responsive_preview_size():
        """
        获取响应式预览区域尺寸
        
        Returns:
            QSize: 预览区域尺寸
        """
        screen_category = ResponsiveUtils.get_screen_category()
        
        # 预览区域尺寸配置
        preview_configs = {
            'xs': QSize(180, 150),
            'sm': QSize(220, 180),
            'md': QSize(280, 240),
            'lg': QSize(320, 280),
            'xl': QSize(360, 320),
        }
        
        return preview_configs.get(screen_category, preview_configs['md'])
    
    @staticmethod
    def get_responsive_button_size():
        """
        获取响应式按钮尺寸
        
        Returns:
            dict: 按钮尺寸配置
        """
        screen_category = ResponsiveUtils.get_screen_category()
        
        # 按钮尺寸配置
        button_configs = {
            'xs': {
                'min_width': 50,
                'min_height': 24,
                'padding': f"{ResponsiveUtils.get_responsive_spacing('xs')} {ResponsiveUtils.get_responsive_spacing('sm')}",
            },
            'sm': {
                'min_width': 55,
                'min_height': 26,
                'padding': f"{ResponsiveUtils.get_responsive_spacing('sm')} {ResponsiveUtils.get_responsive_spacing('base')}",
            },
            'md': {
                'min_width': 60,
                'min_height': 28,
                'padding': f"{ResponsiveUtils.get_responsive_spacing('base')} {ResponsiveUtils.get_responsive_spacing('xl')}",
            },
            'lg': {
                'min_width': 65,
                'min_height': 30,
                'padding': f"{ResponsiveUtils.get_responsive_spacing('md')} {ResponsiveUtils.get_responsive_spacing('xl')}",
            },
            'xl': {
                'min_width': 70,
                'min_height': 32,
                'padding': f"{ResponsiveUtils.get_responsive_spacing('lg')} {ResponsiveUtils.get_responsive_spacing('xxl')}",
            },
        }
        
        return button_configs.get(screen_category, button_configs['md'])
    
    @staticmethod
    def get_responsive_grid_columns():
        """
        获取响应式网格列数
        
        Returns:
            int: 网格列数
        """
        screen_category = ResponsiveUtils.get_screen_category()
        
        # 网格列数配置
        grid_configs = {
            'xs': 2,
            'sm': 3,
            'md': 4,
            'lg': 5,
            'xl': 6,
        }
        
        return grid_configs.get(screen_category, 4)
    
    @staticmethod
    def apply_responsive_style(widget, style_type: str = 'default'):
        """
        应用响应式样式到控件
        
        Args:
            widget: Qt控件
            style_type: 样式类型
        """
        screen_category = ResponsiveUtils.get_screen_category()
        
        if style_type == 'button':
            config = ResponsiveUtils.get_responsive_button_size()
            widget.setMinimumSize(config['min_width'], config['min_height'])
        elif style_type == 'preview':
            size = ResponsiveUtils.get_responsive_preview_size()
            widget.setMinimumSize(size)
    
    @staticmethod
    def is_small_screen():
        """判断是否为小屏幕"""
        return ResponsiveUtils.get_screen_category() in ['xs', 'sm']
    
    @staticmethod
    def is_large_screen():
        """判断是否为大屏幕"""
        return ResponsiveUtils.get_screen_category() in ['lg', 'xl']
