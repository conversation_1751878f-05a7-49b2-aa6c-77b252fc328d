Requirement already satisfied: scikit-image in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (0.21.0)
Requirement already satisfied: numpy>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from scikit-image) (1.24.3)
Requirement already satisfied: scipy>=1.8 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from scikit-image) (1.10.1)
Requirement already satisfied: networkx>=2.8 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from scikit-image) (3.0)
Requirement already satisfied: pillow>=9.0.1 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from scikit-image) (10.2.0)
Requirement already satisfied: imageio>=2.27 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from scikit-image) (2.35.1)
Requirement already satisfied: tifffile>=2022.8.12 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from scikit-image) (2023.7.10)
Requirement already satisfied: PyWavelets>=1.1.1 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from scikit-image) (1.4.1)
Requirement already satisfied: packaging>=21 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from scikit-image) (25.0)
Requirement already satisfied: lazy_loader>=0.2 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from scikit-image) (0.4)
