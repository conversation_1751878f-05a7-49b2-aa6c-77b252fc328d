"""
Stable Diffusion模型管理API
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel

from ...services.ai_generation.model_manager import ModelManager
from ...services.ai_generation.sd_weight_loader import SDWeightLoader
from ...core.dependencies import get_model_manager, get_ai_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/sd-models", tags=["SD模型管理"])

# 全局模型管理器实例
# model_manager = None # 不再需要
sd_weight_loader = None

# def get_model_manager() -> ModelManager: # 不再需要，从dependencies导入
#     """获取模型管理器实例"""
#     global model_manager
#     if model_manager is None:
#         model_manager = ModelManager()
#     return model_manager

def get_sd_weight_loader() -> SDWeightLoader:
    """获取SD权重加载器实例"""
    global sd_weight_loader
    if sd_weight_loader is None:
        sd_weight_loader = SDWeightLoader()
    return sd_weight_loader


class AddSafetensorsModelRequest(BaseModel):
    """添加safetensors模型请求"""
    model_key: str
    model_name: str
    safetensors_path: str
    description: Optional[str] = ""


class LoadModelRequest(BaseModel):
    """加载模型请求"""
    model_key: str
    force_reload: Optional[bool] = False


@router.get("/available")
async def get_available_models():
    """获取可用的模型列表"""
    try:
        manager = get_model_manager() # 使用共享的实例
        models = manager.get_available_models()

        return {
            "success": True,
            "message": "获取模型列表成功",
            "data": {"models": models}
        }

    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@router.get("/cached")
async def get_cached_models():
    """获取已缓存的模型列表"""
    try:
        manager = get_model_manager() # 使用共享的实例
        loader = get_sd_weight_loader()

        cached_models = manager.get_cached_models()
        sd_cached_models = loader.get_cached_models()

        return {
            "success": True,
            "message": "获取缓存模型列表成功",
            "data": {
                "cached_models": cached_models,
                "sd_cached_models": sd_cached_models
            }
        }

    except Exception as e:
        logger.error(f"获取缓存模型列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取缓存模型列表失败: {str(e)}")


@router.get("/current")
async def get_current_model():
    """获取当前加载的模型信息"""
    try:
        manager = get_model_manager() # 使用共享的实例
        current_model_key = getattr(manager, 'current_model_key', None)

        if current_model_key:
            # 获取模型信息
            available_models = manager.get_available_models()
            model_info = available_models.get(current_model_key, {})
            return {
                "success": True,
                "message": "获取当前模型信息成功",
                "data": {
                    "current_model_key": current_model_key,
                    "model_info": model_info
                }
            }
        else:
            return {
                "success": True,
                "message": "当前没有加载的模型",
                "data": {"current_model_key": None, "model_info": None}
            }

    except Exception as e:
        logger.error(f"获取当前模型信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取当前模型信息失败: {str(e)}")


@router.post("/load")
async def load_model(request: LoadModelRequest):
    """加载指定模型"""
    try:
        manager = get_model_manager() # 使用共享的实例

        # 检查是否是safetensors模型
        available_models = manager.get_available_models()
        model_info = available_models.get(request.model_key)

        if not model_info:
            raise HTTPException(status_code=404, detail=f"模型不存在: {request.model_key}")

        # 根据模型类型选择加载方式
        if model_info.get("model_type") == "safetensors":
            # 使用safetensors加载器
            safetensors_path = model_info.get("model_id") or model_info.get("model_path")
            if not safetensors_path:
                raise HTTPException(status_code=400, detail="模型路径不存在")

            # 确保路径是绝对路径
            if not os.path.isabs(safetensors_path):
                # 如果是相对路径，相对于项目根目录
                safetensors_path = os.path.abspath(safetensors_path)

            success = manager.load_safetensors_model(
                model_key=request.model_key,
                safetensors_path=str(safetensors_path),
                force_reload=bool(request.force_reload)
            )
        else:
            # 使用标准加载器
            success = manager.load_model(request.model_key, bool(request.force_reload))

        if success:
            return {
                "success": True,
                "message": f"模型加载成功: {request.model_key}",
                "data": {"model_key": request.model_key}
            }
        else:
            raise HTTPException(status_code=500, detail=f"模型加载失败: {request.model_key}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"加载模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"加载模型失败: {str(e)}")


@router.post("/add-safetensors")
async def add_safetensors_model(
    model_key: str = Form(...),
    model_name: str = Form(...),
    safetensors_path: str = Form(...),
    description: str = Form(""),
    update_if_exists: bool = Form(False)
):
    """添加或更新safetensors模型"""
    try:
        logger.info(f"收到添加/更新safetensors模型请求: {model_key}, 文件路径: {safetensors_path}, 更新: {update_if_exists}")
        
        # 基本参数验证
        if not all([model_key, model_name, safetensors_path]):
            raise HTTPException(status_code=400, detail="缺少必填参数: model_key, model_name, safetensors_path")
        
        # 检查文件路径格式
        from pathlib import Path
        try:
            file_path = Path(safetensors_path)
            if not file_path.suffix.lower() == '.safetensors':
                raise HTTPException(status_code=400, detail=f"文件扩展名无效: {file_path.suffix}，需要.safetensors文件")
        except Exception as path_e:
            raise HTTPException(status_code=400, detail=f"文件路径格式无效: {str(path_e)}")
        
        manager = get_model_manager() # 使用共享的实例

        # 检查模型是否已存在
        if manager.get_custom_model(model_key) and not update_if_exists:
            raise HTTPException(status_code=409, detail=f"模型键 '{model_key}' 已存在。如需更新，请设置 update_if_exists=True")

        # 如果是更新，则调用更新方法
        if update_if_exists:
            success, message = manager.update_custom_model(
                model_key=model_key,
                model_name=model_name,
                model_path=safetensors_path,
                description=description or ""
            )
        else:
            # 否则，调用添加方法
            success, message = manager.add_safetensors_model(
                model_key=model_key,
                model_name=model_name,
                safetensors_path=safetensors_path,
                description=description or ""
            )

        if success:
            logger.info(f"safetensors模型操作成功: {model_key}")
            return {
                "success": True,
                "message": message,
                "data": {"model_key": model_key}
            }
        else:
            logger.warning(f"safetensors模型操作失败: {message}")
            raise HTTPException(status_code=400, detail=message)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加safetensors模型异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/validate-safetensors")
async def validate_safetensors_file(file_path: str):
    """验证safetensors文件"""
    try:
        loader = get_sd_weight_loader()

        is_valid, message, file_info = loader.validate_safetensors_file(file_path)

        return {
            "success": is_valid,
            "message": message,
            "data": {"file_info": file_info} if is_valid else {}
        }

    except Exception as e:
        logger.error(f"验证safetensors文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"验证失败: {str(e)}")


@router.post("/upload-safetensors")
async def upload_safetensors_file(
    file: UploadFile = File(...),
    model_key: str = Form(...),
    model_name: str = Form(...),
    description: str = Form("")
):
    """上传safetensors文件"""
    try:
        # 检查文件类型
        if not file.filename.endswith('.safetensors'):
            raise HTTPException(status_code=400, detail="只支持.safetensors文件")

        # 保存上传的文件
        upload_dir = Path("data/uploads/models")
        upload_dir.mkdir(parents=True, exist_ok=True)

        file_path = upload_dir / file.filename
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # 验证文件
        loader = get_sd_weight_loader()
        is_valid, validation_msg, file_info = loader.validate_safetensors_file(str(file_path))

        if not is_valid:
            # 删除无效文件
            file_path.unlink()
            raise HTTPException(status_code=400, detail=f"文件验证失败: {validation_msg}")

        # 添加模型
        manager = get_model_manager() # 使用共享的实例
        success, message = manager.add_safetensors_model_from_file(
            model_key=model_key,
            model_name=model_name,
            safetensors_path=str(file_path),
            description=description
        )

        if success:
            return {
                "success": True,
                "message": f"文件上传并添加成功: {model_name}",
                "data": {
                    "model_key": model_key,
                    "file_path": str(file_path),
                    "file_info": file_info
                }
            }
        else:
            # 删除文件
            file_path.unlink()
            raise HTTPException(status_code=400, detail=message)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传safetensors文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")


@router.delete("/cache/{model_key}")
async def clear_model_cache(model_key: str):
    """清理指定模型的缓存"""
    try:
        loader = get_sd_weight_loader()
        loader.clear_cache(model_key)

        return {
            "success": True,
            "message": f"模型缓存清理成功: {model_key}"
        }

    except Exception as e:
        logger.error(f"清理模型缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理缓存失败: {str(e)}")


@router.delete("/cache")
async def clear_all_cache():
    """清理所有模型缓存"""
    try:
        loader = get_sd_weight_loader()
        loader.clear_cache()

        return {
            "success": True,
            "message": "所有模型缓存清理成功"
        }

    except Exception as e:
        logger.error(f"清理所有缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理缓存失败: {str(e)}")
