"""
数据集管理器 - 文件系统存储方案
实现按时间戳命名的文件夹结构和图片+JSON描述的dataset格式
"""

import os
import json
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import logging
from PIL import Image
from .json_template_manager import JSONTemplateManager

logger = logging.getLogger(__name__)

class DatasetManager:
    """数据集管理器 - 基于文件系统的简单数据管理"""
    
    def __init__(self, base_dir: str = "data/datasets"):
        """
        初始化数据集管理器
        
        Args:
            base_dir: 数据集基础目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"数据集管理器初始化完成，基础目录: {self.base_dir}")
    
    def create_dataset_folder(self, name: Optional[str] = None, naming_format: Optional[str] = "timestamp") -> Tuple[str, Path]:
        """
        创建新的数据集文件夹（支持时间戳和序号命名）

        Args:
            name: 可选的数据集名称前缀
            naming_format: 命名格式 ("timestamp" 或 "sequential")

        Returns:
            Tuple[str, Path]: (文件夹名称, 文件夹路径)
        """
        if naming_format == "sequential":
            folder_name = self._generate_sequential_name(name)
        else:
            # 默认使用时间戳格式
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 生成文件夹名称
            if name:
                # 清理名称，移除特殊字符
                safe_name = "".join(c for c in name if c.isalnum() or c in (' ', '-', '_')).strip()
                safe_name = safe_name.replace(' ', '_')
                folder_name = f"{timestamp}_{safe_name}"
            else:
                folder_name = timestamp

        # 创建文件夹路径
        folder_path = self.base_dir / folder_name
        folder_path.mkdir(parents=True, exist_ok=True)

        # 创建数据集元数据文件
        metadata = {
            "dataset_name": folder_name,
            "created_at": datetime.now().isoformat(),
            "description": name or "自动生成的数据集",
            "naming_format": naming_format,
            "total_images": 0,
            "images": [],
            "version": "1.0",
            "format": "image+json"
        }

        metadata_path = folder_path / "dataset.json"
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        logger.info(f"创建数据集文件夹: {folder_path}")
        return folder_name, folder_path

    def _generate_sequential_name(self, name: Optional[str] = None) -> str:
        """
        生成序号格式的文件夹名称

        Args:
            name: 可选的数据集名称前缀

        Returns:
            str: 序号格式的文件夹名称
        """
        # 查找现有的序号文件夹
        existing_numbers = []
        for folder_path in self.base_dir.iterdir():
            if folder_path.is_dir():
                folder_name = folder_path.name
                # 检查是否是序号格式 (001, 002, 003...)
                if folder_name.isdigit() and len(folder_name) == 3:
                    existing_numbers.append(int(folder_name))
                # 检查是否是带前缀的序号格式 (001_name, 002_name...)
                elif "_" in folder_name:
                    prefix = folder_name.split("_")[0]
                    if prefix.isdigit() and len(prefix) == 3:
                        existing_numbers.append(int(prefix))

        # 生成下一个序号
        next_number = max(existing_numbers, default=0) + 1
        sequential_prefix = f"{next_number:03d}"

        if name:
            # 清理名称，移除特殊字符
            safe_name = "".join(c for c in name if c.isalnum() or c in (' ', '-', '_')).strip()
            safe_name = safe_name.replace(' ', '_')
            return f"{sequential_prefix}_{safe_name}"
        else:
            return sequential_prefix
    
    def add_image_to_dataset(
        self, 
        dataset_folder: str, 
        image_path: str, 
        description: Dict[str, Any],
        new_filename: Optional[str] = None
    ) -> bool:
        """
        添加图片到数据集（图片+JSON描述格式）
        
        Args:
            dataset_folder: 数据集文件夹名称
            image_path: 源图片路径
            description: 图片描述信息
            new_filename: 新的文件名（可选）
            
        Returns:
            bool: 是否成功添加
        """
        try:
            dataset_path = self.base_dir / dataset_folder
            if not dataset_path.exists():
                logger.error(f"数据集文件夹不存在: {dataset_path}")
                return False
            
            # 确定目标文件名
            source_path = Path(image_path)
            if new_filename:
                # 保持原始扩展名
                target_filename = f"{new_filename}{source_path.suffix}"
            else:
                target_filename = source_path.name
            
            # 复制图片文件
            target_image_path = dataset_path / target_filename
            shutil.copy2(source_path, target_image_path)
            
            # 创建对应的JSON描述文件
            json_filename = target_filename.rsplit('.', 1)[0] + '.json'
            json_path = dataset_path / json_filename
            
            # 获取图片基本信息
            with Image.open(target_image_path) as img:
                width, height = img.size
                format_name = img.format
                mode = img.mode
                channels = len(img.getbands()) if hasattr(img, 'getbands') else 3

            # 使用JSON模板管理器创建标准化描述
            source_type = description.get("source", "upload")

            if source_type == "ai_generation":
                # AI生成图片
                generation_params = {
                    "model": description.get("model"),
                    "prompt": description.get("prompt"),
                    "negative_prompt": description.get("negative_prompt"),
                    "steps": description.get("steps"),
                    "cfg_scale": description.get("cfg_scale"),
                    "seed": description.get("seed"),
                    "sampler": description.get("sampler"),
                    "scheduler": description.get("scheduler"),
                    "guidance_scale": description.get("guidance_scale")
                }

                content_info = {
                    "description": description.get("description", ""),
                    "military_target": description.get("military_target"),
                    "weather": description.get("weather"),
                    "scene": description.get("scene"),
                    "tags": description.get("tags", [])
                }

                standardized_description = JSONTemplateManager.create_ai_generation_template(
                    target_filename, target_image_path.stat().st_size,
                    width, height, format_name, mode, channels,
                    generation_params, content_info
                )

            elif source_type == "traditional_generation":
                # 传统合成图片
                composition_params = {
                    "target_vehicle": description.get("target_vehicle"),
                    "background_scene": description.get("background_scene"),
                    "weather_effect": description.get("weather_effect"),
                    "matting_method": description.get("matting_method"),
                    "blend_mode": description.get("blend_mode"),
                    "opacity": description.get("opacity"),
                    "feather_radius": description.get("feather_radius"),
                    "edge_smooth": description.get("edge_smooth"),
                    "color_match": description.get("color_match"),
                    "lighting_adjust": description.get("lighting_adjust"),
                    "shadow_generate": description.get("shadow_generate")
                }

                content_info = {
                    "description": description.get("description", ""),
                    "military_target": description.get("military_target"),
                    "weather": description.get("weather"),
                    "scene": description.get("scene"),
                    "tags": description.get("tags", [])
                }

                standardized_description = JSONTemplateManager.create_traditional_generation_template(
                    target_filename, target_image_path.stat().st_size,
                    width, height, format_name, mode, channels,
                    composition_params, content_info
                )

            else:
                # 上传图片
                upload_info = {
                    "description": description.get("description", ""),
                    "military_target": description.get("military_target"),
                    "weather": description.get("weather"),
                    "scene": description.get("scene"),
                    "tags": description.get("tags", []),
                    "original_filename": description.get("original_filename"),
                    "content_type": description.get("content_type")
                }

                standardized_description = JSONTemplateManager.create_upload_template(
                    target_filename, target_image_path.stat().st_size,
                    width, height, format_name, mode, channels,
                    upload_info
                )

            # 验证JSON模板
            if not JSONTemplateManager.validate_json_template(standardized_description):
                logger.warning(f"生成的JSON模板验证失败: {target_filename}")

            # 使用标准化描述
            description = standardized_description
            
            # 保存JSON描述文件
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(description, f, indent=2, ensure_ascii=False)
            
            # 更新数据集元数据
            self._update_dataset_metadata(dataset_folder, target_filename, description)
            
            logger.info(f"添加图片到数据集: {target_image_path}")
            return True
            
        except Exception as e:
            logger.error(f"添加图片到数据集失败: {str(e)}")
            return False
    
    def _update_dataset_metadata(self, dataset_folder: str, filename: str, description: Dict[str, Any]):
        """更新数据集元数据"""
        try:
            dataset_path = self.base_dir / dataset_folder
            metadata_path = dataset_path / "dataset.json"
            
            # 读取现有元数据
            if metadata_path.exists():
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            else:
                metadata = {
                    "dataset_name": dataset_folder,
                    "created_at": datetime.now().isoformat(),
                    "total_images": 0,
                    "images": []
                }
            
            # 添加新图片信息
            image_info = {
                "filename": filename,
                "description": description.get("description", ""),
                "military_target": description.get("military_target"),
                "weather": description.get("weather"),
                "scene": description.get("scene"),
                "added_at": description.get("added_at")
            }
            
            metadata["images"].append(image_info)
            metadata["total_images"] = len(metadata["images"])
            metadata["updated_at"] = datetime.now().isoformat()
            
            # 保存更新的元数据
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"更新数据集元数据失败: {str(e)}")
    
    def list_datasets(self) -> List[Dict[str, Any]]:
        """
        列出所有数据集文件夹
        
        Returns:
            List[Dict]: 数据集信息列表
        """
        datasets = []
        
        try:
            for folder_path in self.base_dir.iterdir():
                if folder_path.is_dir():
                    dataset_info = self._get_dataset_info(folder_path.name)
                    if dataset_info:
                        datasets.append(dataset_info)
            
            # 按创建时间排序（最新的在前）
            datasets.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            
        except Exception as e:
            logger.error(f"列出数据集失败: {str(e)}")
        
        return datasets
    
    def _get_dataset_info(self, folder_name: str) -> Optional[Dict[str, Any]]:
        """获取数据集信息"""
        try:
            folder_path = self.base_dir / folder_name
            metadata_path = folder_path / "dataset.json"

            if metadata_path.exists():
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)

                # 添加文件夹路径信息
                metadata["folder_path"] = str(folder_path)
                metadata["folder_name"] = folder_name

                # 检查是否为传统图片数据集
                metadata["dataset_type"] = self._detect_dataset_type(folder_path)

                return metadata
            else:
                # 如果没有元数据文件，创建基本信息
                stat = folder_path.stat()
                dataset_type = self._detect_dataset_type(folder_path)

                return {
                    "dataset_name": folder_name,
                    "folder_name": folder_name,
                    "folder_path": str(folder_path),
                    "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "total_images": len(list(folder_path.glob("*.jpg")) + list(folder_path.glob("*.png"))),
                    "description": self._get_default_description(dataset_type),
                    "dataset_type": dataset_type
                }

        except Exception as e:
            logger.error(f"获取数据集信息失败: {str(e)}")
            return None

    def _detect_dataset_type(self, folder_path: Path) -> str:
        """
        检测数据集类型

        Args:
            folder_path: 数据集文件夹路径

        Returns:
            str: 数据集类型 ("traditional", "ai", "manual", "unknown")
        """
        try:
            # 检查是否包含传统生成的图片
            json_files = list(folder_path.glob("*.json"))

            traditional_count = 0
            ai_count = 0
            total_count = 0

            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        source = data.get("source", "")

                        total_count += 1
                        if source == "traditional_generation":
                            traditional_count += 1
                        elif source == "ai_generation":
                            ai_count += 1

                except Exception:
                    continue

            # 根据比例判断数据集类型
            if total_count == 0:
                return "unknown"
            elif traditional_count > total_count * 0.8:
                return "traditional"
            elif ai_count > total_count * 0.8:
                return "ai"
            elif traditional_count > 0 or ai_count > 0:
                return "mixed"
            else:
                return "manual"

        except Exception as e:
            logger.error(f"检测数据集类型失败: {str(e)}")
            return "unknown"

    def _get_default_description(self, dataset_type: str) -> str:
        """
        根据数据集类型获取默认描述

        Args:
            dataset_type: 数据集类型

        Returns:
            str: 默认描述
        """
        descriptions = {
            "traditional": "传统图片合成数据集",
            "ai": "AI生成图片数据集",
            "mixed": "混合类型数据集",
            "manual": "手动上传数据集",
            "unknown": "未知数据集"
        }
        return descriptions.get(dataset_type, "未知数据集")

    def rename_dataset_folder(self, old_name: str, new_name: str) -> bool:
        """
        重命名数据集文件夹
        
        Args:
            old_name: 原文件夹名称
            new_name: 新文件夹名称
            
        Returns:
            bool: 是否成功重命名
        """
        try:
            old_path = self.base_dir / old_name
            
            # 生成新的文件夹名（保持时间戳格式）
            if "_" in old_name and old_name.split("_")[0].isdigit():
                # 保持原有时间戳
                timestamp = old_name.split("_")[0]
                safe_new_name = "".join(c for c in new_name if c.isalnum() or c in (' ', '-', '_')).strip()
                safe_new_name = safe_new_name.replace(' ', '_')
                new_folder_name = f"{timestamp}_{safe_new_name}"
            else:
                new_folder_name = new_name
            
            new_path = self.base_dir / new_folder_name
            
            if new_path.exists():
                logger.error(f"目标文件夹已存在: {new_path}")
                return False
            
            # 重命名文件夹
            old_path.rename(new_path)
            
            # 更新元数据文件中的名称
            metadata_path = new_path / "dataset.json"
            if metadata_path.exists():
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                metadata["dataset_name"] = new_folder_name
                metadata["updated_at"] = datetime.now().isoformat()
                
                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            logger.info(f"重命名数据集文件夹: {old_name} -> {new_folder_name}")
            return True
            
        except Exception as e:
            logger.error(f"重命名数据集文件夹失败: {str(e)}")
            return False
    
    def delete_dataset_folder(self, folder_name: str) -> bool:
        """
        删除数据集文件夹
        
        Args:
            folder_name: 文件夹名称
            
        Returns:
            bool: 是否成功删除
        """
        try:
            folder_path = self.base_dir / folder_name
            
            if not folder_path.exists():
                logger.error(f"数据集文件夹不存在: {folder_path}")
                return False
            
            # 删除整个文件夹
            shutil.rmtree(folder_path)
            
            logger.info(f"删除数据集文件夹: {folder_path}")
            return True
            
        except Exception as e:
            logger.error(f"删除数据集文件夹失败: {str(e)}")
            return False

    def list_images_in_dataset(self, folder_name: str, include_annotated: bool = True) -> List[Dict[str, Any]]:
        """
        列出数据集中的所有图片（包括标注图片）

        Args:
            folder_name: 数据集文件夹名称
            include_annotated: 是否包含标注图片

        Returns:
            List[Dict]: 图片信息列表
        """
        images = []

        try:
            folder_path = self.base_dir / folder_name
            if not folder_path.exists():
                return images

            # 查找所有图片文件
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif']
            for ext in image_extensions:
                for image_path in folder_path.glob(f"*{ext}"):
                    # 查找对应的JSON文件
                    json_path = image_path.with_suffix('.json')

                    # 直接构建相对于项目根目录的路径
                    # 由于数据集管理器的base_dir是"data/datasets"，图片路径是相对于backend目录的
                    # 我们需要将其转换为相对于项目根目录的路径
                    if image_path.is_absolute():
                        # 如果是绝对路径，尝试计算相对路径
                        try:
                            # 获取项目根目录
                            backend_dir = Path.cwd()  # 当前工作目录是backend
                            project_root = backend_dir.parent  # 项目根目录
                            relative_path = image_path.relative_to(project_root)
                            path_str = str(relative_path).replace('\\', '/')
                        except ValueError:
                            # 如果无法计算相对路径，使用绝对路径
                            path_str = str(image_path).replace('\\', '/')
                    else:
                        # 如果是相对路径，直接添加backend前缀
                        path_str = f"backend/{str(image_path).replace(chr(92), '/')}"

                    image_info = {
                        "filename": image_path.name,
                        "path": path_str,
                        "size": image_path.stat().st_size,
                        "modified_at": datetime.fromtimestamp(image_path.stat().st_mtime).isoformat()
                    }

                    # 如果有对应的JSON文件，读取描述信息
                    if json_path.exists():
                        try:
                            with open(json_path, 'r', encoding='utf-8') as f:
                                description = json.load(f)
                            image_info.update(description)
                        except Exception as e:
                            logger.warning(f"读取图片描述文件失败: {json_path}, {str(e)}")

                    images.append(image_info)

            # 如果需要包含标注图片，扫描标注图片目录
            if include_annotated:
                annotated_images = self._scan_annotated_images()
                images.extend(annotated_images)

            # 按修改时间排序
            images.sort(key=lambda x: x.get("modified_at", ""), reverse=True)

        except Exception as e:
            logger.error(f"列出数据集图片失败: {str(e)}")

        return images

    def _scan_annotated_images(self) -> List[Dict[str, Any]]:
        """
        扫描标注图片目录，获取所有标注图片信息

        Returns:
            List[Dict]: 标注图片信息列表
        """
        annotated_images = []

        try:
            # 扫描多个可能的标注图片目录
            # 注意：base_dir 是 data/datasets，需要找到 backend/data/generated
            backend_data_dir = Path(__file__).parent.parent.parent.parent / "data" / "generated"
            annotated_dirs = [
                backend_data_dir / "ai_generated" / "gsa_detection",
                backend_data_dir / "ai",  # 直接在ai目录下查找annotated_开头的文件
            ]

            for annotated_dir in annotated_dirs:
                if not annotated_dir.exists():
                    continue

                # 查找标注图片文件
                for pattern in ["*_annotated.*", "annotated_*"]:
                    for annotated_path in annotated_dir.glob(pattern):
                        if (annotated_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp'] and
                            ('_annotated.' in annotated_path.name or annotated_path.name.startswith('annotated_'))):
                            # 构建标注图片信息
                            image_info = self._build_annotated_image_info(annotated_path)
                            if image_info:
                                annotated_images.append(image_info)

        except Exception as e:
            logger.error(f"扫描标注图片失败: {str(e)}")

        return annotated_images

    def _build_annotated_image_info(self, annotated_path: Path) -> Optional[Dict[str, Any]]:
        """
        构建标注图片的信息字典

        Args:
            annotated_path: 标注图片路径

        Returns:
            Dict: 标注图片信息，如果构建失败返回None
        """
        try:
            # 基本文件信息
            image_info = {
                "filename": annotated_path.name,
                "path": str(annotated_path),
                "size": annotated_path.stat().st_size,
                "modified_at": datetime.fromtimestamp(annotated_path.stat().st_mtime).isoformat(),
                "source": "annotation",  # 标识为标注图片
                "image_type": "annotated"
            }

            # 尝试从文件名解析原始图片信息
            filename = annotated_path.stem
            original_info = self._parse_annotated_filename(filename)
            if original_info:
                image_info.update(original_info)

            # 查找对应的标注JSON文件
            annotation_json = self._find_annotation_json(annotated_path)
            if annotation_json:
                image_info["annotation_data"] = annotation_json

            # 查找对应的原始图片JSON文件
            original_json = self._find_original_image_json(annotated_path)
            if original_json:
                # 只取需要的字段，不覆盖标注图片的基本信息
                if "metadata" in original_json:
                    image_info["metadata"] = original_json["metadata"]
                if "generation_type" in original_json:
                    image_info["generation_type"] = original_json["generation_type"]
                # 保存原始图片的尺寸信息
                if "width" in original_json:
                    image_info["original_width"] = original_json["width"]
                if "height" in original_json:
                    image_info["original_height"] = original_json["height"]

            return image_info

        except Exception as e:
            logger.error(f"构建标注图片信息失败: {annotated_path}, {str(e)}")
            return None

    def _parse_annotated_filename(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        解析标注图片文件名，提取原始图片信息

        Args:
            filename: 标注图片文件名（不含扩展名）

        Returns:
            Dict: 解析出的信息，如果解析失败返回None
        """
        try:
            # 处理格式：{uuid}_{number}_annotated 或 annotated_{uuid}_{number}_temp
            if filename.endswith("_annotated"):
                # 格式：{uuid}_{number}_annotated
                base_name = filename[:-10]  # 移除 "_annotated"
                parts = base_name.split("_")
                if len(parts) >= 2:
                    uuid_part = "_".join(parts[:-1])
                    number_part = parts[-1]
                    return {
                        "original_uuid": uuid_part,
                        "original_index": number_part,
                        "original_filename": f"{base_name}.png"
                    }
            elif filename.startswith("annotated_") and filename.endswith("_temp"):
                # 格式：annotated_{uuid}_{number}_temp
                base_name = filename[10:-5]  # 移除 "annotated_" 和 "_temp"
                parts = base_name.split("_")
                if len(parts) >= 2:
                    uuid_part = "_".join(parts[:-1])
                    number_part = parts[-1]
                    return {
                        "original_uuid": uuid_part,
                        "original_index": number_part,
                        "original_filename": f"{uuid_part}_{number_part}.png"
                    }

            return None

        except Exception as e:
            logger.error(f"解析标注图片文件名失败: {filename}, {str(e)}")
            return None

    def _find_annotation_json(self, annotated_path: Path) -> Optional[Dict[str, Any]]:
        """
        查找标注图片对应的标注JSON文件

        Args:
            annotated_path: 标注图片路径

        Returns:
            Dict: 标注JSON内容，如果找不到返回None
        """
        try:
            # 从文件名解析原始信息
            filename = annotated_path.stem
            original_info = self._parse_annotated_filename(filename)

            if original_info:
                uuid_part = original_info.get("original_uuid")
                index_part = original_info.get("original_index")

                if uuid_part and index_part:
                    # 查找标注JSON文件，检查多个可能的位置
                    backend_data_dir = Path(__file__).parent.parent.parent.parent / "data" / "generated"
                    annotation_dirs = [
                        backend_data_dir / "ai_generated" / "annotations",  # 新位置
                        backend_data_dir / "annotations",  # 旧位置
                    ]

                    annotation_file = None
                    for annotation_dir in annotation_dirs:
                        potential_file = annotation_dir / f"{uuid_part}_{index_part}_annotation.json"
                        if potential_file.exists():
                            annotation_file = potential_file
                            break

                    if annotation_file and annotation_file.exists():
                        with open(annotation_file, 'r', encoding='utf-8') as f:
                            return json.load(f)

            return None

        except Exception as e:
            logger.error(f"查找标注JSON失败: {annotated_path}, {str(e)}")
            return None

    def _find_original_image_json(self, annotated_path: Path) -> Optional[Dict[str, Any]]:
        """
        查找标注图片对应的原始图片JSON文件

        Args:
            annotated_path: 标注图片路径

        Returns:
            Dict: 原始图片JSON内容，如果找不到返回None
        """
        try:
            # 从文件名解析原始信息
            filename = annotated_path.stem
            original_info = self._parse_annotated_filename(filename)

            if original_info:
                original_filename = original_info.get("original_filename")

                if original_filename:
                    # 查找原始图片JSON文件，检查多个可能的位置
                    backend_data_dir = Path(__file__).parent.parent.parent.parent / "data" / "generated"
                    ai_dirs = [
                        backend_data_dir / "ai_generated",  # 新位置
                        backend_data_dir / "ai",  # 旧位置
                    ]

                    json_filename = original_filename.replace(".png", ".json")
                    for ai_dir in ai_dirs:
                        json_file = ai_dir / json_filename
                        if json_file.exists():
                            with open(json_file, 'r', encoding='utf-8') as f:
                                return json.load(f)

            return None

        except Exception as e:
            logger.error(f"查找原始图片JSON失败: {annotated_path}, {str(e)}")
            return None

    def rename_image(self, folder_name: str, old_filename: str, new_filename: str) -> bool:
        """
        重命名数据集中的图片

        Args:
            folder_name: 数据集文件夹名称
            old_filename: 原文件名
            new_filename: 新文件名

        Returns:
            bool: 是否成功重命名
        """
        try:
            folder_path = self.base_dir / folder_name
            old_image_path = folder_path / old_filename

            if not old_image_path.exists():
                logger.error(f"图片文件不存在: {old_image_path}")
                return False

            # 确保新文件名有正确的扩展名
            old_ext = old_image_path.suffix
            if not new_filename.endswith(old_ext):
                new_filename += old_ext

            new_image_path = folder_path / new_filename

            if new_image_path.exists():
                logger.error(f"目标文件已存在: {new_image_path}")
                return False

            # 重命名图片文件
            old_image_path.rename(new_image_path)

            # 重命名对应的JSON文件
            old_json_path = old_image_path.with_suffix('.json')
            new_json_path = new_image_path.with_suffix('.json')

            if old_json_path.exists():
                old_json_path.rename(new_json_path)

                # 更新JSON文件中的文件名
                try:
                    with open(new_json_path, 'r', encoding='utf-8') as f:
                        description = json.load(f)

                    description["filename"] = new_filename
                    description["updated_at"] = datetime.now().isoformat()

                    with open(new_json_path, 'w', encoding='utf-8') as f:
                        json.dump(description, f, indent=2, ensure_ascii=False)

                except Exception as e:
                    logger.warning(f"更新JSON文件失败: {str(e)}")

            # 更新数据集元数据
            self._update_image_in_metadata(folder_name, old_filename, new_filename)

            logger.info(f"重命名图片: {old_filename} -> {new_filename}")
            return True

        except Exception as e:
            logger.error(f"重命名图片失败: {str(e)}")
            return False

    def delete_image(self, folder_name: str, filename: str) -> bool:
        """
        删除数据集中的图片

        Args:
            folder_name: 数据集文件夹名称
            filename: 文件名

        Returns:
            bool: 是否成功删除
        """
        try:
            folder_path = self.base_dir / folder_name
            image_path = folder_path / filename

            if not image_path.exists():
                logger.error(f"图片文件不存在: {image_path}")
                return False

            # 删除图片文件
            image_path.unlink()

            # 删除对应的JSON文件
            json_path = image_path.with_suffix('.json')
            if json_path.exists():
                json_path.unlink()

            # 从数据集元数据中移除
            self._remove_image_from_metadata(folder_name, filename)

            logger.info(f"删除图片: {image_path}")
            return True

        except Exception as e:
            logger.error(f"删除图片失败: {str(e)}")
            return False

    def _update_image_in_metadata(self, folder_name: str, old_filename: str, new_filename: str):
        """在元数据中更新图片信息"""
        try:
            folder_path = self.base_dir / folder_name
            metadata_path = folder_path / "dataset.json"

            if not metadata_path.exists():
                return

            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)

            # 更新图片列表中的文件名
            for image_info in metadata.get("images", []):
                if image_info.get("filename") == old_filename:
                    image_info["filename"] = new_filename
                    break

            metadata["updated_at"] = datetime.now().isoformat()

            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"更新元数据中的图片信息失败: {str(e)}")

    def _remove_image_from_metadata(self, folder_name: str, filename: str):
        """从元数据中移除图片信息"""
        try:
            folder_path = self.base_dir / folder_name
            metadata_path = folder_path / "dataset.json"

            if not metadata_path.exists():
                return

            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)

            # 从图片列表中移除
            metadata["images"] = [
                img for img in metadata.get("images", [])
                if img.get("filename") != filename
            ]

            metadata["total_images"] = len(metadata["images"])
            metadata["updated_at"] = datetime.now().isoformat()

            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"从元数据中移除图片信息失败: {str(e)}")

    def get_dataset_statistics(self) -> Dict[str, Any]:
        """
        获取数据集统计信息

        Returns:
            Dict: 统计信息
        """
        try:
            datasets = self.list_datasets()
            total_datasets = len(datasets)
            total_images = sum(dataset.get("total_images", 0) for dataset in datasets)

            # 计算总大小
            total_size = 0
            for folder_path in self.base_dir.iterdir():
                if folder_path.is_dir():
                    for file_path in folder_path.rglob("*"):
                        if file_path.is_file():
                            total_size += file_path.stat().st_size

            return {
                "total_datasets": total_datasets,
                "total_images": total_images,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "base_directory": str(self.base_dir)
            }

        except Exception as e:
            logger.error(f"获取数据集统计信息失败: {str(e)}")
            return {
                "total_datasets": 0,
                "total_images": 0,
                "total_size_bytes": 0,
                "total_size_mb": 0,
                "base_directory": str(self.base_dir)
            }

    def add_generated_image_to_dataset(
        self,
        dataset_folder: str,
        image_path: str,
        generation_type: str,
        generation_params: Dict[str, Any],
        content_info: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        添加生成的图片到数据集（专用于AI和传统生成）

        Args:
            dataset_folder: 数据集文件夹名称
            image_path: 生成的图片路径
            generation_type: 生成类型 ("ai_generation" 或 "traditional_generation")
            generation_params: 生成参数
            content_info: 内容信息

        Returns:
            bool: 是否成功添加
        """
        try:
            # 构建描述信息
            description = {
                "source": generation_type,
                **generation_params
            }

            if content_info:
                description.update(content_info)

            # 使用现有的添加方法
            return self.add_image_to_dataset(
                dataset_folder,
                image_path,
                description
            )

        except Exception as e:
            logger.error(f"添加生成图片到数据集失败: {str(e)}")
            return False
