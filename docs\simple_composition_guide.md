# 简化图片合成功能使用指南

## 概述

简化图片合成功能是对传统图片生成工具的重构，专注于提供最简化的图片合成体验。该功能使用 `pic_resource` 目录中的背景图片，叠加军事目标图片，实现快速、简单的图片合成。

## 功能特点

### 1. 最简化实现
- 采用最直接的图片合成方法
- 避免复杂的抠图和混合算法
- 保持代码简洁易维护

### 2. 资源管理
- **背景图片**：使用 `pic_resource` 目录中的现有资源
- **军事目标**：使用 `pic_resource/targets` 目录中的军事目标图片
- **自动选择**：根据天气和场景自动选择合适的背景图片

### 3. 基础参数调整
- **目标大小**：调整军事目标相对于背景的比例（10%-80%）
- **透明度**：调整目标图片的透明度（50%-100%）
- **位置**：支持随机位置或居中放置
- **阴影**：可选的简单阴影效果

## 界面使用

### 传统生成选项卡

1. **目标与场景选择**
   - 军事目标：坦克、战机、舰艇
   - 天气条件：晴天、雨天、雪天、大雾、夜间
   - 地形场景：城市、岛屿、乡村

2. **合成配置**
   - 目标大小：滑块调整（默认30%）
   - 透明度：滑块调整（默认100%）
   - 随机位置：复选框（默认开启）
   - 添加阴影：复选框（默认关闭）
   - 启用智能抠图：复选框（默认开启）
   - 抠图方法：自动选择、深度学习(rembg)、传统算法(GrabCut)

3. **生成配置**
   - 生成数量：1-20张（默认1张）
   - 图像尺寸：自动使用背景图片的原始尺寸

## 技术架构

### 核心组件

1. **SimpleCompositionService** - 主要合成服务
   - 协调各个组件
   - 管理合成流程
   - 处理结果保存

2. **SimpleBackgroundSelector** - 背景图片选择器
   - 扫描 `pic_resource` 目录
   - 根据天气和场景选择背景
   - 支持降级选择策略

3. **SimpleTargetProcessor** - 军事目标处理器
   - 加载 `targets` 目录中的图片
   - 基础的尺寸调整和透明度处理
   - 支持旋转、翻转等简单变换

4. **SimpleCompositor** - 基础合成器
   - 简单的图片叠加
   - 位置计算和调整
   - 可选的阴影效果

5. **MattingProcessor** - 抠图处理器
   - 基于深度学习的rembg抠图
   - 传统GrabCut算法抠图
   - 智能缓存和降级机制

### 文件结构

```
backend/app/services/traditional/
├── simple_composition_service.py    # 主要合成服务
├── simple_background_selector.py    # 背景选择器
├── simple_target_processor.py       # 目标处理器
├── simple_compositor.py             # 基础合成器
└── matting_processor.py             # 抠图处理器
```

## 资源目录结构

```
pic_resource/
├── day/                    # 白天场景
│   ├── city/              # 城市
│   ├── island/            # 岛屿
│   └── rural/             # 乡村
├── night/                 # 夜间场景
├── rainy/                 # 雨天场景
├── snowy/                 # 雪天场景
├── foggy/                 # 大雾场景
└── targets/               # 军事目标
    ├── tanks/             # 坦克
    ├── aircraft/          # 战机
    └── ships/             # 舰艇
```

## API 使用示例

```python
from backend.app.services.traditional.simple_composition_service import SimpleCompositionService

# 创建服务实例
service = SimpleCompositionService({
    "output_dir": "output",
    "quality": 95
})

# 初始化服务
await service.initialize()

# 生成图像
result = await service.generate_images(
    military_target="坦克",
    weather="晴天", 
    scene="城市",
    num_images=1,
    target_scale=0.3,
    opacity=1.0,
    random_position=True,
    add_shadow=False
)
```

## 输出结果

生成的图片会：
1. 保存到指定的输出目录
2. 自动保存到数据库（如果配置正确）
3. 生成简化的标注信息（边界框和分割掩码）
4. 在预览区域显示

## 性能特点

- **快速生成**：平均每张图片0.3-0.5秒
- **低资源消耗**：避免复杂算法，减少CPU和内存使用
- **稳定可靠**：简化的流程减少了出错的可能性
- **自适应尺寸**：自动使用背景图片原始尺寸，保持最佳质量

## 与原有功能的区别

| 特性 | 原有传统合成 | 简化合成 |
|------|-------------|----------|
| 抠图功能 | 支持多种抠图算法 | 无抠图，直接叠加 |
| 混合模式 | 10+种混合模式 | 仅透明度叠加 |
| 光照调整 | 复杂的光照匹配 | 无光照调整 |
| 色彩匹配 | 自动色彩匹配 | 无色彩匹配 |
| 天气效果 | 复杂的天气渲染 | 通过背景图片体现 |
| 图像尺寸 | 用户指定固定尺寸 | 自动使用背景原始尺寸 |
| 配置选项 | 20+个参数 | 4个核心参数 |
| 生成速度 | 较慢（2-5秒） | 快速（0.3-0.5秒） |

## 注意事项

1. **图片格式**：支持 JPG、PNG、BMP、WEBP、TIFF 格式
2. **目标图片**：建议使用已抠图的PNG格式以获得更好效果
3. **背景选择**：如果指定组合不存在，会自动降级选择
4. **数据库**：需要正确配置数据库连接才能保存记录

## 故障排除

### 常见问题

1. **找不到背景图片**
   - 检查 `pic_resource` 目录结构
   - 确认天气和场景映射正确

2. **找不到目标图片**
   - 检查 `pic_resource/targets` 目录
   - 确认目标类型目录存在

3. **数据库保存失败**
   - 检查数据库连接配置
   - 确认数据库表结构正确

4. **生成图片质量问题**
   - 调整目标大小比例
   - 检查原始图片质量
   - 尝试不同的透明度设置

## 最新功能更新

### v2.2 - 智能抠图功能
- **深度学习抠图**：集成rembg库，支持AI自动抠图
- **传统算法抠图**：GrabCut算法作为备选方案
- **智能缓存系统**：避免重复处理，大幅提升性能
- **自动降级机制**：抠图失败时自动使用原始图片
- **用户友好界面**：简单的开关和方法选择

### v2.1 - 自动尺寸适配
- **移除固定尺寸限制**：不再需要用户选择图像尺寸
- **自动使用背景原始尺寸**：保持背景图片的原始质量和比例
- **支持多种尺寸**：从小尺寸(168x300)到高分辨率(4240x2832)
- **优化用户体验**：简化界面，减少配置步骤

### 综合优化效果
- 生成的图片质量更高，保持背景图片原始细节
- 支持各种宽高比的背景图片
- 减少了图片缩放带来的质量损失
- 军事目标与背景融合更加自然
- 界面更加简洁，操作更加便捷

## 抠图功能详细说明

### 抠图方法对比

| 方法 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| 深度学习(rembg) | 精度高，边缘自然 | 需要网络下载模型，首次较慢 | 复杂背景，精细抠图 |
| 传统算法(GrabCut) | 无需网络，稳定可靠 | 精度相对较低 | 简单背景，快速处理 |
| 自动选择 | 智能选择最佳方法 | - | 推荐使用 |

### 抠图性能

- **处理时间**：GrabCut 0.5-2秒，rembg 1-3秒（首次需下载模型）
- **缓存机制**：相同图片第二次处理几乎瞬间完成
- **质量验证**：自动验证抠图质量，失败时降级处理
- **错误处理**：抠图失败自动使用原始图片，确保流程不中断

### 使用建议

1. **首次使用**：建议选择"自动选择"，系统会智能选择最佳方法
2. **网络环境差**：选择"传统算法(GrabCut)"避免网络问题
3. **追求质量**：选择"深度学习(rembg)"获得最佳抠图效果
4. **批量处理**：启用抠图后，缓存机制会显著提升后续处理速度

### 故障排除

1. **rembg下载失败**：检查网络连接，系统会自动降级到GrabCut
2. **抠图效果不佳**：尝试不同的抠图方法，或关闭抠图使用原图
3. **处理时间过长**：高分辨率图片处理时间较长，属于正常现象
4. **缓存问题**：可通过清空缓存重新处理

## 扩展建议

如需要更高级的功能，可以考虑：
1. 添加简单的色彩调整
2. 支持多个目标叠加
3. 添加更多的阴影样式
4. 支持自定义位置设置
5. 添加图片质量预设选项
6. 集成更多抠图算法（如SAM、U2Net等）
