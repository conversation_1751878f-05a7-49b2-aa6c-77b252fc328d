"""
数据集管理客户端 - 文件系统存储方案
"""

import requests
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class DatasetManagementClient:
    """数据集管理客户端"""
    
    def __init__(self, api_base: str = "http://localhost:8000/api/v1"):
        self.api_base = api_base
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送API请求"""
        url = f"{self.api_base}{endpoint}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {method} {url}, 错误: {str(e)}")
            raise Exception(f"API请求失败: {str(e)}")
    
    def list_datasets(self) -> Dict[str, Any]:
        """列出所有数据集"""
        try:
            return self._make_request('GET', '/datasets/list')
        except Exception as e:
            logger.error(f"列出数据集失败: {str(e)}")
            return {
                "success": False,
                "message": f"列出数据集失败: {str(e)}",
                "data": {"datasets": [], "total": 0}
            }
    
    def create_dataset(self, name: str = None, naming_format: str = "timestamp") -> Dict[str, Any]:
        """创建新数据集"""
        try:
            data = {
                "name": name,
                "naming_format": naming_format
            }
            return self._make_request('POST', '/datasets/create', json=data)
        except Exception as e:
            logger.error(f"创建数据集失败: {str(e)}")
            return {
                "success": False,
                "message": f"创建数据集失败: {str(e)}"
            }
    
    def rename_dataset(self, old_name: str, new_name: str) -> Dict[str, Any]:
        """重命名数据集"""
        try:
            data = {"old_name": old_name, "new_name": new_name}
            return self._make_request('POST', '/datasets/rename', json=data)
        except Exception as e:
            logger.error(f"重命名数据集失败: {str(e)}")
            return {
                "success": False,
                "message": f"重命名数据集失败: {str(e)}"
            }
    
    def delete_dataset(self, folder_name: str) -> Dict[str, Any]:
        """删除数据集"""
        try:
            return self._make_request('DELETE', f'/datasets/{folder_name}')
        except Exception as e:
            logger.error(f"删除数据集失败: {str(e)}")
            return {
                "success": False,
                "message": f"删除数据集失败: {str(e)}"
            }
    
    def list_images_in_dataset(self, folder_name: str, include_annotated: bool = True) -> Dict[str, Any]:
        """列出数据集中的图片（包括标注图片）"""
        try:
            params = {"include_annotated": include_annotated}
            return self._make_request('GET', f'/datasets/{folder_name}/images', params=params)
        except Exception as e:
            logger.error(f"列出数据集图片失败: {str(e)}")
            return {
                "success": False,
                "message": f"列出数据集图片失败: {str(e)}",
                "data": {"images": [], "total": 0}
            }

    def list_all_annotated_images(self) -> Dict[str, Any]:
        """列出所有标注图片"""
        try:
            return self._make_request('GET', '/datasets/annotated/list')
        except Exception as e:
            logger.error(f"列出标注图片失败: {str(e)}")
            return {
                "success": False,
                "message": f"列出标注图片失败: {str(e)}",
                "data": {"images": [], "total": 0}
            }
    
    def upload_image_to_dataset(
        self, 
        folder_name: str, 
        file_path: str,
        description: str = None,
        military_target: str = None,
        weather: str = None,
        scene: str = None,
        new_filename: str = None
    ) -> Dict[str, Any]:
        """上传图片到数据集"""
        try:
            # 准备文件和数据
            with open(file_path, 'rb') as f:
                files = {'file': f}
                data = {}
                
                if description:
                    data['description'] = description
                if military_target:
                    data['military_target'] = military_target
                if weather:
                    data['weather'] = weather
                if scene:
                    data['scene'] = scene
                if new_filename:
                    data['new_filename'] = new_filename
                
                # 发送请求（不使用session的默认headers，因为multipart/form-data需要特殊处理）
                url = f"{self.api_base}/datasets/{folder_name}/upload"
                response = requests.post(url, files=files, data=data)
                response.raise_for_status()
                return response.json()
                
        except Exception as e:
            logger.error(f"上传图片到数据集失败: {str(e)}")
            return {
                "success": False,
                "message": f"上传图片失败: {str(e)}"
            }
    
    def rename_image(self, folder_name: str, old_filename: str, new_filename: str) -> Dict[str, Any]:
        """重命名图片"""
        try:
            data = {
                "folder_name": folder_name,
                "old_filename": old_filename,
                "new_filename": new_filename
            }
            return self._make_request('POST', '/datasets/images/rename', json=data)
        except Exception as e:
            logger.error(f"重命名图片失败: {str(e)}")
            return {
                "success": False,
                "message": f"重命名图片失败: {str(e)}"
            }
    
    def delete_image(self, folder_name: str, filename: str) -> Dict[str, Any]:
        """删除图片"""
        try:
            data = {
                "folder_name": folder_name,
                "filename": filename
            }
            return self._make_request('POST', '/datasets/images/delete', json=data)
        except Exception as e:
            logger.error(f"删除图片失败: {str(e)}")
            return {
                "success": False,
                "message": f"删除图片失败: {str(e)}"
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        try:
            return self._make_request('GET', '/datasets/statistics')
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取统计信息失败: {str(e)}",
                "data": {
                    "total_datasets": 0,
                    "total_images": 0,
                    "total_size_bytes": 0,
                    "total_size_mb": 0
                }
            }
    
    def add_generated_image_to_dataset(
        self, 
        folder_name: str, 
        image_path: str, 
        description: Dict[str, Any]
    ) -> Dict[str, Any]:
        """将生成的图片添加到数据集"""
        try:
            data = {
                "image_path": image_path,
                "description": description
            }
            return self._make_request('POST', f'/datasets/{folder_name}/add-generated', json=data)
        except Exception as e:
            logger.error(f"添加生成图片到数据集失败: {str(e)}")
            return {
                "success": False,
                "message": f"添加生成图片失败: {str(e)}"
            }
    
    def close(self):
        """关闭客户端连接"""
        if self.session:
            self.session.close()
            logger.info("数据集管理客户端连接已关闭")
