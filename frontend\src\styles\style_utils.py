"""
样式工具类 - 提供常用的样式字符串
与统一主题保持一致
"""

from .unified_theme import COLORS, FONTS, SPACING, RADIUS

class StyleUtils:
    """样式工具类"""
    
    @staticmethod
    def get_status_style(status_type: str) -> str:
        """
        获取状态标签样式
        
        Args:
            status_type: 状态类型 ('loading', 'success', 'error', 'warning', 'info', 'default')
        
        Returns:
            样式字符串
        """
        status_styles = {
            'loading': f"color: {COLORS['primary']}; font-size: {FONTS['size_base']}; padding: {SPACING['sm']};",
            'success': f"color: {COLORS['success']}; font-size: {FONTS['size_base']}; padding: {SPACING['sm']};",
            'error': f"color: {COLORS['danger']}; font-size: {FONTS['size_base']}; padding: {SPACING['sm']};",
            'warning': f"color: {COLORS['warning']}; font-size: {FONTS['size_base']}; padding: {SPACING['sm']};",
            'info': f"color: {COLORS['info']}; font-size: {FONTS['size_base']}; padding: {SPACING['sm']};",
            'default': f"color: {COLORS['text_secondary']}; font-size: {FONTS['size_base']}; padding: {SPACING['sm']};",
        }
        return status_styles.get(status_type, status_styles['default'])
    
    @staticmethod
    def get_model_status_style(status_type: str) -> str:
        """
        获取模型状态样式
        
        Args:
            status_type: 状态类型 ('loaded', 'preloading', 'cached', 'sd_weight', 'unloaded')
        
        Returns:
            样式字符串
        """
        model_styles = {
            'loaded': f"color: {COLORS['success']}; font-size: {FONTS['size_base']};",
            'preloading': f"color: {COLORS['primary']}; font-size: {FONTS['size_base']};",
            'cached': f"color: {COLORS['success']}; font-size: {FONTS['size_base']};",
            'sd_weight': f"color: {COLORS['info']}; font-size: {FONTS['size_base']};",
            'unloaded': f"color: {COLORS['text_muted']}; font-size: {FONTS['size_base']};",
            'pending': f"color: {COLORS['warning']}; font-size: {FONTS['size_base']};",
        }
        return model_styles.get(status_type, model_styles['unloaded'])
    
    @staticmethod
    def get_info_text_style() -> str:
        """获取信息文本样式"""
        return f"color: {COLORS['text_secondary']}; font-style: italic; font-size: {FONTS['size_sm']};"
    
    @staticmethod
    def get_progress_label_style() -> str:
        """获取进度标签样式"""
        return f"color: {COLORS['primary']}; font-size: {FONTS['size_base']}; padding: {SPACING['sm']};"
    
    @staticmethod
    def get_error_text_style() -> str:
        """获取错误文本样式"""
        return f"color: {COLORS['danger']}; font-size: {FONTS['size_lg']};"
    
    @staticmethod
    def get_hint_text_style() -> str:
        """获取提示文本样式"""
        return f"color: {COLORS['text_secondary']}; font-size: {FONTS['size_base']};"
    
    @staticmethod
    def get_preview_placeholder_style() -> str:
        """获取预览占位符样式"""
        return f"""
            QLabel {{
                border: 2px dashed {COLORS['border_medium']};
                border-radius: {RADIUS['md']};
                background-color: {COLORS['bg_secondary']};
                color: {COLORS['text_secondary']};
                font-size: {FONTS['size_lg']};
                padding: {SPACING['xl']};
                font-family: {FONTS['family_primary']};
            }}
        """
    
    @staticmethod
    def get_image_info_style() -> str:
        """获取图片信息样式"""
        return f"""
            QLabel {{
                color: {COLORS['text_primary']};
                font-size: {FONTS['size_sm']};
                padding: {SPACING['xs']};
                background-color: {COLORS['bg_secondary']};
                border-radius: {RADIUS['sm']};
                font-family: {FONTS['family_primary']};
            }}
        """
    
    @staticmethod
    def get_section_title_style() -> str:
        """获取章节标题样式"""
        return f"""
            QLabel {{
                color: {COLORS['text_primary']};
                font-size: {FONTS['size_xl']};
                font-weight: {FONTS['weight_medium']};
                padding: {SPACING['base']} 0;
                font-family: {FONTS['family_primary']};
            }}
        """
    
    @staticmethod
    def get_form_label_style() -> str:
        """获取表单标签样式"""
        return f"""
            QLabel {{
                color: {COLORS['text_primary']};
                font-size: {FONTS['size_base']};
                font-weight: {FONTS['weight_normal']};
                padding: {SPACING['xs']};
                font-family: {FONTS['family_primary']};
            }}
        """
    
    @staticmethod
    def get_card_style() -> str:
        """获取卡片样式"""
        return f"""
            QWidget {{
                background-color: {COLORS['bg_primary']};
                border: 1px solid {COLORS['border_light']};
                border-radius: {RADIUS['md']};
                padding: {SPACING['lg']};
            }}
        """
    
    @staticmethod
    def get_separator_style() -> str:
        """获取分隔线样式"""
        return f"""
            QFrame {{
                background-color: {COLORS['border_light']};
                border: none;
            }}
        """
    
    @staticmethod
    def apply_status_style(widget, status_type: str):
        """
        应用状态样式到控件
        
        Args:
            widget: Qt控件
            status_type: 状态类型
        """
        widget.setStyleSheet(StyleUtils.get_status_style(status_type))
    
    @staticmethod
    def apply_model_status_style(widget, status_type: str):
        """
        应用模型状态样式到控件
        
        Args:
            widget: Qt控件
            status_type: 状态类型
        """
        widget.setStyleSheet(StyleUtils.get_model_status_style(status_type))
    
    @staticmethod
    def apply_info_text_style(widget):
        """应用信息文本样式"""
        widget.setStyleSheet(StyleUtils.get_info_text_style())
    
    @staticmethod
    def apply_progress_label_style(widget):
        """应用进度标签样式"""
        widget.setStyleSheet(StyleUtils.get_progress_label_style())
    
    @staticmethod
    def apply_error_text_style(widget):
        """应用错误文本样式"""
        widget.setStyleSheet(StyleUtils.get_error_text_style())
    
    @staticmethod
    def apply_hint_text_style(widget):
        """应用提示文本样式"""
        widget.setStyleSheet(StyleUtils.get_hint_text_style())

# 为了向后兼容，提供一些常用的样式常量
STATUS_STYLES = {
    'loading': StyleUtils.get_status_style('loading'),
    'success': StyleUtils.get_status_style('success'),
    'error': StyleUtils.get_status_style('error'),
    'warning': StyleUtils.get_status_style('warning'),
    'info': StyleUtils.get_status_style('info'),
    'default': StyleUtils.get_status_style('default'),
}

MODEL_STATUS_STYLES = {
    'loaded': StyleUtils.get_model_status_style('loaded'),
    'preloading': StyleUtils.get_model_status_style('preloading'),
    'cached': StyleUtils.get_model_status_style('cached'),
    'sd_weight': StyleUtils.get_model_status_style('sd_weight'),
    'unloaded': StyleUtils.get_model_status_style('unloaded'),
    'pending': StyleUtils.get_model_status_style('pending'),
}
