{"output_model_all": {"model_key": "output_model_all", "model_name": "output_model_all", "model_path": "D:/workspace/pic_gen_tool_v2/output_model_all.safetensors", "description": "safetensors模型: output_model_all", "model_type": "safetensors", "is_local": true, "added_at": "2025-07-05T05:06:14.806328", "enabled": true, "cfg_scale_range": [7.0, 12.0], "steps_range": [20, 50], "target_size_bias": "medium", "recommended_for": ["自定义场景"], "file_info": {"path": "D:\\workspace\\pic_gen_tool_v2\\output_model_all.safetensors", "size": 37862112, "modified_time": "2025-07-04T19:56:29.572317", "hash": "a7148ca4d8a7bdcd4100101fe2dcd34c", "weight_keys": ["lora_te_text_model_encoder_layers_0_mlp_fc1.alpha", "lora_te_text_model_encoder_layers_0_mlp_fc1.lora_down.weight", "lora_te_text_model_encoder_layers_0_mlp_fc1.lora_up.weight", "lora_te_text_model_encoder_layers_0_mlp_fc2.alpha", "lora_te_text_model_encoder_layers_0_mlp_fc2.lora_down.weight", "lora_te_text_model_encoder_layers_0_mlp_fc2.lora_up.weight", "lora_te_text_model_encoder_layers_0_self_attn_k_proj.alpha", "lora_te_text_model_encoder_layers_0_self_attn_k_proj.lora_down.weight", "lora_te_text_model_encoder_layers_0_self_attn_k_proj.lora_up.weight", "lora_te_text_model_encoder_layers_0_self_attn_out_proj.alpha"], "total_weights": 792, "is_sd_model": true, "model_type": "stable_diffusion"}, "local_path": "models\\custom\\output_model_all\\output_model_all.safetensors"}, "test_model_real": {"model_key": "test_model_real", "model_name": "真实测试模型", "model_path": "D:\\workspace\\pic_gen_tool_v2\\output_model_all.safetensors", "description": "这是一个使用真实文件的测试模型", "model_type": "safetensors", "is_local": true, "added_at": "2025-07-05T05:34:45.903842", "enabled": true, "cfg_scale_range": [7.0, 12.0], "steps_range": [20, 50], "target_size_bias": "medium", "recommended_for": ["自定义场景"], "file_info": {"path": "D:\\workspace\\pic_gen_tool_v2\\output_model_all.safetensors", "size": 37862112, "modified_time": "2025-07-04T19:56:29.572317", "hash": "a7148ca4d8a7bdcd4100101fe2dcd34c", "weight_keys": ["lora_te_text_model_encoder_layers_0_mlp_fc1.alpha", "lora_te_text_model_encoder_layers_0_mlp_fc1.lora_down.weight", "lora_te_text_model_encoder_layers_0_mlp_fc1.lora_up.weight", "lora_te_text_model_encoder_layers_0_mlp_fc2.alpha", "lora_te_text_model_encoder_layers_0_mlp_fc2.lora_down.weight", "lora_te_text_model_encoder_layers_0_mlp_fc2.lora_up.weight", "lora_te_text_model_encoder_layers_0_self_attn_k_proj.alpha", "lora_te_text_model_encoder_layers_0_self_attn_k_proj.lora_down.weight", "lora_te_text_model_encoder_layers_0_self_attn_k_proj.lora_up.weight", "lora_te_text_model_encoder_layers_0_self_attn_out_proj.alpha"], "total_weights": 792, "is_sd_model": true, "model_type": "stable_diffusion"}, "local_path": "models\\custom\\test_model_real\\output_model_all.safetensors"}}