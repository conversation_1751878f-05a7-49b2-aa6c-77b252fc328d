"""
图片预览和网格展示组件
"""
import os
import requests
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QScrollArea,
    QLabel, QPushButton, QCheckBox, QFrame, QSizePolicy, QMenu,
    QMessageBox, QProgressBar, QSpacerItem
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSize, QTimer
from PyQt6.QtGui import QPixmap, QAction, QFont, QPainter, QPen, QGuiApplication


class ImageLoadWorker(QThread):
    """图片加载工作线程"""
    image_loaded = pyqtSignal(int, QPixmap)  # image_id, pixmap
    load_failed = pyqtSignal(int, str)  # image_id, error_message

    def __init__(self, image_id: int, image_url: str):
        super().__init__()
        self.image_id = image_id
        self.image_url = image_url

    def run(self):
        try:
            response = requests.get(self.image_url, timeout=10)
            response.raise_for_status()

            if len(response.content) == 0:
                self.load_failed.emit(self.image_id, "图片文件为空")
                return

            pixmap = QPixmap()
            if pixmap.loadFromData(response.content):
                self.image_loaded.emit(self.image_id, pixmap)
            else:
                self.load_failed.emit(self.image_id, "图片格式不支持或数据损坏")
        except requests.exceptions.Timeout:
            self.load_failed.emit(self.image_id, "请求超时")
        except requests.exceptions.ConnectionError:
            self.load_failed.emit(self.image_id, "连接失败")
        except requests.exceptions.HTTPError as e:
            self.load_failed.emit(self.image_id, f"HTTP错误: {e}")
        except Exception as e:
            self.load_failed.emit(self.image_id, f"未知错误: {str(e)}")


class ImageThumbnail(QFrame):
    """图片缩略图组件"""
    clicked = pyqtSignal(int)  # image_id
    selection_changed = pyqtSignal(int, bool)  # image_id, selected
    context_menu_requested = pyqtSignal(int, object)  # image_id, position

    def __init__(self, image_data: Dict[str, Any], thumbnail_size: int = 180):
        super().__init__()
        self.image_data = image_data
        self.image_id = image_data['id']
        self.thumbnail_size = thumbnail_size
        self.is_selected = False
        self.pixmap = None
        self.load_worker = None

        # 根据屏幕大小调整缩略图尺寸
        screen_width = QGuiApplication.primaryScreen().availableGeometry().width()
        if screen_width <= 1280:
            self.thumbnail_size = min(thumbnail_size, 150)  # 小屏幕使用更小的缩略图

        self.setFixedSize(self.thumbnail_size + 20, self.thumbnail_size + 80)
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet("""
            QFrame {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                background-color: #ffffff;
                margin: 3px;
            }
            QFrame:hover {
                border-color: #0d6efd;
            }
        """)

        self._setup_ui()
        # 不立即加载图片，等待懒加载触发

    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # 选择框
        self.checkbox = QCheckBox()
        self.checkbox.stateChanged.connect(self._on_selection_changed)

        # 图片显示区域
        self.image_label = QLabel()
        self.image_label.setFixedSize(self.thumbnail_size, self.thumbnail_size)
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet("""
            QLabel {
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: #f8f9fa;
                color: #6c757d;
            }
        """)
        self.image_label.setText("加载中...")

        # 图片信息
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)

        # 文件名
        filename = self.image_data.get('filename', 'Unknown')
        if len(filename) > 20:
            filename = filename[:17] + "..."
        self.filename_label = QLabel(filename)
        self.filename_label.setFont(QFont("Arial", 9, QFont.Weight.Bold))
        self.filename_label.setStyleSheet("color: #333333;")

        # 尺寸和类型信息
        width = self.image_data.get('width', 0)
        height = self.image_data.get('height', 0)
        gen_type = self.image_data.get('generation_type', 'unknown')
        info_text = f"{width}×{height} | {gen_type}"
        self.info_label = QLabel(info_text)
        self.info_label.setFont(QFont("Arial", 8))
        self.info_label.setStyleSheet("color: #6c757d;")

        info_layout.addWidget(self.filename_label)
        info_layout.addWidget(self.info_label)

        # 添加到主布局
        top_layout = QHBoxLayout()
        top_layout.addWidget(self.checkbox)
        top_layout.addStretch()

        layout.addLayout(top_layout)
        layout.addWidget(self.image_label)
        layout.addLayout(info_layout)

    def _load_image(self):
        """加载图片"""
        # 如果已经在加载中或已经加载完成，跳过
        if (self.load_worker and self.load_worker.isRunning()) or self.pixmap is not None:
            return

        # 更新显示状态
        self.image_label.setText("加载中...")
        self.image_label.setStyleSheet("""
            QLabel {
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: #f8f9fa;
                color: #6c757d;
            }
        """)

        # 构建图片URL
        image_url = f"http://localhost:8000/api/v1/images/{self.image_id}/file"

        # 启动图片加载工作线程
        self.load_worker = ImageLoadWorker(self.image_id, image_url)
        self.load_worker.image_loaded.connect(self._on_image_loaded)
        self.load_worker.load_failed.connect(self._on_load_failed)
        self.load_worker.start()

    def _on_image_loaded(self, image_id: int, pixmap: QPixmap):
        """图片加载完成"""
        if image_id == self.image_id:
            self.pixmap = pixmap
            # 缩放图片以适应缩略图大小
            scaled_pixmap = pixmap.scaled(
                self.thumbnail_size, self.thumbnail_size,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pixmap)

    def _on_load_failed(self, image_id: int, error_message: str):
        """图片加载失败"""
        if image_id == self.image_id:
            self.image_label.setText("加载失败")
            self.image_label.setStyleSheet("""
                QLabel {
                    border: 1px solid #ced4da;
                    border-radius: 4px;
                    background-color: #f8f9fa;
                    color: #dc3545;
                }
            """)

    def _on_selection_changed(self, state):
        """选择状态改变"""
        self.is_selected = state == Qt.CheckState.Checked.value
        self.selection_changed.emit(self.image_id, self.is_selected)
        self._update_style()

    def _update_style(self):
        """更新样式"""
        if self.is_selected:
            self.setStyleSheet("""
                QFrame {
                    border: 2px solid #0d6efd;
                    border-radius: 8px;
                    background-color: #ffffff;
                    margin: 5px;
                }
            """)
        else:
            self.setStyleSheet("""
                QFrame {
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    background-color: #ffffff;
                    margin: 5px;
                }
                QFrame:hover {
                    border-color: #0d6efd;
                }
            """)

    def set_selected(self, selected: bool):
        """设置选择状态"""
        self.checkbox.setChecked(selected)

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.image_id)
        elif event.button() == Qt.MouseButton.RightButton:
            self.context_menu_requested.emit(self.image_id, event.globalPosition().toPoint())
        super().mousePressEvent(event)


class ImageGridWidget(QWidget):
    """图片网格展示组件"""
    image_selected = pyqtSignal(int)  # image_id
    images_selection_changed = pyqtSignal(list)  # selected_image_ids

    def __init__(self, parent=None):
        super().__init__(parent)
        self.images_data = []
        self.thumbnails = {}  # image_id -> ImageThumbnail
        self.selected_images = set()
        self.columns = 4
        self.thumbnail_size = 180
        self.lazy_load_timer = QTimer()
        self.lazy_load_timer.setSingleShot(True)
        self.lazy_load_timer.timeout.connect(self._load_visible_images)

        self._setup_ui()

        # 监听窗口大小变化以调整列数
        self.resize_timer = QTimer()
        self.resize_timer.setSingleShot(True)
        self.resize_timer.timeout.connect(self._adjust_columns)

    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setFrameStyle(QFrame.Shape.NoFrame)

        # 设置滚动区域的最小尺寸
        self.scroll_area.setMinimumSize(300, 200)

        # 创建网格容器
        self.grid_widget = QWidget()
        self.grid_layout = QGridLayout(self.grid_widget)
        self.grid_layout.setSpacing(8)  # 减小间距以适应小屏幕
        self.grid_layout.setContentsMargins(8, 8, 8, 8)

        # 设置网格布局的对齐方式
        self.grid_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)

        self.scroll_area.setWidget(self.grid_widget)

        # 连接滚动事件以实现懒加载
        self.scroll_area.verticalScrollBar().valueChanged.connect(self._on_scroll)

        layout.addWidget(self.scroll_area)

    def set_images(self, images_data: List[Dict[str, Any]]):
        """设置图片数据"""
        self.clear_images()
        self.images_data = images_data
        self._create_thumbnails()

    def clear_images(self):
        """清空图片"""
        # 清理现有的缩略图
        for thumbnail in self.thumbnails.values():
            thumbnail.deleteLater()

        self.thumbnails.clear()
        self.selected_images.clear()

        # 清理布局
        while self.grid_layout.count():
            child = self.grid_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def _create_thumbnails(self):
        """创建缩略图"""
        for i, image_data in enumerate(self.images_data):
            thumbnail = ImageThumbnail(image_data, self.thumbnail_size)
            thumbnail.clicked.connect(self._on_thumbnail_clicked)
            thumbnail.selection_changed.connect(self._on_selection_changed)
            thumbnail.context_menu_requested.connect(self._on_context_menu)

            row = i // self.columns
            col = i % self.columns
            self.grid_layout.addWidget(thumbnail, row, col)

            self.thumbnails[image_data['id']] = thumbnail

        # 创建缩略图后立即触发可见图片加载
        QTimer.singleShot(100, self._load_visible_images)

    def _on_thumbnail_clicked(self, image_id: int):
        """缩略图点击事件"""
        self.image_selected.emit(image_id)

    def _on_selection_changed(self, image_id: int, selected: bool):
        """选择状态改变"""
        if selected:
            self.selected_images.add(image_id)
        else:
            self.selected_images.discard(image_id)

        self.images_selection_changed.emit(list(self.selected_images))

    def _on_context_menu(self, image_id: int, position):
        """右键菜单"""
        menu = QMenu(self)

        view_action = QAction("查看详情", self)
        view_action.triggered.connect(lambda: self.image_selected.emit(image_id))
        menu.addAction(view_action)

        menu.addSeparator()

        select_action = QAction("选择", self)
        select_action.triggered.connect(lambda: self._toggle_selection(image_id))
        menu.addAction(select_action)

        menu.exec(position)

    def _toggle_selection(self, image_id: int):
        """切换选择状态"""
        if image_id in self.thumbnails:
            thumbnail = self.thumbnails[image_id]
            thumbnail.set_selected(not thumbnail.is_selected)

    def select_all(self):
        """全选"""
        for thumbnail in self.thumbnails.values():
            thumbnail.set_selected(True)

    def select_none(self):
        """取消全选"""
        for thumbnail in self.thumbnails.values():
            thumbnail.set_selected(False)

    def get_selected_images(self) -> List[int]:
        """获取选中的图片ID列表"""
        return list(self.selected_images)

    def set_columns(self, columns: int):
        """设置列数"""
        if columns != self.columns:
            self.columns = columns
            self._relayout_thumbnails()

    def _relayout_thumbnails(self):
        """重新布局缩略图"""
        # 移除所有widget但不删除
        thumbnails_list = []
        while self.grid_layout.count():
            item = self.grid_layout.takeAt(0)
            if item.widget():
                thumbnails_list.append(item.widget())

        # 重新添加到网格
        for i, thumbnail in enumerate(thumbnails_list):
            row = i // self.columns
            col = i % self.columns
            self.grid_layout.addWidget(thumbnail, row, col)

    def _on_scroll(self):
        """滚动事件处理"""
        # 延迟加载可见图片
        self.lazy_load_timer.stop()
        self.lazy_load_timer.start(100)  # 100ms延迟

    def _load_visible_images(self):
        """加载可见区域的图片"""
        if not self.thumbnails:
            return

        # 获取滚动区域的可见范围
        scroll_area_rect = self.scroll_area.viewport().rect()
        scroll_value = self.scroll_area.verticalScrollBar().value()

        # 计算可见区域在grid_widget中的位置
        visible_top = scroll_value
        visible_bottom = scroll_value + scroll_area_rect.height()

        # 添加一些缓冲区域，提前加载即将可见的图片
        buffer_height = 200
        visible_top = max(0, visible_top - buffer_height)
        visible_bottom += buffer_height

        # 为每个缩略图检查是否在可见区域内
        for thumbnail in self.thumbnails.values():
            if thumbnail.pixmap is None:  # 只加载未加载的图片
                # 确保thumbnail已经被正确布局
                if not thumbnail.isVisible():
                    continue

                thumbnail_rect = thumbnail.geometry()
                thumbnail_top = thumbnail_rect.top()
                thumbnail_bottom = thumbnail_rect.bottom()

                # 检查是否与可见区域相交
                if (thumbnail_top <= visible_bottom and thumbnail_bottom >= visible_top):
                    thumbnail._load_image()

    def _adjust_columns(self):
        """根据窗口宽度调整列数"""
        if not self.scroll_area:
            return

        available_width = self.scroll_area.viewport().width() - 20  # 减去边距
        min_thumbnail_width = self.thumbnail_size + 20  # 缩略图宽度加边距

        new_columns = max(1, available_width // min_thumbnail_width)
        if new_columns != self.columns:
            self.columns = new_columns
            self._relayout_thumbnails()

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 延迟调整列数，避免频繁重新布局
        self.resize_timer.stop()
        self.resize_timer.start(200)
        # 窗口大小改变后也需要重新检查可见图片
        QTimer.singleShot(400, self._load_visible_images)

    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)
        # 首次显示时调整列数并加载可见图片
        QTimer.singleShot(100, self._adjust_columns)
        QTimer.singleShot(300, self._load_visible_images)