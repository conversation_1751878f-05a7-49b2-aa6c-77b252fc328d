# Stable Diffusion权重文件管理指南

## 概述

本指南介绍如何在图片生成工具中使用Stable Diffusion权重文件（.safetensors格式）功能。

## 功能特性

### ✅ 已实现功能

1. **safetensors文件支持**
   - 支持加载和验证.safetensors格式的SD权重文件
   - 自动检测文件格式和模型类型
   - 文件完整性验证

2. **模型管理**
   - 添加、删除、重命名自定义SD权重模型
   - 模型列表管理和状态显示
   - 模型缓存机制

3. **前端界面集成**
   - AI生成界面中的SD权重管理按钮
   - 专用的SD权重管理对话框
   - 模型状态实时显示

4. **数据管理集成**
   - 在数据管理模块显示当前使用的模型信息
   - 模型类型区分（传统AI模型 vs SD权重模型）

5. **API接口**
   - RESTful API支持模型管理操作
   - 模型加载、验证、切换接口

## 使用方法

### 1. 添加SD权重文件

#### 方法一：通过界面添加
1. 在AI生成标签页中，点击"SD权重"按钮
2. 在弹出的对话框中，切换到"添加模型"标签页
3. 填写模型信息：
   - **模型键名**：唯一标识符（如：my_custom_model）
   - **模型名称**：显示名称
   - **文件路径**：选择.safetensors文件
   - **描述**：可选的模型描述
4. 点击"验证文件"确认文件有效性
5. 点击"添加模型"完成添加

#### 方法二：自动检测
1. 将.safetensors文件放置在项目目录中
2. 在SD权重管理对话框中，切换到"自动检测"标签页
3. 点击"开始检测"自动扫描文件
4. 对检测到的文件点击"添加"按钮

### 2. 使用SD权重模型

1. 在AI生成界面的模型选择下拉框中选择SD权重模型
2. SD权重模型会以蓝色标识显示
3. 点击"切换模型"按钮加载选中的模型
4. 模型状态会在界面中实时显示

### 3. 管理SD权重模型

#### 查看模型列表
- 在SD权重管理对话框的"模型列表"标签页中查看所有模型
- 显示模型名称、类型、描述、状态等信息

#### 加载模型
- 在模型列表中点击"加载"按钮
- 支持强制重新加载选项

#### 清理缓存
- 点击"清理缓存"按钮清理所有模型缓存
- 释放内存空间

### 4. 监控模型状态

#### AI生成界面
- 模型状态标签显示当前加载的模型
- 绿色：传统AI模型
- 蓝色：SD权重模型
- 灰色：未加载

#### 数据管理界面
- 显示当前使用的模型信息
- 自动刷新模型状态
- 支持手动刷新

## 技术规格

### 支持的文件格式
- **.safetensors**：推荐格式，安全且高效
- 文件大小：建议1MB以上
- 模型类型：Stable Diffusion兼容模型

### 系统要求
- **内存**：建议8GB以上
- **显卡**：支持CUDA的NVIDIA显卡（推荐）
- **存储**：足够空间存储模型文件（通常几GB）

### 缓存机制
- 自动缓存已加载的模型
- 基于文件哈希的缓存验证
- 支持手动清理缓存

## API接口

### 获取可用模型
```
GET /api/v1/sd-models/available
```

### 获取当前模型
```
GET /api/v1/sd-models/current
```

### 加载模型
```
POST /api/v1/sd-models/load
{
  "model_key": "model_name",
  "force_reload": false
}
```

### 添加safetensors模型
```
POST /api/v1/sd-models/add-safetensors
{
  "model_key": "unique_key",
  "model_name": "Display Name",
  "safetensors_path": "/path/to/model.safetensors",
  "description": "Optional description"
}
```

### 验证safetensors文件
```
POST /api/v1/sd-models/validate-safetensors?file_path=/path/to/file.safetensors
```

### 清理缓存
```
DELETE /api/v1/sd-models/cache/{model_key}  # 清理指定模型
DELETE /api/v1/sd-models/cache              # 清理所有缓存
```

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查文件路径是否正确
   - 确认文件格式为.safetensors
   - 检查系统内存是否充足
   - 验证CUDA环境（如使用GPU）

2. **文件验证失败**
   - 确认文件完整性
   - 检查文件大小（应大于1MB）
   - 验证是否为有效的SD模型

3. **界面显示异常**
   - 刷新模型列表
   - 重启应用程序
   - 检查日志文件

### 日志查看
- 应用日志位置：`logs/app.log`
- 关键日志标识：`sd_weight_loader`、`custom_model_manager`

### 性能优化建议
1. 使用SSD存储模型文件
2. 确保充足的系统内存
3. 定期清理不需要的模型缓存
4. 使用GPU加速（如可用）

## 注意事项

1. **文件安全**：只使用来源可信的.safetensors文件
2. **版权合规**：确保使用的模型符合相关许可协议
3. **资源管理**：大型模型会占用大量内存，注意系统资源
4. **备份重要**：建议备份重要的自定义模型文件

## 更新日志

### v2.0.0
- ✅ 新增SD权重文件支持
- ✅ 实现模型管理界面
- ✅ 添加缓存机制
- ✅ 集成API接口
- ✅ 数据管理模块显示模型信息

---

如有问题或建议，请查看项目文档或联系开发团队。
