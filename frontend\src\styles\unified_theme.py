"""
统一主题样式定义 - 现代化浅色主题
支持所有界面的一致性设计
"""

# 颜色方案定义
COLORS = {
    # 主色调
    'primary': '#0d6efd',
    'primary_hover': '#0b5ed7', 
    'primary_pressed': '#0a58ca',
    'primary_light': '#e7f1ff',
    
    # 辅助色
    'success': '#198754',
    'success_hover': '#157347',
    'success_light': '#d1e7dd',
    
    'danger': '#dc3545',
    'danger_hover': '#bb2d3b',
    'danger_light': '#f8d7da',
    
    'warning': '#ffc107',
    'warning_hover': '#ffca2c',
    'warning_light': '#fff3cd',
    
    'info': '#0dcaf0',
    'info_hover': '#3dd5f3',
    'info_light': '#cff4fc',
    
    'secondary': '#6c757d',
    'secondary_hover': '#5c636a',
    'secondary_light': '#e2e3e5',
    
    # 背景色
    'bg_primary': '#ffffff',
    'bg_secondary': '#f8f9fa',
    'bg_tertiary': '#e9ecef',
    'bg_dark': '#343a40',
    
    # 文本色
    'text_primary': '#212529',
    'text_secondary': '#6c757d',
    'text_muted': '#adb5bd',
    'text_light': '#ffffff',
    
    # 边框色
    'border_light': '#e9ecef',
    'border_medium': '#ced4da',
    'border_dark': '#adb5bd',
    
    # 状态色
    'disabled_bg': '#e9ecef',
    'disabled_text': '#6c757d',
    'focus_border': '#86b7fe',
    'hover_bg': '#f8f9fa',
}

# 字体配置
FONTS = {
    'family_primary': '"Microsoft YaHei", "Segoe UI", sans-serif',
    'family_mono': '"Consolas", "Monaco", monospace',
    'size_xs': '8pt',
    'size_sm': '9pt', 
    'size_base': '10pt',
    'size_lg': '11pt',
    'size_xl': '12pt',
    'size_xxl': '14pt',
    'weight_normal': '400',
    'weight_medium': '500',
    'weight_bold': '600',
}

# 间距配置
SPACING = {
    'xs': '4px',
    'sm': '6px',
    'base': '8px',
    'md': '10px',
    'lg': '12px',
    'xl': '16px',
    'xxl': '20px',
    'xxxl': '24px',
}

# 圆角配置
RADIUS = {
    'sm': '3px',
    'base': '4px',
    'md': '6px',
    'lg': '8px',
    'xl': '12px',
    'round': '50%',
}

# 阴影配置
SHADOWS = {
    'sm': '0 1px 2px rgba(0, 0, 0, 0.05)',
    'base': '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
    'md': '0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06)',
    'lg': '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)',
}

def get_unified_style():
    """获取统一的样式字符串"""
    return f"""
/* 基础样式 */
QWidget {{
    color: {COLORS['text_primary']};
    background-color: {COLORS['bg_primary']};
    font-size: {FONTS['size_lg']};
    font-family: {FONTS['family_primary']};
    font-weight: {FONTS['weight_normal']};
}}

QMainWindow {{
    background-color: {COLORS['bg_secondary']};
}}

/* 标签页样式 */
QTabWidget::pane {{
    border: 1px solid {COLORS['border_light']};
    border-radius: {RADIUS['base']};
    margin-top: 5px;
    background-color: {COLORS['bg_primary']};
}}

QTabBar::tab {{
    background: {COLORS['bg_secondary']};
    border: 1px solid {COLORS['border_light']};
    border-bottom-color: {COLORS['bg_primary']};
    padding: {SPACING['md']} {SPACING['xxl']};
    margin-right: 2px;
    border-top-left-radius: {RADIUS['base']};
    border-top-right-radius: {RADIUS['base']};
    color: {COLORS['text_secondary']};
    font-weight: {FONTS['weight_normal']};
    min-width: 80px;
}}

QTabBar::tab:selected {{
    background: {COLORS['bg_primary']};
    border-color: {COLORS['border_light']};
    border-bottom-color: {COLORS['bg_primary']};
    color: {COLORS['text_primary']};
    font-weight: {FONTS['weight_medium']};
}}

QTabBar::tab:hover {{
    background: {COLORS['hover_bg']};
    color: {COLORS['text_primary']};
}}

/* 分组框样式 */
QGroupBox {{
    background-color: {COLORS['bg_primary']};
    border: 1px solid {COLORS['border_light']};
    border-radius: {RADIUS['md']};
    margin-top: {SPACING['lg']};
    padding-top: {SPACING['md']};
    font-weight: {FONTS['weight_medium']};
    font-size: {FONTS['size_base']};
    color: {COLORS['text_primary']};
    font-family: {FONTS['family_primary']};
}}

QGroupBox::title {{
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: {SPACING['sm']} {SPACING['md']};
    background-color: transparent;
    border-radius: {RADIUS['sm']};
    color: {COLORS['text_primary']};
    font-weight: {FONTS['weight_medium']};
}}

/* 标签样式 */
QLabel {{
    color: {COLORS['text_primary']};
    font-size: {FONTS['size_base']};
    padding: {SPACING['xs']};
    font-family: {FONTS['family_primary']};
}}

/* 输入控件统一样式 */
QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
    background-color: {COLORS['bg_primary']};
    padding: {SPACING['base']} {SPACING['lg']};
    border: 1px solid {COLORS['border_medium']};
    border-radius: {RADIUS['base']};
    font-size: {FONTS['size_base']};
    color: {COLORS['text_primary']};
    font-family: {FONTS['family_primary']};
    min-height: 20px;
}}

QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
    border: 2px solid {COLORS['focus_border']};
    outline: none;
    background-color: {COLORS['bg_primary']};
}}

QLineEdit:hover, QTextEdit:hover, QSpinBox:hover, QDoubleSpinBox:hover, QComboBox:hover {{
    border-color: {COLORS['border_dark']};
}}

QLineEdit:disabled, QTextEdit:disabled, QSpinBox:disabled, QDoubleSpinBox:disabled, QComboBox:disabled {{
    background-color: {COLORS['disabled_bg']};
    color: {COLORS['disabled_text']};
    border-color: {COLORS['border_light']};
}}

/* 按钮基础样式 */
QPushButton {{
    background-color: {COLORS['primary']};
    color: {COLORS['text_light']};
    font-weight: {FONTS['weight_medium']};
    border: none;
    border-radius: {RADIUS['base']};
    padding: {SPACING['base']} {SPACING['xl']};
    font-size: {FONTS['size_base']};
    min-width: 60px;
    min-height: 28px;
    font-family: {FONTS['family_primary']};
}}

QPushButton:hover {{
    background-color: {COLORS['primary_hover']};
}}

QPushButton:pressed {{
    background-color: {COLORS['primary_pressed']};
}}

QPushButton:disabled {{
    background-color: {COLORS['disabled_bg']};
    color: {COLORS['disabled_text']};
}}

/* 单选按钮样式 */
QRadioButton {{
    spacing: {SPACING['base']};
    font-size: {FONTS['size_base']};
    padding: {SPACING['xs']};
    color: {COLORS['text_primary']};
    font-family: {FONTS['family_primary']};
}}

QRadioButton::indicator {{
    width: 16px;
    height: 16px;
    border: 2px solid {COLORS['border_medium']};
    border-radius: 10px;
    background-color: {COLORS['bg_primary']};
}}

QRadioButton::indicator:checked {{
    background-color: {COLORS['primary']};
    border-color: {COLORS['primary']};
}}

QRadioButton::indicator:hover {{
    border-color: {COLORS['primary_hover']};
}}

QRadioButton::indicator:disabled {{
    background-color: {COLORS['disabled_bg']};
    border-color: {COLORS['border_light']};
}}

/* 复选框样式 */
QCheckBox {{
    spacing: {SPACING['base']};
    font-size: {FONTS['size_base']};
    padding: {SPACING['xs']};
    color: {COLORS['text_primary']};
    font-family: {FONTS['family_primary']};
}}

QCheckBox::indicator {{
    width: 16px;
    height: 16px;
    border: 2px solid {COLORS['border_medium']};
    border-radius: {RADIUS['sm']};
    background-color: {COLORS['bg_primary']};
}}

QCheckBox::indicator:checked {{
    background-color: {COLORS['primary']};
    border-color: {COLORS['primary']};
}}

QCheckBox::indicator:hover {{
    border-color: {COLORS['primary_hover']};
}}

QCheckBox::indicator:disabled {{
    background-color: {COLORS['disabled_bg']};
    border-color: {COLORS['border_light']};
}}

/* 下拉框样式 */
QComboBox::drop-down {{
    border: none;
    background: transparent;
    width: 20px;
}}

QComboBox::down-arrow {{
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid {COLORS['text_secondary']};
    width: 0;
    height: 0;
}}

QComboBox QAbstractItemView {{
    background-color: {COLORS['bg_primary']};
    border: 1px solid {COLORS['border_medium']};
    border-radius: {RADIUS['base']};
    selection-background-color: {COLORS['primary_light']};
    selection-color: {COLORS['primary']};
}}

/* 滑块样式 */
QSlider::groove:horizontal {{
    border: 1px solid {COLORS['border_medium']};
    height: 6px;
    background: {COLORS['bg_secondary']};
    border-radius: 3px;
}}

QSlider::handle:horizontal {{
    background: {COLORS['primary']};
    border: 1px solid {COLORS['primary']};
    width: 18px;
    height: 18px;
    border-radius: 9px;
    margin: -7px 0;
}}

QSlider::handle:horizontal:hover {{
    background: {COLORS['primary_hover']};
    border-color: {COLORS['primary_hover']};
}}

QSlider::handle:horizontal:disabled {{
    background: {COLORS['disabled_bg']};
    border-color: {COLORS['border_light']};
}}

/* 进度条样式 */
QProgressBar {{
    border: 1px solid {COLORS['border_light']};
    border-radius: {RADIUS['base']};
    text-align: center;
    background-color: {COLORS['bg_secondary']};
    color: {COLORS['text_primary']};
    font-weight: {FONTS['weight_medium']};
    font-family: {FONTS['family_primary']};
    min-height: 20px;
}}

QProgressBar::chunk {{
    background-color: {COLORS['primary']};
    border-radius: {RADIUS['sm']};
}}

/* 滚动条样式 */
QScrollBar:vertical {{
    background: {COLORS['bg_secondary']};
    width: 12px;
    border-radius: 6px;
    margin: 0;
}}

QScrollBar::handle:vertical {{
    background: {COLORS['border_medium']};
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}}

QScrollBar::handle:vertical:hover {{
    background: {COLORS['border_dark']};
}}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
    height: 0px;
}}

QScrollBar:horizontal {{
    background: {COLORS['bg_secondary']};
    height: 12px;
    border-radius: 6px;
    margin: 0;
}}

QScrollBar::handle:horizontal {{
    background: {COLORS['border_medium']};
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}}

QScrollBar::handle:horizontal:hover {{
    background: {COLORS['border_dark']};
}}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
    width: 0px;
}}

/* 滚动区域样式 */
QScrollArea {{
    border: 1px solid {COLORS['border_light']};
    border-radius: {RADIUS['base']};
    background-color: {COLORS['bg_primary']};
}}

/* 列表控件样式 */
QListWidget {{
    background-color: {COLORS['bg_primary']};
    border: 1px solid {COLORS['border_light']};
    border-radius: {RADIUS['base']};
    padding: {SPACING['sm']};
    font-family: {FONTS['family_primary']};
}}

QListWidget::item {{
    background-color: transparent;
    border: none;
    padding: {SPACING['base']};
    border-radius: {RADIUS['sm']};
    color: {COLORS['text_primary']};
    margin: 1px;
}}

QListWidget::item:selected {{
    background-color: {COLORS['primary_light']};
    color: {COLORS['primary']};
}}

QListWidget::item:hover {{
    background-color: {COLORS['hover_bg']};
}}

/* 树形控件样式 */
QTreeWidget {{
    background-color: {COLORS['bg_primary']};
    border: 1px solid {COLORS['border_light']};
    border-radius: {RADIUS['base']};
    padding: {SPACING['sm']};
    font-family: {FONTS['family_primary']};
}}

QTreeWidget::item {{
    background-color: transparent;
    border: none;
    padding: {SPACING['sm']};
    color: {COLORS['text_primary']};
}}

QTreeWidget::item:selected {{
    background-color: {COLORS['primary_light']};
    color: {COLORS['primary']};
}}

QTreeWidget::item:hover {{
    background-color: {COLORS['hover_bg']};
}}

/* 框架和分割器样式 */
QFrame {{
    border: none;
    background-color: transparent;
}}

QSplitter::handle {{
    background-color: {COLORS['border_light']};
}}

QSplitter::handle:horizontal {{
    width: 2px;
}}

QSplitter::handle:vertical {{
    height: 2px;
}}

/* 工具提示样式 */
QToolTip {{
    background-color: {COLORS['bg_dark']};
    color: {COLORS['text_light']};
    border: 1px solid {COLORS['text_secondary']};
    border-radius: {RADIUS['base']};
    padding: {SPACING['sm']} {SPACING['base']};
    font-size: {FONTS['size_sm']};
    font-family: {FONTS['family_primary']};
}}

/* 旋转框样式 */
QSpinBox::up-button, QDoubleSpinBox::up-button {{
    background-color: {COLORS['bg_secondary']};
    border: 1px solid {COLORS['border_medium']};
    border-radius: {RADIUS['sm']};
    width: 16px;
}}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {{
    background-color: {COLORS['hover_bg']};
}}

QSpinBox::down-button, QDoubleSpinBox::down-button {{
    background-color: {COLORS['bg_secondary']};
    border: 1px solid {COLORS['border_medium']};
    border-radius: {RADIUS['sm']};
    width: 16px;
}}

QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {{
    background-color: {COLORS['hover_bg']};
}}

/* 消息框样式 */
QMessageBox {{
    background-color: {COLORS['bg_primary']};
    color: {COLORS['text_primary']};
}}

QMessageBox QPushButton {{
    min-width: 80px;
    padding: {SPACING['sm']} {SPACING['lg']};
}}
"""

# 为了兼容现有代码，保留MINIMAL_STYLE变量
MINIMAL_STYLE = get_unified_style()
