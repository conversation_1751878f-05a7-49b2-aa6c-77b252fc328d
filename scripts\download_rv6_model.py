#!/usr/bin/env python3
"""
手动下载Realistic Vision V6模型
"""

import os
import sys
import requests
from pathlib import Path
from tqdm import tqdm

# 定义代理设置
# 如果你的代理地址是 127.0.0.1:7890，则设置为以下形式
# 如果你的代理需要认证，可以写成 '*************************:port'
PROXIES = {
    'http': 'http://127.0.0.1:7890',
    'https': 'http://127.0.0.1:7890',
}

def download_file(url, local_path, chunk_size=8192):
    """下载文件并显示进度条"""
    try:
        # 在requests.get方法中添加proxies参数
        response = requests.get(url, stream=True, proxies=PROXIES)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(local_path, 'wb') as f, tqdm(
            desc=local_path.name,
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    f.write(chunk)
                    pbar.update(len(chunk))
        
        print(f"✅ 下载完成: {local_path}")
        return True
        
    except requests.exceptions.ProxyError as e:
        print(f"❌ 代理连接失败: {str(e)}")
        print("请检查代理服务 (例如 Clash 或 V2Ray) 是否正在运行，并且地址和端口是否正确 (127.0.0.1:7890)。")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 下载失败 (网络或请求错误): {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 下载失败 (未知错误): {str(e)}")
        return False

def download_rv6_model():
    """下载Realistic Vision V6模型"""
    print("🚀 开始下载Realistic Vision V6模型...")
    
    # 创建模型目录
    model_dir = Path("models/realistic-vision-v6")
    model_dir.mkdir(parents=True, exist_ok=True)
    
    # 模型文件列表
    files_to_download = [
        {
            "url": "https://huggingface.co/SG161222/Realistic_Vision_V6.0_B1_noVAE/resolve/main/Realistic_Vision_V6.0_NV_B1_fp16.safetensors",
            "filename": "Realistic_Vision_V6.0_NV_B1_fp16.safetensors",
            "size": "2.13 GB",
            "description": "主模型文件 (FP16版本，推荐)"
        },
        {
            "url": "https://huggingface.co/SG161222/Realistic_Vision_V6.0_B1_noVAE/resolve/main/Realistic_Vision_V6.0_NV_B1.safetensors",
            "filename": "Realistic_Vision_V6.0_NV_B1.safetensors",
            "size": "4.27 GB",
            "description": "主模型文件 (完整版本)"
        }
    ]
    
    print(f"📁 模型将保存到: {model_dir.absolute()}")
    print("\n📋 可下载的文件:")
    for i, file_info in enumerate(files_to_download, 1):
        print(f"  {i}. {file_info['filename']} ({file_info['size']}) - {file_info['description']}")
    
    # 让用户选择要下载的文件
    print("\n请选择要下载的文件:")
    print("1. 仅下载FP16版本 (推荐，节省空间)")
    print("2. 仅下载完整版本")
    print("3. 下载两个版本")
    print("0. 退出")
    
    try:
        choice = input("\n请输入选择 (0-3): ").strip()
        
        if choice == "0":
            print("👋 退出下载")
            return
        elif choice == "1":
            files_to_download = [files_to_download[0]]  # 仅FP16版本
        elif choice == "2":
            files_to_download = [files_to_download[1]]  # 仅完整版本
        elif choice == "3":
            pass  # 下载所有文件
        else:
            print("❌ 无效选择")
            return
        
        # 开始下载
        success_count = 0
        for file_info in files_to_download:
            local_path = model_dir / file_info["filename"]
            
            # 检查文件是否已存在
            if local_path.exists():
                print(f"⚠️  文件已存在: {local_path}")
                overwrite = input("是否覆盖? (y/N): ").strip().lower()
                if overwrite != 'y':
                    print("跳过下载")
                    continue
            
            print(f"\n📥 下载: {file_info['filename']} ({file_info['size']})")
            if download_file(file_info["url"], local_path):
                success_count += 1
        
        print(f"\n📊 下载完成! 成功: {success_count}/{len(files_to_download)}")
        
        if success_count > 0:
            print("\n✅ 模型下载完成!")
            print("现在可以在AI生成工具中使用Realistic Vision V6模型了。")
            print(f"模型位置: {model_dir.absolute()}")
            
            # 创建一个简单的配置文件
            config_file = model_dir / "model_info.txt"
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write("Realistic Vision V6.0 B1 (noVAE)\n")
                f.write("模型类型: Stable Diffusion 1.5\n")
                f.write("用途: 现实主义图像生成\n")
                f.write("特点: 极小目标生成效果更佳\n")
                f.write(f"下载时间: {__import__('datetime').datetime.now()}\n")
            
            print(f"📝 模型信息已保存到: {config_file}")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  下载被用户中断")
    except Exception as e:
        print(f"\n❌ 下载过程中出现错误: {str(e)}")

if __name__ == "__main__":
    print("🎨 Realistic Vision V6 模型下载工具")
    print("=" * 50)
    
    # 检查网络连接（通过代理）
    try:
        # 检查Hugging Face网站是否可通过代理访问
        response = requests.get("https://huggingface.co", timeout=10, proxies=PROXIES)
        if response.status_code == 200:
            print("✅ 网络连接正常 (通过代理)")
        else:
            print(f"⚠️  网络连接可能有问题 (通过代理，状态码: {response.status_code})")
    except requests.exceptions.ProxyError as e:
        print(f"❌ 代理连接失败: {str(e)}")
        print("请检查代理服务 (例如 Clash 或 V2Ray) 是否正在运行，并且地址和端口是否正确 (127.0.0.1:7890)。")
        sys.exit(1)
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Hugging Face (通过代理): {str(e)}")
        print("请检查代理设置或网络连接。")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 发生未知错误: {str(e)}")
        sys.exit(1)
    
    download_rv6_model()
