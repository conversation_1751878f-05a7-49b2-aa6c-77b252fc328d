#!/usr/bin/env python3
"""
图片生成工具前端应用启动文件
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from main_window import MainWindow

def main():
    """启动前端应用"""
    # 确保工作目录是项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))  # frontend/src
    project_root = os.path.dirname(os.path.dirname(current_dir))  # 项目根目录
    os.chdir(project_root)

    app = QApplication(sys.argv)

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()