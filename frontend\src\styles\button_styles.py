"""
按钮样式统一管理 - 与统一主题兼容
"""

from PyQt6.QtWidgets import QPushButton
from PyQt6.QtCore import QSize
from PyQt6.QtGui import QFont, QFontMetrics
from .unified_theme import COLORS, FONTS, SPACING, RADIUS

class ButtonStyleManager:
    """按钮样式管理器 - 统一主题风格"""

    # 基础样式 - 统一尺寸、字体、边框等
    BASE_STYLE = f"""
        QPushButton {{
            font-family: {FONTS['family_primary']};
            font-weight: {FONTS['weight_medium']};
            border: none;
            border-radius: {RADIUS['base']};
            text-align: center;
            font-size: {FONTS['size_base']};
            padding: {SPACING['base']} {SPACING['xl']};
            min-height: 28px;
        }}
        QPushButton:disabled {{
            background-color: {COLORS['disabled_bg']};
            color: {COLORS['disabled_text']};
        }}
    """

    # 主要按钮样式（蓝色）
    PRIMARY_STYLE = BASE_STYLE + f"""
        QPushButton {{
            background-color: {COLORS['primary']};
            color: {COLORS['text_light']};
        }}
        QPushButton:hover {{
            background-color: {COLORS['primary_hover']};
        }}
        QPushButton:pressed {{
            background-color: {COLORS['primary_pressed']};
        }}
    """
    
    # 成功按钮样式（绿色） - 尺寸与 Primary 一致
    SUCCESS_STYLE = BASE_STYLE + f"""
        QPushButton {{
            background-color: {COLORS['success']};
            color: {COLORS['text_light']};
        }}
        QPushButton:hover {{
            background-color: {COLORS['success_hover']};
        }}
        QPushButton:pressed {{
            background-color: {COLORS['success_hover']};
        }}
    """

    # 危险按钮样式（红色） - 尺寸与 Primary 一致
    DANGER_STYLE = BASE_STYLE + f"""
        QPushButton {{
            background-color: {COLORS['danger']};
            color: {COLORS['text_light']};
        }}
        QPushButton:hover {{
            background-color: {COLORS['danger_hover']};
        }}
        QPushButton:pressed {{
            background-color: {COLORS['danger_hover']};
        }}
    """

    # 次要按钮样式（灰色） - 尺寸与 Primary 一致
    SECONDARY_STYLE = BASE_STYLE + f"""
        QPushButton {{
            background-color: {COLORS['secondary']};
            color: {COLORS['text_light']};
        }}
        QPushButton:hover {{
            background-color: {COLORS['secondary_hover']};
        }}
        QPushButton:pressed {{
            background-color: {COLORS['secondary_hover']};
        }}
    """

    # 小按钮样式 - 尺寸更小
    SMALL_STYLE = f"""
        QPushButton {{
            font-family: {FONTS['family_primary']};
            font-weight: {FONTS['weight_medium']};
            border: none;
            border-radius: {RADIUS['sm']};
            text-align: center;
            background-color: {COLORS['secondary']};
            color: {COLORS['text_light']};
            font-size: {FONTS['size_sm']};
            padding: {SPACING['sm']} {SPACING['md']};
            min-height: 24px;
        }}
        QPushButton:hover {{
            background-color: {COLORS['secondary_hover']};
        }}
        QPushButton:pressed {{
            background-color: {COLORS['secondary_hover']};
        }}
        QPushButton:disabled {{
            background-color: {COLORS['disabled_bg']};
            color: {COLORS['disabled_text']};
        }}
    """
    
    @staticmethod
    def calculate_button_width(text: str, font_size: int = 10, padding: int = 32) -> int:
        """
        根据文本内容计算合适的按钮宽度
        
        Args:
            text: 按钮文本
            font_size: 字体大小
            padding: 左右内边距总和
            
        Returns:
            建议的按钮宽度
        """
        font = QFont("Microsoft YaHei", font_size)
        font.setBold(True)
        metrics = QFontMetrics(font)
        text_width = metrics.horizontalAdvance(text)
        
        # 为中文字符预留更多空间
        chinese_chars = sum(1 for char in text if ord(char) > 127)
        if chinese_chars > 0:
            text_width += chinese_chars * 2  # 为中文字符额外预留空间
        
        return max(60, text_width + padding)  # 最小宽度60px
    
    @staticmethod
    def setup_button(button: QPushButton, style_type: str = "primary", 
                    auto_width: bool = True, min_width: int = None) -> None:
        """
        设置按钮样式
        
        Args:
            button: 按钮对象
            style_type: 样式类型 ("primary", "success", "danger", "secondary", "small")
            auto_width: 是否自动计算宽度
            min_width: 最小宽度
        """
        # 应用样式
        style_map = {
            "primary": ButtonStyleManager.PRIMARY_STYLE,
            "success": ButtonStyleManager.SUCCESS_STYLE,
            "danger": ButtonStyleManager.DANGER_STYLE,
            "secondary": ButtonStyleManager.SECONDARY_STYLE,
            "small": ButtonStyleManager.SMALL_STYLE,
        }
        
        button.setStyleSheet(style_map.get(style_type, ButtonStyleManager.PRIMARY_STYLE))
        
        # 设置宽度
        if auto_width:
            font_size = 9 if style_type == "small" else 10
            width = ButtonStyleManager.calculate_button_width(button.text(), font_size)
            if min_width:
                width = max(width, min_width)
            button.setMinimumWidth(width)
        elif min_width:
            button.setMinimumWidth(min_width)
    
    @staticmethod
    def create_button(text: str, style_type: str = "primary", 
                     auto_width: bool = True, min_width: int = None) -> QPushButton:
        """
        创建带样式的按钮
        
        Args:
            text: 按钮文本
            style_type: 样式类型
            auto_width: 是否自动计算宽度
            min_width: 最小宽度
            
        Returns:
            配置好的按钮对象
        """
        button = QPushButton(text)
        ButtonStyleManager.setup_button(button, style_type, auto_width, min_width)
        return button
