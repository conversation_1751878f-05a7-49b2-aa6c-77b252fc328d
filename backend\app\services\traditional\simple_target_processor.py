"""
简化的军事目标图片处理器
专注于从targets目录加载军事目标图片，并进行基础的尺寸调整和透明度处理
"""
import os
import random
import logging
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from PIL import Image, ImageEnhance, ImageFilter
import numpy as np

from .matting_processor import get_matting_processor

logger = logging.getLogger(__name__)


class SimpleTargetProcessor:
    """简化的军事目标图片处理器"""
    
    def __init__(self, targets_dir: str = None):
        """
        初始化目标处理器
        
        Args:
            targets_dir: 目标图片目录路径
        """
        if targets_dir is None:
            # 默认使用pic_resource/targets目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
            targets_dir = os.path.join(project_root, "pic_resource", "targets")
        
        self.targets_dir = Path(targets_dir)
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.webp', '.tiff'}
        
        # 军事目标类型映射
        self.target_mapping = {
            "坦克": "tanks",
            "战机": "aircraft", 
            "舰艇": "ships"
        }
        
        # 目标图片缓存
        self._target_cache = {}
        self._cache_valid = False

        # 初始化抠图处理器
        self.matting_processor = get_matting_processor()

        logger.info(f"简化目标处理器初始化完成，目标目录: {self.targets_dir}")
    
    def initialize(self) -> bool:
        """
        初始化处理器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            if not self.targets_dir.exists():
                logger.error(f"目标目录不存在: {self.targets_dir}")
                return False
            
            # 扫描目标图片
            self._scan_targets()
            self._cache_valid = True
            
            logger.info("简化目标处理器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"简化目标处理器初始化失败: {str(e)}")
            return False
    
    def _scan_targets(self):
        """扫描目标图片文件"""
        self._target_cache.clear()
        
        try:
            for target_type_en in self.target_mapping.values():
                target_dir = self.targets_dir / target_type_en
                if not target_dir.exists():
                    logger.warning(f"目标类型目录不存在: {target_dir}")
                    continue
                
                images = []
                for img_file in target_dir.iterdir():
                    if (img_file.is_file() and 
                        img_file.suffix.lower() in self.supported_formats):
                        images.append(str(img_file))
                
                if images:
                    self._target_cache[target_type_en] = images
                    logger.debug(f"发现 {len(images)} 张目标图片: {target_type_en}")
            
            total_targets = sum(len(images) for images in self._target_cache.values())
            logger.info(f"目标图片扫描完成，共发现 {total_targets} 张图片")
            
        except Exception as e:
            logger.error(f"扫描目标图片失败: {str(e)}")
    
    def get_random_target(self, military_target: str) -> Optional[str]:
        """
        获取随机的军事目标图片
        
        Args:
            military_target: 军事目标类型
            
        Returns:
            Optional[str]: 图片路径
        """
        try:
            target_type_en = self.target_mapping.get(military_target)
            if not target_type_en:
                logger.error(f"未知的军事目标类型: {military_target}")
                return None
            
            # 检查缓存是否有效
            if not self._cache_valid:
                self._scan_targets()
                self._cache_valid = True
            
            # 获取目标图片列表
            target_images = self._target_cache.get(target_type_en, [])
            
            if not target_images:
                logger.warning(f"未找到目标图片: {target_type_en}")
                return None
            
            # 随机选择一张图片
            selected_image = random.choice(target_images)
            logger.debug(f"选择目标图片: {selected_image}")
            
            return selected_image
            
        except Exception as e:
            logger.error(f"获取目标图片失败: {str(e)}")
            return None
    
    def load_and_process_target(
        self,
        target_path: str,
        background_size: Tuple[int, int],
        **kwargs
    ) -> Optional[Image.Image]:
        """
        加载并处理目标图片

        Args:
            target_path: 目标图片路径
            background_size: 背景图片尺寸
            **kwargs: 处理参数

        Returns:
            Optional[Image.Image]: 处理后的目标图片
        """
        try:
            # 检查是否启用抠图
            enable_matting = kwargs.get('enable_matting', False)
            matting_method = kwargs.get('matting_method', 'auto')

            if enable_matting:
                # 使用抠图处理器
                target_img = self.matting_processor.process_image(
                    target_path,
                    method=matting_method
                )

                if target_img is None:
                    # 抠图失败，降级使用原始图片
                    logger.warning(f"抠图失败，使用原始图片: {target_path}")
                    target_img = Image.open(target_path).convert('RGBA')
                else:
                    logger.info(f"抠图成功: {os.path.basename(target_path)}")
            else:
                # 直接加载图片
                target_img = Image.open(target_path).convert('RGBA')

            # 处理图片
            processed_img = self._process_target_image(target_img, background_size, **kwargs)

            return processed_img

        except Exception as e:
            logger.error(f"加载和处理目标图片失败 {target_path}: {str(e)}")
            return None
    
    def _process_target_image(
        self, 
        target_img: Image.Image, 
        background_size: Tuple[int, int], 
        **kwargs
    ) -> Image.Image:
        """
        处理军事目标图片
        
        Args:
            target_img: 目标图片
            background_size: 背景图片尺寸
            **kwargs: 处理参数
            
        Returns:
            Image.Image: 处理后的目标图片
        """
        try:
            # 获取处理参数
            target_scale = kwargs.get('target_scale', 0.3)  # 目标相对于背景的比例
            opacity = kwargs.get('opacity', 1.0)  # 透明度
            rotation = kwargs.get('rotation', 0)  # 旋转角度
            flip_horizontal = kwargs.get('flip_horizontal', False)  # 水平翻转
            enhance_contrast = kwargs.get('enhance_contrast', 1.0)  # 对比度增强
            enhance_brightness = kwargs.get('enhance_brightness', 1.0)  # 亮度增强
            
            # 1. 调整尺寸
            target_img = self._resize_target(target_img, background_size, target_scale)
            
            # 2. 旋转
            if rotation != 0:
                target_img = target_img.rotate(rotation, expand=True)
            
            # 3. 翻转
            if flip_horizontal:
                target_img = target_img.transpose(Image.Transpose.FLIP_LEFT_RIGHT)
            
            # 4. 调整对比度
            if enhance_contrast != 1.0:
                enhancer = ImageEnhance.Contrast(target_img)
                target_img = enhancer.enhance(enhance_contrast)
            
            # 5. 调整亮度
            if enhance_brightness != 1.0:
                enhancer = ImageEnhance.Brightness(target_img)
                target_img = enhancer.enhance(enhance_brightness)
            
            # 6. 调整透明度
            if opacity < 1.0:
                target_img = self._adjust_opacity(target_img, opacity)
            
            return target_img
            
        except Exception as e:
            logger.error(f"处理目标图片失败: {str(e)}")
            return target_img
    
    def _resize_target(
        self, 
        target_img: Image.Image, 
        background_size: Tuple[int, int], 
        scale: float
    ) -> Image.Image:
        """
        调整目标图片尺寸
        
        Args:
            target_img: 目标图片
            background_size: 背景图片尺寸
            scale: 缩放比例
            
        Returns:
            Image.Image: 调整后的图片
        """
        try:
            bg_width, bg_height = background_size
            
            # 计算目标尺寸
            target_width = int(bg_width * scale)
            target_height = int(bg_height * scale)
            
            # 保持宽高比调整尺寸
            target_img.thumbnail((target_width, target_height), Image.Resampling.LANCZOS)
            
            return target_img
            
        except Exception as e:
            logger.error(f"调整目标尺寸失败: {str(e)}")
            return target_img
    
    def _adjust_opacity(self, target_img: Image.Image, opacity: float) -> Image.Image:
        """
        调整图片透明度
        
        Args:
            target_img: 目标图片
            opacity: 透明度 (0.0-1.0)
            
        Returns:
            Image.Image: 调整后的图片
        """
        try:
            if target_img.mode == 'RGBA':
                # 获取alpha通道
                alpha = target_img.split()[-1]
                # 调整alpha值
                alpha = ImageEnhance.Brightness(alpha).enhance(opacity)
                # 重新组合
                rgb = target_img.convert('RGB')
                target_img = Image.merge('RGBA', rgb.split() + (alpha,))
            else:
                # 如果没有alpha通道，创建一个
                alpha = Image.new('L', target_img.size, int(255 * opacity))
                target_img = target_img.convert('RGB')
                target_img = Image.merge('RGBA', target_img.split() + (alpha,))
            
            return target_img
            
        except Exception as e:
            logger.error(f"调整透明度失败: {str(e)}")
            return target_img
    
    def get_available_targets(self) -> Dict[str, int]:
        """
        获取可用的目标类型和数量
        
        Returns:
            Dict[str, int]: 目标类型对应的图片数量
        """
        try:
            if not self._cache_valid:
                self._scan_targets()
                self._cache_valid = True
            
            result = {}
            for target_cn, target_en in self.target_mapping.items():
                count = len(self._target_cache.get(target_en, []))
                if count > 0:
                    result[target_cn] = count
            
            return result
            
        except Exception as e:
            logger.error(f"获取可用目标失败: {str(e)}")
            return {}
    
    def refresh_cache(self):
        """刷新目标图片缓存"""
        try:
            self._scan_targets()
            self._cache_valid = True
            logger.info("目标图片缓存刷新完成")
        except Exception as e:
            logger.error(f"刷新目标图片缓存失败: {str(e)}")
            self._cache_valid = False

    def get_matting_stats(self) -> Dict[str, Any]:
        """
        获取抠图统计信息

        Returns:
            Dict[str, Any]: 抠图统计信息
        """
        return self.matting_processor.get_stats()

    def clear_matting_cache(self):
        """清空抠图缓存"""
        self.matting_processor.clear_cache()


# 全局实例
_simple_target_processor = None


def get_simple_target_processor() -> SimpleTargetProcessor:
    """获取简化目标处理器实例"""
    global _simple_target_processor
    if _simple_target_processor is None:
        _simple_target_processor = SimpleTargetProcessor()
        _simple_target_processor.initialize()
    return _simple_target_processor
