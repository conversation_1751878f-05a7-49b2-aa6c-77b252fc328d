"""
数据管理主界面组件
"""
import os
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QSplitter,
    QGroupBox, QLabel, QPushButton, QLineEdit, QComboBox, QCheckBox,
    QSpinBox, QDateEdit, QProgressBar, QMessageBox, QFileDialog,
    QFrame, QScrollArea, QTabWidget, QTextEdit, QFormLayout
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QDate, QTimer
from PyQt6.QtGui import QFont, QPixmap, QGuiApplication

# 修复导入路径问题
import sys
import os

# 添加src目录到Python路径，确保可以找到services模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# 使用绝对导入，与main_window.py保持一致
from widgets.image_preview import ImageGridWidget
from widgets.upload_dialog import ImageUploadDialog
from widgets.batch_edit_dialog import BatchEditDialog
from services.image_management_client import ImageManagementClient
from styles.unified_theme import MINIMAL_STYLE
from styles.button_styles import ButtonStyleManager


class DataLoadWorker(QThread):
    """数据加载工作线程"""
    data_loaded = pyqtSignal(dict)  # 加载完成的数据
    load_failed = pyqtSignal(str)  # 加载失败信息
    progress_updated = pyqtSignal(str)  # 进度更新
    
    def __init__(self, client: ImageManagementClient, **kwargs):
        super().__init__()
        self.client = client
        self.params = kwargs
    
    def run(self):
        try:
            self.progress_updated.emit("正在加载图片数据...")
            result = self.client.get_images(**self.params)
            
            if result.get('success', True):  # API返回的数据结构
                self.data_loaded.emit(result)
            else:
                self.load_failed.emit(result.get('message', '加载失败'))
        except Exception as e:
            self.load_failed.emit(f"加载失败: {str(e)}")


class FilterWidget(QGroupBox):
    """筛选控件组"""
    filter_changed = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__("筛选条件", parent)
        # 动态调整高度以适应小屏幕
        screen_height = QGuiApplication.primaryScreen().availableGeometry().height()
        if screen_height <= 720:
            # 小屏幕：使用更紧凑的高度
            self.setMinimumHeight(150)
            self.setMaximumHeight(300)
        else:
            # 大屏幕：使用标准高度
            self.setMinimumHeight(200)
            self.setMaximumHeight(450)
        self._setup_ui()

    def _setup_ui(self):
        """设置UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(3, 3, 3, 3)
        main_layout.setSpacing(3)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setFrameStyle(QFrame.Shape.NoFrame)

        # 创建内容widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(3, 3, 3, 3)
        content_layout.setSpacing(6)

        # 生成类型 - 单独一行
        gen_type_layout = QVBoxLayout()
        gen_type_layout.addWidget(QLabel("生成类型:"))
        self.generation_type_combo = QComboBox()
        self.generation_type_combo.addItems(["全部", "ai", "traditional", "uploaded"])
        self.generation_type_combo.currentTextChanged.connect(self.filter_changed.emit)
        gen_type_layout.addWidget(self.generation_type_combo)
        content_layout.addLayout(gen_type_layout)

        # 军事目标 - 单独一行
        military_layout = QVBoxLayout()
        military_layout.addWidget(QLabel("军事目标:"))
        self.military_target_combo = QComboBox()
        self.military_target_combo.addItems(["全部", "Tank", "Fighter Aircraft", "Warship"])
        self.military_target_combo.currentTextChanged.connect(self.filter_changed.emit)
        military_layout.addWidget(self.military_target_combo)
        content_layout.addLayout(military_layout)

        # 天气 - 单独一行
        weather_layout = QVBoxLayout()
        weather_layout.addWidget(QLabel("天气:"))
        self.weather_combo = QComboBox()
        self.weather_combo.addItems(["全部", "雨天", "雪天", "大雾", "夜间"])
        self.weather_combo.currentTextChanged.connect(self.filter_changed.emit)
        weather_layout.addWidget(self.weather_combo)
        content_layout.addLayout(weather_layout)

        # 场景 - 单独一行
        scene_layout = QVBoxLayout()
        scene_layout.addWidget(QLabel("场景:"))
        self.scene_combo = QComboBox()
        self.scene_combo.addItems(["全部", "城市", "岛屿", "乡村"])
        self.scene_combo.currentTextChanged.connect(self.filter_changed.emit)
        scene_layout.addWidget(self.scene_combo)
        content_layout.addLayout(scene_layout)

        # 分类 - 单独一行
        category_layout = QVBoxLayout()
        category_layout.addWidget(QLabel("分类:"))
        self.category_combo = QComboBox()
        self.category_combo.addItems(["全部", "训练集", "验证集", "测试集"])
        self.category_combo.currentTextChanged.connect(self.filter_changed.emit)
        category_layout.addWidget(self.category_combo)
        content_layout.addLayout(category_layout)

        # 收藏选项
        self.favorite_checkbox = QCheckBox("仅显示收藏")
        self.favorite_checkbox.stateChanged.connect(self.filter_changed.emit)
        content_layout.addWidget(self.favorite_checkbox)

        # 日期筛选 - 简化布局
        date_layout = QVBoxLayout()
        self.date_enabled_cb = QCheckBox("启用日期筛选")
        self.date_enabled_cb.stateChanged.connect(self._on_date_filter_toggled)
        date_layout.addWidget(self.date_enabled_cb)

        # 日期选择器 - 垂直布局
        date_from_layout = QVBoxLayout()
        date_from_layout.addWidget(QLabel("从:"))
        self.date_from_edit = QDateEdit()
        self.date_from_edit.setCalendarPopup(True)
        self.date_from_edit.setDate(QDate.currentDate().addDays(-30))
        self.date_from_edit.dateChanged.connect(self.filter_changed.emit)
        self.date_from_edit.setEnabled(False)
        date_from_layout.addWidget(self.date_from_edit)

        date_to_layout = QVBoxLayout()
        date_to_layout.addWidget(QLabel("到:"))
        self.date_to_edit = QDateEdit()
        self.date_to_edit.setCalendarPopup(True)
        self.date_to_edit.setDate(QDate.currentDate())
        self.date_to_edit.dateChanged.connect(self.filter_changed.emit)
        self.date_to_edit.setEnabled(False)
        date_to_layout.addWidget(self.date_to_edit)

        date_layout.addLayout(date_from_layout)
        date_layout.addLayout(date_to_layout)
        content_layout.addLayout(date_layout)

        # 搜索框
        search_layout = QVBoxLayout()
        search_layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索文件名、描述...")
        self.search_edit.textChanged.connect(self._on_search_changed)
        search_layout.addWidget(self.search_edit)
        content_layout.addLayout(search_layout)

        # 添加弹性空间
        content_layout.addStretch()

        # 设置滚动区域
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)

        # 搜索延迟定时器
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.filter_changed.emit)
    
    def _on_search_changed(self):
        """搜索文本改变"""
        self.search_timer.stop()
        self.search_timer.start(500)  # 500ms延迟

    def _on_date_filter_toggled(self, state):
        """日期筛选开关切换"""
        enabled = state == Qt.CheckState.Checked.value
        self.date_from_edit.setEnabled(enabled)
        self.date_to_edit.setEnabled(enabled)
        self.filter_changed.emit()
    
    def get_filters(self) -> Dict[str, Any]:
        """获取当前筛选条件"""
        filters = {}
        
        if self.generation_type_combo.currentText() != "全部":
            filters['generation_type'] = self.generation_type_combo.currentText()
        
        if self.military_target_combo.currentText() != "全部":
            filters['military_target'] = self.military_target_combo.currentText()
        
        if self.weather_combo.currentText() != "全部":
            filters['weather'] = self.weather_combo.currentText()
        
        if self.scene_combo.currentText() != "全部":
            filters['scene'] = self.scene_combo.currentText()
        
        if self.category_combo.currentText() != "全部":
            filters['category'] = self.category_combo.currentText()
        
        if self.favorite_checkbox.isChecked():
            filters['is_favorite'] = True
        
        search_text = self.search_edit.text().strip()
        if search_text:
            filters['search_text'] = search_text

        # 日期筛选
        if self.date_enabled_cb.isChecked():
            from datetime import datetime
            date_from = self.date_from_edit.date().toPython()
            date_to = self.date_to_edit.date().toPython()

            # 转换为datetime对象
            filters['date_from'] = datetime.combine(date_from, datetime.min.time())
            filters['date_to'] = datetime.combine(date_to, datetime.max.time())

        return filters
    
    def clear_filters(self):
        """清空筛选条件"""
        self.generation_type_combo.setCurrentIndex(0)
        self.military_target_combo.setCurrentIndex(0)
        self.weather_combo.setCurrentIndex(0)
        self.scene_combo.setCurrentIndex(0)
        self.category_combo.setCurrentIndex(0)
        self.favorite_checkbox.setChecked(False)
        self.search_edit.clear()
        self.date_enabled_cb.setChecked(False)
        self.date_from_edit.setDate(QDate.currentDate().addDays(-30))
        self.date_to_edit.setDate(QDate.currentDate())


class ToolbarWidget(QFrame):
    """工具栏组件"""
    upload_requested = pyqtSignal()
    batch_delete_requested = pyqtSignal()
    batch_edit_requested = pyqtSignal()
    refresh_requested = pyqtSignal()
    clear_filters_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Shape.StyledPanel)
        self.setMaximumHeight(60)
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 左侧按钮组
        self.upload_btn = ButtonStyleManager.create_button("📁 上传图片", "primary")
        self.upload_btn.clicked.connect(self.upload_requested.emit)
        layout.addWidget(self.upload_btn)

        self.refresh_btn = ButtonStyleManager.create_button("🔄 刷新", "secondary")
        self.refresh_btn.clicked.connect(self.refresh_requested.emit)
        layout.addWidget(self.refresh_btn)

        self.clear_filter_btn = ButtonStyleManager.create_button("🗑️ 清除筛选", "secondary")
        self.clear_filter_btn.clicked.connect(self.clear_filters_requested.emit)
        layout.addWidget(self.clear_filter_btn)

        layout.addWidget(QFrame())  # 分隔符

        # 批量操作按钮
        self.batch_edit_btn = ButtonStyleManager.create_button("✏️ 批量编辑", "primary")
        self.batch_edit_btn.clicked.connect(self.batch_edit_requested.emit)
        self.batch_edit_btn.setEnabled(False)
        layout.addWidget(self.batch_edit_btn)

        self.batch_delete_btn = ButtonStyleManager.create_button("🗑️ 批量删除", "danger")
        self.batch_delete_btn.clicked.connect(self.batch_delete_requested.emit)
        self.batch_delete_btn.setEnabled(False)
        layout.addWidget(self.batch_delete_btn)
        
        layout.addStretch()
        
        # 右侧信息显示
        self.info_label = QLabel("总计: 0 张图片")
        layout.addWidget(self.info_label)
    
    def set_selection_count(self, count: int):
        """设置选中数量"""
        self.batch_edit_btn.setEnabled(count > 0)
        self.batch_delete_btn.setEnabled(count > 0)
        
        if count > 0:
            self.batch_edit_btn.setText(f"✏️ 批量编辑 ({count})")
            self.batch_delete_btn.setText(f"🗑️ 批量删除 ({count})")
        else:
            self.batch_edit_btn.setText("✏️ 批量编辑")
            self.batch_delete_btn.setText("🗑️ 批量删除")
    
    def set_total_count(self, total: int):
        """设置总数量"""
        self.info_label.setText(f"总计: {total} 张图片")


class DataManagementWidget(QWidget):
    """数据管理主界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.client = ImageManagementClient()
        self.load_worker = None
        self.current_page = 1
        self.page_size = 20
        self.total_images = 0
        self.current_images = []
        self.preview_load_worker = None

        # 应用主题样式
        self.setStyleSheet(MINIMAL_STYLE)

        self._setup_ui()
        self._connect_signals()
        self._load_data()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 工具栏
        self.toolbar = ToolbarWidget()
        layout.addWidget(self.toolbar)
        
        # 主要内容区域
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：筛选面板
        left_panel = QWidget()
        left_panel.setMinimumWidth(220)  # 设置最小宽度
        # 根据屏幕宽度动态调整筛选面板宽度
        screen_width = QGuiApplication.primaryScreen().availableGeometry().width()
        if screen_width <= 1280:
            left_panel.setMaximumWidth(240)  # 小屏幕使用更窄的面板
        else:
            left_panel.setMaximumWidth(280)

        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(5)

        self.filter_widget = FilterWidget()
        left_layout.addWidget(self.filter_widget)
        left_layout.addStretch()

        # 中间：图片网格
        middle_panel = QWidget()
        middle_panel.setMinimumWidth(400)  # 确保中间有足够空间显示图片
        middle_layout = QVBoxLayout(middle_panel)
        middle_layout.setContentsMargins(5, 5, 5, 5)
        middle_layout.setSpacing(5)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(20)
        middle_layout.addWidget(self.progress_bar)

        # 图片网格
        self.image_grid = ImageGridWidget()
        middle_layout.addWidget(self.image_grid)

        # 右侧：图片预览面板
        right_panel = self._create_preview_panel()
        right_panel.setMinimumWidth(300)  # 设置预览面板最小宽度

        # 分页控件
        pagination_widget = QWidget()
        pagination_widget.setMaximumHeight(40)
        pagination_layout = QHBoxLayout(pagination_widget)
        pagination_layout.setContentsMargins(0, 5, 0, 5)

        self.prev_page_btn = ButtonStyleManager.create_button("◀ 上一页", "secondary")
        self.prev_page_btn.setEnabled(False)
        self.prev_page_btn.setMaximumWidth(80)

        self.next_page_btn = ButtonStyleManager.create_button("下一页 ▶", "secondary")
        self.next_page_btn.setEnabled(False)
        self.next_page_btn.setMaximumWidth(80)

        self.page_info_label = QLabel("第 1 页")
        self.page_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.page_info_label.setMinimumWidth(80)

        # 分页大小选择
        page_size_widget = QWidget()
        page_size_layout = QHBoxLayout(page_size_widget)
        page_size_layout.setContentsMargins(0, 0, 0, 0)
        page_size_layout.addWidget(QLabel("每页:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["10", "20", "50", "100"])
        self.page_size_combo.setCurrentText("20")
        self.page_size_combo.currentTextChanged.connect(self._on_page_size_changed)
        self.page_size_combo.setMaximumWidth(60)
        page_size_layout.addWidget(self.page_size_combo)
        page_size_layout.addWidget(QLabel("张"))

        pagination_layout.addWidget(self.prev_page_btn)
        pagination_layout.addWidget(self.page_info_label)
        pagination_layout.addWidget(self.next_page_btn)
        pagination_layout.addStretch()
        pagination_layout.addWidget(page_size_widget)

        middle_layout.addWidget(pagination_widget)

        # 设置分割器属性
        main_splitter.setHandleWidth(3)  # 设置分割器手柄宽度
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(middle_panel)
        main_splitter.addWidget(right_panel)

        # 设置分割器比例 - 左侧固定，中间自适应，右侧固定
        if screen_width <= 1280:
            main_splitter.setSizes([240, 500, 300])  # 为小屏幕优化
        else:
            main_splitter.setSizes([280, 600, 350])  # 默认比例

        main_splitter.setStretchFactor(0, 0)  # 左侧面板固定宽度
        main_splitter.setStretchFactor(1, 1)  # 中间面板可伸缩
        main_splitter.setStretchFactor(2, 0)  # 右侧面板固定宽度

        layout.addWidget(main_splitter)

    def _create_preview_panel(self):
        """创建图片预览面板"""
        preview_panel = QWidget()
        preview_layout = QVBoxLayout(preview_panel)
        preview_layout.setContentsMargins(5, 5, 5, 5)
        preview_layout.setSpacing(10)

        # 预览标题
        preview_title = QLabel("图片预览")
        preview_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                padding: 5px;
                background-color: #f8f9fa;
                border-radius: 4px;
            }
        """)
        preview_layout.addWidget(preview_title)

        # 图片显示区域
        self.preview_label = QLabel()
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setMinimumSize(280, 240)
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #ced4da;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: #6c757d;
                font-size: 12px;
            }
        """)
        self.preview_label.setText("点击图片列表中的项目\n在此处预览图片")
        self.preview_label.setScaledContents(False)
        preview_layout.addWidget(self.preview_label)

        # 加载状态指示器
        self.loading_label = QLabel("加载中...")
        self.loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.loading_label.setStyleSheet("""
            QLabel {
                color: #0d6efd;
                font-size: 12px;
                background-color: transparent;
                border: none;
            }
        """)
        self.loading_label.setVisible(False)
        preview_layout.addWidget(self.loading_label)

        # 图片信息显示
        info_group = QGroupBox("图片信息")
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        info_layout = QVBoxLayout(info_group)

        self.image_info_text = QLabel("未选择图片")
        self.image_info_text.setWordWrap(True)
        self.image_info_text.setStyleSheet("""
            QLabel {
                color: #495057;
                font-size: 11px;
                padding: 5px;
                background-color: #f8f9fa;
                border-radius: 4px;
                border: none;
            }
        """)
        info_layout.addWidget(self.image_info_text)

        preview_layout.addWidget(info_group)
        preview_layout.addStretch()

        return preview_panel

    def _connect_signals(self):
        """连接信号"""
        # 筛选条件改变
        self.filter_widget.filter_changed.connect(self._on_filter_changed)
        
        # 工具栏信号
        self.toolbar.upload_requested.connect(self._upload_image)
        self.toolbar.refresh_requested.connect(self._refresh_data)
        self.toolbar.batch_delete_requested.connect(self._batch_delete)
        self.toolbar.batch_edit_requested.connect(self._batch_edit)
        self.toolbar.clear_filters_requested.connect(self._clear_filters)
        
        # 图片网格信号
        self.image_grid.images_selection_changed.connect(self._on_selection_changed)
        self.image_grid.image_selected.connect(self._on_image_selected)
        
        # 分页信号
        self.prev_page_btn.clicked.connect(self._prev_page)
        self.next_page_btn.clicked.connect(self._next_page)
    
    def _load_data(self):
        """加载数据"""
        if self.load_worker and self.load_worker.isRunning():
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        # 获取筛选条件
        filters = self.filter_widget.get_filters()
        filters.update({
            'page': self.current_page,
            'page_size': self.page_size
        })
        
        self.load_worker = DataLoadWorker(self.client, **filters)
        self.load_worker.data_loaded.connect(self._on_data_loaded)
        self.load_worker.load_failed.connect(self._on_load_failed)
        self.load_worker.progress_updated.connect(self._on_progress_updated)
        self.load_worker.start()
    
    def _on_data_loaded(self, data: Dict[str, Any]):
        """数据加载完成"""
        self.progress_bar.setVisible(False)

        # 处理不同的API响应格式
        if 'success' in data and not data.get('success', True):
            self._on_load_failed(data.get('message', '数据加载失败'))
            return

        # 更新图片网格
        images = data.get('images', [])
        # 确保图片数据包含必要字段
        processed_images = []
        for img in images:
            if isinstance(img, dict):
                # 确保必要字段存在
                processed_img = {
                    'id': img.get('id', 0),
                    'filename': img.get('filename', 'Unknown'),
                    'width': img.get('width', 0),
                    'height': img.get('height', 0),
                    'generation_type': img.get('generation_type', 'unknown'),
                    'description': img.get('description', ''),
                    'tags': img.get('tags', []),
                    'category': img.get('category', ''),
                    'is_favorite': img.get('is_favorite', False),
                    'created_at': img.get('created_at', ''),
                    'military_target': img.get('military_target', ''),
                    'weather': img.get('weather', ''),
                    'scene': img.get('scene', '')
                }
                processed_images.append(processed_img)

        self.current_images = processed_images
        self.image_grid.set_images(processed_images)

        # 更新分页信息
        self.total_images = data.get('total', 0)
        total_pages = data.get('total_pages', 1)

        self.page_info_label.setText(f"第 {self.current_page} / {total_pages} 页")
        self.prev_page_btn.setEnabled(self.current_page > 1)
        self.next_page_btn.setEnabled(self.current_page < total_pages)

        # 更新工具栏信息
        self.toolbar.set_total_count(self.total_images)
    
    def _on_load_failed(self, error_message: str):
        """数据加载失败"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "加载失败", f"无法加载图片数据:\n{error_message}")
    
    def _on_progress_updated(self, message: str):
        """进度更新"""
        # 可以在这里显示加载状态
        pass
    
    def _on_filter_changed(self):
        """筛选条件改变"""
        self.current_page = 1
        self._load_data()
    
    def _on_selection_changed(self, selected_ids: List[int]):
        """选择改变"""
        self.toolbar.set_selection_count(len(selected_ids))
    
    def _on_image_selected(self, image_id: int):
        """图片被选中"""
        # 在预览面板显示选中的图片
        self._show_image_preview(image_id)
    
    def _prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self._load_data()
    
    def _next_page(self):
        """下一页"""
        self.current_page += 1
        self._load_data()

    def _on_page_size_changed(self, new_size_text: str):
        """分页大小改变"""
        try:
            new_size = int(new_size_text)
            if new_size != self.page_size:
                self.page_size = new_size
                self.current_page = 1  # 重置到第一页
                self._load_data()
        except ValueError:
            pass
    
    def _refresh_data(self):
        """刷新数据"""
        self._load_data()

    def _clear_filters(self):
        """清除筛选条件"""
        self.filter_widget.clear_filters()
    
    def _upload_image(self):
        """上传图片"""
        dialog = ImageUploadDialog(self)
        dialog.upload_completed.connect(self._on_upload_completed)
        dialog.exec()

    def _on_upload_completed(self, result):
        """上传完成处理"""
        # 刷新数据列表
        self._refresh_data()

        # 显示成功消息
        image_data = result.get('image')
        if image_data:
            QMessageBox.information(
                self, "上传成功",
                f"图片已成功添加到数据库！\n文件名: {image_data.get('filename')}"
            )
    
    def _batch_delete(self):
        """批量删除"""
        selected_ids = self.image_grid.get_selected_images()
        if not selected_ids:
            return
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除选中的 {len(selected_ids)} 张图片吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 执行批量删除
            result = self.client.batch_delete_images(selected_ids)
            if result.get('success'):
                QMessageBox.information(self, "删除成功", result.get('message', '删除完成'))
                self._refresh_data()
            else:
                QMessageBox.critical(self, "删除失败", result.get('message', '删除失败'))
    
    def _batch_edit(self):
        """批量编辑"""
        selected_ids = self.image_grid.get_selected_images()
        if not selected_ids:
            return

        dialog = BatchEditDialog(selected_ids, self)
        dialog.edit_completed.connect(self._on_batch_edit_completed)
        dialog.exec()

    def _on_batch_edit_completed(self, result):
        """批量编辑完成处理"""
        # 刷新数据列表
        self._refresh_data()

    def _show_image_preview(self, image_id: int):
        """显示图片预览"""
        # 查找图片数据
        image_data = None
        for img in self.current_images:
            if img.get('id') == image_id:
                image_data = img
                break

        if not image_data:
            self._show_preview_error("未找到图片数据")
            return

        # 显示加载状态
        self._show_loading_state()

        # 更新图片信息
        self._update_image_info(image_data)

        # 启动图片加载线程
        self._load_preview_image(image_id)

    def _show_loading_state(self):
        """显示加载状态"""
        self.preview_label.setText("加载中...")
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 2px solid #0d6efd;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: #0d6efd;
                font-size: 12px;
            }
        """)
        self.loading_label.setVisible(True)

    def _show_preview_error(self, error_message: str):
        """显示预览错误"""
        self.preview_label.setText(f"加载失败\n{error_message}")
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #dc3545;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: #dc3545;
                font-size: 12px;
            }
        """)
        self.loading_label.setVisible(False)
        self.image_info_text.setText("加载失败")

    def _show_preview_placeholder(self):
        """显示预览占位符"""
        self.preview_label.setText("点击图片列表中的项目\n在此处预览图片")
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #ced4da;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: #6c757d;
                font-size: 12px;
            }
        """)
        self.loading_label.setVisible(False)
        self.image_info_text.setText("未选择图片")

    def _update_image_info(self, image_data: Dict[str, Any]):
        """更新图片信息显示"""
        info_text = f"ID: {image_data.get('id', 'N/A')}\n"
        info_text += f"文件名: {image_data.get('filename', 'N/A')}\n"
        info_text += f"尺寸: {image_data.get('width', 'N/A')} × {image_data.get('height', 'N/A')}\n"
        info_text += f"格式: {image_data.get('format', 'N/A')}\n"

        file_size = image_data.get('file_size')
        if file_size:
            if file_size > 1024 * 1024:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
            elif file_size > 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size} B"
            info_text += f"大小: {size_str}\n"

        created_at = image_data.get('created_at')
        if created_at:
            info_text += f"创建时间: {created_at[:19]}"

        self.image_info_text.setText(info_text)

    def _load_preview_image(self, image_id: int):
        """加载预览图片"""
        # 构建图片URL
        image_url = f"http://localhost:8000/api/v1/images/{image_id}/file"

        # 创建图片加载工作线程
        from .image_preview import ImageLoadWorker
        self.preview_load_worker = ImageLoadWorker(image_id, image_url)
        self.preview_load_worker.image_loaded.connect(self._on_preview_image_loaded)
        self.preview_load_worker.load_failed.connect(self._on_preview_load_failed)
        self.preview_load_worker.start()

    def _on_preview_image_loaded(self, image_id: int, pixmap):
        """预览图片加载完成"""
        self.loading_label.setVisible(False)

        # 获取预览区域的实际大小
        label_size = self.preview_label.size()
        # 确保有合理的最小尺寸
        if label_size.width() < 100 or label_size.height() < 100:
            label_size = self.preview_label.sizeHint()
            if label_size.width() < 280:
                label_size.setWidth(280)
            if label_size.height() < 240:
                label_size.setHeight(240)

        # 缩放图像以适应预览区域，保持宽高比
        scaled_pixmap = pixmap.scaled(
            label_size,
            Qt.AspectRatioMode.KeepAspectRatio,
            Qt.TransformationMode.SmoothTransformation
        )

        self.preview_label.setPixmap(scaled_pixmap)
        self.preview_label.setText("")
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 2px solid #0d6efd;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)

    def _on_preview_load_failed(self, image_id: int, error_message: str):
        """预览图片加载失败"""
        self._show_preview_error(error_message)

        # 清除选择
        self.image_grid.select_none()
