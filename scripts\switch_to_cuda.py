#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Switch AI mode to CUDA mode script
Uninstall CPU PyTorch and install CUDA 12.1 version
"""

import sys
import os
import subprocess
import argparse
import time
from pathlib import Path

# 确保输出使用UTF-8编码（Windows系统）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'

def check_cuda_availability():
    """检查CUDA是否可用"""
    try:
        import torch
        if torch.cuda.is_available():
            cuda_version = torch.version.cuda
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            print(f"CUDA is available: {cuda_version}")
            print(f"GPU devices: {device_count}")
            print(f"Primary GPU: {device_name}")
            return True
        else:
            print("CUDA is not available")
            return False
    except ImportError:
        print("PyTorch not installed")
        return False
    except Exception as e:
        print(f"Error checking CUDA: {str(e)}")
        return False

def check_nvidia_driver():
    """检查NVIDIA驱动"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("NVIDIA driver detected:")
            # 提取驱动版本信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Driver Version' in line:
                    print(f"  {line.strip()}")
                    break
            return True
        else:
            print("NVIDIA driver not found or not working")
            return False
    except subprocess.TimeoutExpired:
        print("nvidia-smi command timeout")
        return False
    except FileNotFoundError:
        print("nvidia-smi command not found - NVIDIA driver may not be installed")
        return False
    except Exception as e:
        print(f"Error checking NVIDIA driver: {str(e)}")
        return False

def uninstall_cpu_pytorch():
    """卸载CPU版本的PyTorch"""
    print("Uninstalling CPU version of PyTorch...")
    
    # PyTorch相关包列表
    pytorch_packages = [
        'torch',
        'torchvision', 
        'torchaudio',
        'torchtext',
        'torchdata'
    ]
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONUTF8'] = '1'
    
    success_count = 0
    for package in pytorch_packages:
        try:
            print(f"Uninstalling {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "uninstall", package, "-y"
            ], check=True, env=env, capture_output=True, text=True)
            print(f"  ✓ {package} uninstalled successfully")
            success_count += 1
        except subprocess.CalledProcessError as e:
            if "not installed" in e.stdout.lower() or "not installed" in e.stderr.lower():
                print(f"  - {package} was not installed")
            else:
                print(f"  ✗ Failed to uninstall {package}: {e}")
        except Exception as e:
            print(f"  ✗ Error uninstalling {package}: {str(e)}")
    
    print(f"Uninstallation completed: {success_count}/{len(pytorch_packages)} packages processed")
    return True

def install_cuda_pytorch():
    """安装CUDA 12.1版本的PyTorch"""
    print("Installing CUDA 12.1 version of PyTorch...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONUTF8'] = '1'
    
    # 首先升级pip以避免SSL问题
    print("Upgrading pip to latest version...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip", 
            "--trusted-host", "pypi.org", "--trusted-host", "pypi.python.org", "--trusted-host", "files.pythonhosted.org"
        ], check=True, env=env, timeout=300)
        print("  ✓ pip upgraded successfully")
    except Exception as e:
        print(f"  ⚠️ pip upgrade failed, continuing anyway: {e}")
    
    # CUDA 12.1对应的PyTorch安装命令
    install_commands = [
        # 主要PyTorch包 - 使用trusted-host避免SSL问题
        [sys.executable, "-m", "pip", "install", "torch", "torchvision", "torchaudio", 
         "-i", "https://repo.huaweicloud.com/repository/pypi/simple/", "--trusted-host", "repo.huaweicloud.com"],
        
        # 额外的加速库 - 如果主安装成功再安装
        [sys.executable, "-m", "pip", "install", "xformers", 
         "-i", "https://repo.huaweicloud.com/repository/pypi/simple/", "--trusted-host", "repo.huaweicloud.com"]
    ]
    
    # 备用安装命令（如果主命令失败）
    fallback_commands = [
        # 使用默认PyPI源安装（可能是CPU版本，但至少能安装）
        [sys.executable, "-m", "pip", "install", "torch", "torchvision", "torchaudio",
         "-i", "https://repo.huaweicloud.com/repository/pypi/simple/", "--trusted-host", "repo.huaweicloud.com"],
        
        # 尝试使用清华镜像源
        [sys.executable, "-m", "pip", "install", "torch", "torchvision", "torchaudio",
         "-i", "https://repo.huaweicloud.com/repository/pypi/simple/", "--trusted-host", "repo.huaweicloud.com"]
    ]
    
    success = False
    
    # 尝试主要安装命令
    for i, cmd in enumerate(install_commands, 1):
        try:
            print(f"Installing PyTorch packages ({i}/{len(install_commands)})...")
            print(f"Command: {' '.join(cmd[:8])}...")  # 只显示前几个参数避免太长
            
            result = subprocess.run(cmd, check=True, env=env, timeout=900)  # 15分钟超时
            print(f"  ✓ Installation step {i} completed successfully")
            
            if i == 1:  # 主要PyTorch包安装成功
                success = True
            
        except subprocess.TimeoutExpired:
            print(f"  ✗ Installation step {i} timeout (15 minutes)")
            if i == 1:  # 主要包安装失败，尝试备用方案
                print("Trying fallback installation methods...")
                break
        except subprocess.CalledProcessError as e:
            print(f"  ✗ Installation step {i} failed: {e}")
            if i == 1:  # 主要包安装失败，尝试备用方案
                print("Trying fallback installation methods...")
                break
        except Exception as e:
            print(f"  ✗ Error in installation step {i}: {str(e)}")
            if i == 1:  # 主要包安装失败，尝试备用方案
                print("Trying fallback installation methods...")
                break
    
    # 如果主要安装失败，尝试备用方案
    if not success:
        print("Main installation failed, trying fallback methods...")
        for i, cmd in enumerate(fallback_commands, 1):
            try:
                print(f"Trying fallback method {i}/{len(fallback_commands)}...")
                print(f"Command: {' '.join(cmd[:6])}...")
                
                result = subprocess.run(cmd, check=True, env=env, timeout=900)
                print(f"  ✓ Fallback installation {i} completed successfully")
                success = True
                break
                
            except Exception as e:
                print(f"  ✗ Fallback method {i} failed: {str(e)}")
                continue
    
    if success:
        print("PyTorch installation completed!")
        return True
    else:
        print("All installation methods failed!")
        print("\nTroubleshooting suggestions:")
        print("1. Check your internet connection")
        print("2. Try running the script with administrator privileges")
        print("3. Temporarily disable antivirus/firewall")
        print("4. Try manual installation:")
        print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121 --trusted-host download.pytorch.org")
        return False

def verify_cuda_installation():
    """验证CUDA安装"""
    print("Verifying CUDA PyTorch installation...")
    
    try:
        # 重新导入torch以获取最新版本
        if 'torch' in sys.modules:
            del sys.modules['torch']
        
        import torch
        
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"CUDA version: {torch.version.cuda}")
            print(f"cuDNN version: {torch.backends.cudnn.version()}")
            print(f"GPU count: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            
            # 测试CUDA功能
            print("Testing CUDA functionality...")
            device = torch.device('cuda:0')
            x = torch.randn(1000, 1000, device=device)
            y = torch.randn(1000, 1000, device=device)
            z = torch.mm(x, y)
            print(f"  ✓ CUDA tensor operations working (result shape: {z.shape})")
            
            return True
        else:
            print("  ✗ CUDA is not available after installation")
            return False
            
    except ImportError as e:
        print(f"  ✗ Failed to import torch: {e}")
        return False
    except Exception as e:
        print(f"  ✗ Error verifying installation: {str(e)}")
        return False

def update_config_file():
    """更新配置文件以使用CUDA"""
    config_file = Path(__file__).parent.parent / "backend" / "app" / "core" / "config.py"
    
    if not config_file.exists():
        print(f"Config file not found: {config_file}")
        return False
    
    try:
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新设备配置 - 查找ai_device字段
        if 'ai_device: str = Field(\n        default="auto"' in content:
            content = content.replace(
                'ai_device: str = Field(\n        default="auto"',
                'ai_device: str = Field(\n        default="cuda"'
            )
            print("Updated ai_device setting from auto to cuda")
        elif 'ai_device: str = Field(default="auto"' in content:
            content = content.replace(
                'ai_device: str = Field(default="auto"',
                'ai_device: str = Field(default="cuda"'
            )
            print("Updated ai_device setting from auto to cuda")
        elif 'ai_device: str = Field(\n        default="cpu"' in content:
            content = content.replace(
                'ai_device: str = Field(\n        default="cpu"',
                'ai_device: str = Field(\n        default="cuda"'
            )
            print("Updated ai_device setting from cpu to cuda")
        elif 'ai_device: str = Field(default="cpu"' in content:
            content = content.replace(
                'ai_device: str = Field(default="cpu"',
                'ai_device: str = Field(default="cuda"'
            )
            print("Updated ai_device setting from cpu to cuda")
        else:
            print("ai_device setting not found in config file, will use auto detection")
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Configuration updated: {config_file}")
        return True
        
    except Exception as e:
        print(f"Error updating config file: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Switch AI mode to CUDA 12.1")
    parser.add_argument("--force", action="store_true", help="Force installation even if CUDA check fails")
    parser.add_argument("--skip-uninstall", action="store_true", help="Skip uninstalling CPU PyTorch")
    parser.add_argument("--verify-only", action="store_true", help="Only verify current installation")
    
    args = parser.parse_args()
    
    print("========================================")
    print("    Switch to CUDA Mode - PyTorch")
    print("========================================")
    print()
    
    if args.verify_only:
        print("Verifying current installation...")
        check_nvidia_driver()
        check_cuda_availability()
        return 0
    
    # 检查NVIDIA驱动
    print("Step 1/5: Checking NVIDIA driver...")
    if not check_nvidia_driver() and not args.force:
        print("NVIDIA driver not detected. Please install NVIDIA driver first.")
        print("Use --force to skip this check.")
        return 1
    
    # 检查当前PyTorch状态
    print("\nStep 2/5: Checking current PyTorch installation...")
    check_cuda_availability()
    
    # 卸载CPU版本
    if not args.skip_uninstall:
        print("\nStep 3/5: Uninstalling CPU PyTorch...")
        if not uninstall_cpu_pytorch():
            print("Failed to uninstall CPU PyTorch")
            return 1
    else:
        print("\nStep 3/5: Skipping CPU PyTorch uninstallation...")
    
    # 安装CUDA版本
    print("\nStep 4/5: Installing CUDA 12.1 PyTorch...")
    if not install_cuda_pytorch():
        print("Failed to install CUDA PyTorch")
        return 1
    
    # 验证安装
    print("\nStep 5/5: Verifying installation...")
    if not verify_cuda_installation():
        print("CUDA PyTorch verification failed")
        return 1
    
    # 更新配置文件
    print("\nUpdating configuration...")
    update_config_file()
    
    print("\n========================================")
    print("✓ CUDA mode switch completed successfully!")
    print("✓ PyTorch with CUDA 12.1 support is ready")
    print("✓ Configuration updated")
    print()
    print("Please restart the application to use CUDA acceleration.")
    print("========================================")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 