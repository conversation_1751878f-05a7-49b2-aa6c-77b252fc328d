"""
性能优化器
提供图片处理的性能优化功能，包括内存管理、批量处理、缓存等
"""
import os
import gc
import logging
import psutil
from typing import Dict, Any, List, Optional, Tuple, Union, Callable
from PIL import Image
import numpy as np
import cv2
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import threading
from functools import lru_cache
import hashlib
import pickle
from pathlib import Path
import time

from .core_types import ImageData, ProcessingResult

logger = logging.getLogger(__name__)


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化性能优化器
        
        Args:
            config: 配置参数
        """
        self.config = config or {
            'max_memory_usage': 0.8,  # 最大内存使用率
            'max_workers': min(4, os.cpu_count() or 1),  # 最大工作线程数
            'cache_size': 100,  # 缓存大小
            'cache_dir': 'data/cache',  # 缓存目录
            'tile_size': 1024,  # 大图片分块处理尺寸
            'enable_gpu': False,  # 是否启用GPU加速
            'memory_check_interval': 10  # 内存检查间隔（秒）
        }
        
        # 创建缓存目录
        os.makedirs(self.config['cache_dir'], exist_ok=True)
        
        # 内存监控
        self.memory_monitor = MemoryMonitor(self.config['max_memory_usage'])
        
        # 缓存管理
        self.cache_manager = CacheManager(
            self.config['cache_dir'], 
            self.config['cache_size']
        )
        
        # 线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=self.config['max_workers'])
        
        logger.info(f"性能优化器初始化完成，最大工作线程数: {self.config['max_workers']}")
    
    def optimize_image_processing(
        self,
        process_func: Callable,
        image_data: ImageData,
        *args,
        **kwargs
    ) -> ProcessingResult:
        """
        优化图片处理
        
        Args:
            process_func: 处理函数
            image_data: 图片数据
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            ProcessingResult: 处理结果
        """
        try:
            # 检查内存使用情况
            if not self.memory_monitor.check_memory():
                # 内存不足，执行垃圾回收
                gc.collect()
                if not self.memory_monitor.check_memory():
                    return ProcessingResult(
                        success=False,
                        error_message="内存不足，无法处理图片",
                        processing_time=0.0
                    )
            
            # 生成缓存键
            cache_key = self._generate_cache_key(process_func.__name__, image_data, args, kwargs)
            
            # 检查缓存
            cached_result = self.cache_manager.get(cache_key)
            if cached_result:
                logger.info(f"从缓存获取处理结果: {cache_key}")
                return cached_result
            
            # 检查是否需要分块处理
            image_size = image_data.image.size
            if max(image_size) > self.config['tile_size']:
                result = self._process_large_image(process_func, image_data, *args, **kwargs)
            else:
                result = process_func(image_data, *args, **kwargs)
            
            # 缓存结果
            if result.success:
                self.cache_manager.set(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"优化图片处理失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )
    
    def batch_process(
        self,
        process_func: Callable,
        image_list: List[ImageData],
        *args,
        **kwargs
    ) -> List[ProcessingResult]:
        """
        批量处理图片
        
        Args:
            process_func: 处理函数
            image_list: 图片列表
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            List[ProcessingResult]: 处理结果列表
        """
        try:
            # 检查内存使用情况
            if not self.memory_monitor.check_memory():
                gc.collect()
            
            # 分批处理以避免内存溢出
            batch_size = self._calculate_batch_size(image_list)
            results = []
            
            for i in range(0, len(image_list), batch_size):
                batch = image_list[i:i + batch_size]
                
                # 使用线程池并行处理
                futures = []
                for image_data in batch:
                    future = self.thread_pool.submit(
                        self.optimize_image_processing,
                        process_func, image_data, *args, **kwargs
                    )
                    futures.append(future)
                
                # 收集结果
                batch_results = [future.result() for future in futures]
                results.extend(batch_results)
                
                # 检查内存使用情况
                if not self.memory_monitor.check_memory():
                    logger.warning("内存使用率过高，执行垃圾回收")
                    gc.collect()
            
            return results
            
        except Exception as e:
            logger.error(f"批量处理失败: {str(e)}")
            return [ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            ) for _ in image_list]
    
    def _process_large_image(
        self,
        process_func: Callable,
        image_data: ImageData,
        *args,
        **kwargs
    ) -> ProcessingResult:
        """
        处理大图片（分块处理）
        
        Args:
            process_func: 处理函数
            image_data: 图片数据
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            ProcessingResult: 处理结果
        """
        try:
            image = image_data.image
            width, height = image.size
            tile_size = self.config['tile_size']
            
            # 计算分块数量
            tiles_x = (width + tile_size - 1) // tile_size
            tiles_y = (height + tile_size - 1) // tile_size
            
            logger.info(f"大图片分块处理: {width}x{height} -> {tiles_x}x{tiles_y}块")
            
            # 创建结果图像
            result_image = Image.new(image.mode, (width, height))
            
            # 处理每个分块
            for y in range(tiles_y):
                for x in range(tiles_x):
                    # 计算分块区域
                    left = x * tile_size
                    top = y * tile_size
                    right = min(left + tile_size, width)
                    bottom = min(top + tile_size, height)
                    
                    # 提取分块
                    tile = image.crop((left, top, right, bottom))
                    tile_data = ImageData(image=tile, metadata=image_data.metadata)
                    
                    # 处理分块
                    tile_result = process_func(tile_data, *args, **kwargs)
                    
                    if tile_result.success:
                        # 将处理后的分块粘贴回结果图像
                        result_image.paste(tile_result.image.image, (left, top))
                    else:
                        logger.warning(f"分块处理失败: ({x}, {y})")
                        # 使用原始分块
                        result_image.paste(tile, (left, top))
            
            result_data = ImageData(
                image=result_image,
                metadata={
                    **image_data.metadata,
                    'tiled_processing': True,
                    'tiles': f"{tiles_x}x{tiles_y}"
                }
            )
            
            return ProcessingResult(
                success=True,
                image=result_data,
                processing_time=0.0,
                metadata={'operation': 'tiled_processing'}
            )
            
        except Exception as e:
            logger.error(f"大图片分块处理失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )
    
    def _calculate_batch_size(self, image_list: List[ImageData]) -> int:
        """
        计算批处理大小
        
        Args:
            image_list: 图片列表
            
        Returns:
            int: 批处理大小
        """
        if not image_list:
            return 1
        
        # 估算单张图片的内存使用量
        sample_image = image_list[0].image
        width, height = sample_image.size
        channels = len(sample_image.getbands())
        
        # 估算内存使用量（字节）
        estimated_memory = width * height * channels * 4  # 假设float32
        
        # 获取可用内存
        available_memory = psutil.virtual_memory().available
        max_memory = available_memory * self.config['max_memory_usage']
        
        # 计算批处理大小
        batch_size = max(1, int(max_memory // estimated_memory))
        batch_size = min(batch_size, self.config['max_workers'] * 2)  # 限制最大批处理大小
        
        return batch_size
    
    def _generate_cache_key(
        self,
        func_name: str,
        image_data: ImageData,
        args: Tuple,
        kwargs: Dict[str, Any]
    ) -> str:
        """
        生成缓存键
        
        Args:
            func_name: 函数名
            image_data: 图片数据
            args: 位置参数
            kwargs: 关键字参数
            
        Returns:
            str: 缓存键
        """
        # 计算图片哈希
        image_bytes = image_data.image.tobytes()
        image_hash = hashlib.md5(image_bytes).hexdigest()[:16]
        
        # 计算参数哈希
        params_str = f"{func_name}_{args}_{sorted(kwargs.items())}"
        params_hash = hashlib.md5(params_str.encode()).hexdigest()[:16]
        
        return f"{func_name}_{image_hash}_{params_hash}"
    
    def cleanup(self):
        """清理资源"""
        try:
            self.thread_pool.shutdown(wait=True)
            self.cache_manager.cleanup()
            logger.info("性能优化器资源清理完成")
        except Exception as e:
            logger.error(f"性能优化器清理失败: {str(e)}")


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, max_usage: float = 0.8):
        """
        初始化内存监控器
        
        Args:
            max_usage: 最大内存使用率
        """
        self.max_usage = max_usage
        self.last_check = 0
        self.check_interval = 5  # 检查间隔（秒）
    
    def check_memory(self) -> bool:
        """
        检查内存使用情况
        
        Returns:
            bool: 内存是否充足
        """
        current_time = time.time()
        if current_time - self.last_check < self.check_interval:
            return True  # 跳过频繁检查
        
        self.last_check = current_time
        
        memory = psutil.virtual_memory()
        usage_ratio = memory.used / memory.total
        
        if usage_ratio > self.max_usage:
            logger.warning(f"内存使用率过高: {usage_ratio:.2%}")
            return False
        
        return True


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_dir: str, max_size: int = 100):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录
            max_size: 最大缓存数量
        """
        self.cache_dir = Path(cache_dir)
        self.max_size = max_size
        self.cache_index = {}
        self.access_times = {}
        
        # 创建缓存目录
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载现有缓存索引
        self._load_cache_index()
    
    def get(self, key: str) -> Optional[ProcessingResult]:
        """
        获取缓存
        
        Args:
            key: 缓存键
            
        Returns:
            ProcessingResult: 缓存的结果，如果不存在则返回None
        """
        try:
            if key not in self.cache_index:
                return None
            
            cache_file = self.cache_dir / f"{key}.pkl"
            if not cache_file.exists():
                # 缓存文件不存在，从索引中移除
                del self.cache_index[key]
                return None
            
            # 更新访问时间
            self.access_times[key] = time.time()
            
            # 加载缓存
            with open(cache_file, 'rb') as f:
                result = pickle.load(f)
            
            return result
            
        except Exception as e:
            logger.error(f"获取缓存失败: {str(e)}")
            return None
    
    def set(self, key: str, result: ProcessingResult):
        """
        设置缓存
        
        Args:
            key: 缓存键
            result: 处理结果
        """
        try:
            # 检查缓存大小限制
            if len(self.cache_index) >= self.max_size:
                self._evict_oldest()
            
            # 保存缓存
            cache_file = self.cache_dir / f"{key}.pkl"
            with open(cache_file, 'wb') as f:
                pickle.dump(result, f)
            
            # 更新索引
            self.cache_index[key] = {
                'file': str(cache_file),
                'created': time.time()
            }
            self.access_times[key] = time.time()
            
        except Exception as e:
            logger.error(f"设置缓存失败: {str(e)}")
    
    def _evict_oldest(self):
        """移除最旧的缓存"""
        if not self.access_times:
            return
        
        # 找到最旧的缓存
        oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        # 删除缓存文件
        try:
            cache_file = Path(self.cache_index[oldest_key]['file'])
            if cache_file.exists():
                cache_file.unlink()
        except Exception as e:
            logger.error(f"删除缓存文件失败: {str(e)}")
        
        # 从索引中移除
        del self.cache_index[oldest_key]
        del self.access_times[oldest_key]
    
    def _load_cache_index(self):
        """加载缓存索引"""
        try:
            index_file = self.cache_dir / 'index.pkl'
            if index_file.exists():
                with open(index_file, 'rb') as f:
                    data = pickle.load(f)
                    self.cache_index = data.get('index', {})
                    self.access_times = data.get('access_times', {})
        except Exception as e:
            logger.error(f"加载缓存索引失败: {str(e)}")
    
    def cleanup(self):
        """清理缓存"""
        try:
            # 保存缓存索引
            index_file = self.cache_dir / 'index.pkl'
            with open(index_file, 'wb') as f:
                pickle.dump({
                    'index': self.cache_index,
                    'access_times': self.access_times
                }, f)
        except Exception as e:
            logger.error(f"保存缓存索引失败: {str(e)}")
