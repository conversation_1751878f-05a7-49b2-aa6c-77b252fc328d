"""
Stable Diffusion模型管理服务
"""

import requests
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class SDModelService:
    """SD模型管理服务"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化SD模型服务
        
        Args:
            base_url: 后端API基础URL
        """
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1/sd-models"
    
    def get_available_models(self) -> Dict[str, Any]:
        """
        获取可用的模型列表
        
        Returns:
            Dict[str, Any]: API响应
        """
        try:
            response = requests.get(f"{self.api_base}/available", timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取可用模型列表失败: {str(e)}")
            return {"success": False, "message": f"获取失败: {str(e)}", "data": {}}
    
    def get_cached_models(self) -> Dict[str, Any]:
        """
        获取已缓存的模型列表
        
        Returns:
            Dict[str, Any]: API响应
        """
        try:
            response = requests.get(f"{self.api_base}/cached", timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取缓存模型列表失败: {str(e)}")
            return {"success": False, "message": f"获取失败: {str(e)}", "data": {}}
    
    def get_current_model(self) -> Dict[str, Any]:
        """
        获取当前加载的模型信息
        
        Returns:
            Dict[str, Any]: API响应
        """
        try:
            response = requests.get(f"{self.api_base}/current", timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取当前模型信息失败: {str(e)}")
            return {"success": False, "message": f"获取失败: {str(e)}", "data": {}}
    
    def load_model(self, model_key: str, force_reload: bool = False) -> Dict[str, Any]:
        """
        加载指定模型
        
        Args:
            model_key: 模型键名
            force_reload: 是否强制重新加载
            
        Returns:
            Dict[str, Any]: API响应
        """
        try:
            data = {
                "model_key": model_key,
                "force_reload": force_reload
            }
            response = requests.post(f"{self.api_base}/load", json=data, timeout=120)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"加载模型失败: {str(e)}")
            return {"success": False, "message": f"加载失败: {str(e)}", "data": {}}
    
    def add_safetensors_model(
        self,
        model_key: str,
        model_name: str,
        safetensors_path: str,
        description: str = ""
    ) -> Dict[str, Any]:
        """
        添加safetensors模型
        
        Args:
            model_key: 模型键名
            model_name: 模型显示名称
            safetensors_path: safetensors文件路径
            description: 模型描述
            
        Returns:
            Dict[str, Any]: API响应
        """
        try:
            data = {
                "model_key": model_key,
                "model_name": model_name,
                "safetensors_path": safetensors_path,
                "description": description
            }
            response = requests.post(f"{self.api_base}/add-safetensors", data=data, timeout=60)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"添加safetensors模型失败: {str(e)}")
            return {"success": False, "message": f"添加失败: {str(e)}", "data": {}}
    
    def validate_safetensors_file(self, file_path: str) -> Dict[str, Any]:
        """
        验证safetensors文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: API响应
        """
        try:
            params = {"file_path": file_path}
            response = requests.post(f"{self.api_base}/validate-safetensors", params=params, timeout=60)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"验证safetensors文件失败: {str(e)}")
            return {"success": False, "message": f"验证失败: {str(e)}", "data": {}}
    
    def upload_safetensors_file(
        self,
        file_path: str,
        model_key: str,
        model_name: str,
        description: str = ""
    ) -> Dict[str, Any]:
        """
        上传safetensors文件
        
        Args:
            file_path: 本地文件路径
            model_key: 模型键名
            model_name: 模型显示名称
            description: 模型描述
            
        Returns:
            Dict[str, Any]: API响应
        """
        try:
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                return {"success": False, "message": f"文件不存在: {file_path_obj}", "data": {}}
            
            with open(file_path_obj, 'rb') as f:
                files = {'file': (file_path_obj.name, f, 'application/octet-stream')}
                data = {
                    'model_key': model_key,
                    'model_name': model_name,
                    'description': description
                }
                response = requests.post(
                    f"{self.api_base}/upload-safetensors",
                    files=files,
                    data=data,
                    timeout=300  # 5分钟超时，因为文件可能很大
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"上传safetensors文件失败: {str(e)}")
            return {"success": False, "message": f"上传失败: {str(e)}", "data": {}}
    
    def clear_model_cache(self, model_key: str) -> Dict[str, Any]:
        """
        清理指定模型的缓存
        
        Args:
            model_key: 模型键名
            
        Returns:
            Dict[str, Any]: API响应
        """
        try:
            response = requests.delete(f"{self.api_base}/cache/{model_key}", timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"清理模型缓存失败: {str(e)}")
            return {"success": False, "message": f"清理失败: {str(e)}", "data": {}}
    
    def clear_all_cache(self) -> Dict[str, Any]:
        """
        清理所有模型缓存
        
        Returns:
            Dict[str, Any]: API响应
        """
        try:
            response = requests.delete(f"{self.api_base}/cache", timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"清理所有缓存失败: {str(e)}")
            return {"success": False, "message": f"清理失败: {str(e)}", "data": {}}
    
    def get_model_display_info(self, models_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        获取模型的显示信息
        
        Args:
            models_data: 模型数据
            
        Returns:
            List[Dict[str, Any]]: 格式化的模型信息列表
        """
        display_models = []
        
        for model_key, model_info in models_data.items():
            display_info = {
                "key": model_key,
                "name": model_info.get("model_name", model_key),
                "description": model_info.get("description", ""),
                "type": model_info.get("model_type", "diffusers"),
                "is_custom": model_info.get("is_custom", False),
                "recommended_for": model_info.get("recommended_for", []),
                "cfg_scale_range": model_info.get("cfg_scale_range", [7.0, 12.0]),
                "steps_range": model_info.get("steps_range", [20, 50])
            }
            display_models.append(display_info)
        
        # 按类型和名称排序
        display_models.sort(key=lambda x: (
            0 if x["type"] == "safetensors" else 1,  # safetensors优先
            1 if x["is_custom"] else 0,              # 自定义模型其次
            x["name"]                                # 按名称排序
        ))
        
        return display_models
    
    def auto_detect_safetensors_files(self, search_dirs: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        自动检测safetensors文件
        
        Args:
            search_dirs: 搜索目录列表
            
        Returns:
            List[Dict[str, Any]]: 检测到的文件信息
        """
        if search_dirs is None:
            # 扩展搜索目录，包括更多可能的位置
            search_dirs = [".", "models", "data/uploads", "backend/models"]
        
        detected_files = []
        
        for search_dir in search_dirs:
            try:
                search_path = Path(search_dir).resolve()
                logger.info(f"搜索目录: {search_path}")
                
                if not search_path.exists():
                    logger.warning(f"搜索目录不存在: {search_path}")
                    continue
                
                # 首先搜索当前目录的直接文件
                for safetensors_file in search_path.glob("*.safetensors"):
                    if safetensors_file.is_file():
                        try:
                            file_stat = safetensors_file.stat()
                            file_info = {
                                "path": str(safetensors_file),
                                "name": safetensors_file.stem,
                                "size": file_stat.st_size,
                                "suggested_key": self._generate_model_key(safetensors_file.stem),
                                "suggested_name": safetensors_file.stem,
                                "modified_time": file_stat.st_mtime,
                                "directory": str(safetensors_file.parent)
                            }
                            detected_files.append(file_info)
                            logger.info(f"检测到文件: {safetensors_file.name} ({file_info['size'] / (1024*1024):.1f} MB)")
                        except Exception as file_e:
                            logger.warning(f"处理文件 {safetensors_file} 失败: {str(file_e)}")
                            continue
                
                # 然后递归搜索子目录（限制深度避免过深搜索）
                max_depth = 3
                for depth in range(1, max_depth + 1):
                    pattern = "/".join(["*"] * depth) + "/*.safetensors"
                    for safetensors_file in search_path.glob(pattern):
                        if safetensors_file.is_file():
                            try:
                                file_stat = safetensors_file.stat()
                                file_info = {
                                    "path": str(safetensors_file),
                                    "name": safetensors_file.stem,
                                    "size": file_stat.st_size,
                                    "suggested_key": self._generate_model_key(safetensors_file.stem),
                                    "suggested_name": safetensors_file.stem,
                                    "modified_time": file_stat.st_mtime,
                                    "directory": str(safetensors_file.parent)
                                }
                                # 避免重复添加
                                if not any(existing["path"] == file_info["path"] for existing in detected_files):
                                    detected_files.append(file_info)
                                    logger.info(f"检测到文件: {safetensors_file.name} ({file_info['size'] / (1024*1024):.1f} MB)")
                            except Exception as file_e:
                                logger.warning(f"处理文件 {safetensors_file} 失败: {str(file_e)}")
                                continue
                        
            except Exception as e:
                logger.warning(f"搜索目录 {search_dir} 失败: {str(e)}")
                continue
        
        # 按文件大小排序（大文件优先，可能是完整模型）
        detected_files.sort(key=lambda x: x["size"], reverse=True)
        
        logger.info(f"总共检测到 {len(detected_files)} 个safetensors文件")
        return detected_files
    
    def _generate_model_key(self, filename: str) -> str:
        """
        生成模型键名
        
        Args:
            filename: 文件名
            
        Returns:
            str: 生成的模型键名
        """
        # 清理文件名，生成合适的键名
        key = filename.lower()
        # 移除常见的版本号和后缀
        key = key.replace("_v", "_").replace("-v", "_")
        key = key.replace("_fp16", "").replace("_fp32", "")
        key = key.replace("_inpainting", "_inp")
        # 替换特殊字符
        key = key.replace(" ", "_").replace("-", "_").replace(".", "_")
        # 移除连续的下划线
        while "__" in key:
            key = key.replace("__", "_")
        # 移除开头和结尾的下划线
        key = key.strip("_")
        
        return key
