@echo off
:: 设置代码页为UTF-8，同时处理可能的错误
chcp 65001 >nul 2>&1
if errorlevel 1 (
    echo Setting UTF-8 encoding failed, using default encoding
)

:: 设置控制台标题
title Military Target Dataset Generation Platform - Quick Start

:: 设置环境变量确保Python使用UTF-8编码
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set LANG=zh_CN.UTF-8

echo ========================================
echo    Military Target Dataset Generation Platform - Quick Start
echo ========================================
echo.

echo [STARTUP] Starting backend service...
start "Backend Service" cmd /k "cd /d %~dp0backend && python -m uvicorn app.main:app --host 127.0.0.1 --port 8000 --reload"

echo [WAITING] Waiting for backend service to start...
timeout /t 8 /nobreak >nul

echo [STARTUP] Starting frontend application...
start "Frontend Application" cmd /k "cd /d %~dp0frontend && python src/main_window.py"

echo.
echo ========================================
echo [COMPLETED] Service startup completed!
echo.
echo Backend service: http://127.0.0.1:8000
echo Frontend application started
echo.
echo To stop services, please run stop.bat
echo ========================================
echo.
timeout /t 3 /nobreak >nul 