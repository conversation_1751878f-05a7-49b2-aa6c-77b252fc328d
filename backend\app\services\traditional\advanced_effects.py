"""
高级图片效果处理器
提供各种高级图片效果，包括滤镜、噪声处理、边缘检测等
"""
import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from PIL import Image, ImageFilter, ImageEnhance, ImageOps, ImageDraw
import numpy as np
import cv2
from scipy import ndimage
from skimage import filters, morphology, segmentation, feature
import random

from .core_types import ImageData, ProcessingResult

logger = logging.getLogger(__name__)


class AdvancedEffectsProcessor:
    """高级效果处理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化效果处理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {
            'default_kernel_size': 3,
            'noise_intensity': 0.1,
            'edge_threshold': 100
        }
    
    def apply_artistic_filter(
        self, 
        image_data: ImageData, 
        filter_type: str,
        intensity: float = 1.0,
        **kwargs
    ) -> ProcessingResult:
        """
        应用艺术滤镜
        
        Args:
            image_data: 图片数据
            filter_type: 滤镜类型
            intensity: 强度 (0.0-1.0)
            **kwargs: 其他参数
            
        Returns:
            ProcessingResult: 处理结果
        """
        try:
            image = image_data.image
            image_array = np.array(image)
            
            # 艺术滤镜映射
            filters_map = {
                'oil_painting': self._oil_painting_effect,
                'watercolor': self._watercolor_effect,
                'pencil_sketch': self._pencil_sketch_effect,
                'cartoon': self._cartoon_effect,
                'vintage': self._vintage_effect,
                'sepia': self._sepia_effect,
                'cross_process': self._cross_process_effect,
                'vignette': self._vignette_effect
            }
            
            if filter_type not in filters_map:
                raise ValueError(f"不支持的艺术滤镜: {filter_type}")
            
            # 应用滤镜
            filtered_array = filters_map[filter_type](image_array, intensity, **kwargs)
            
            # 混合原图和滤镜效果
            if intensity < 1.0:
                filtered_array = (
                    image_array * (1 - intensity) + 
                    filtered_array * intensity
                ).astype(np.uint8)
            
            result_image = Image.fromarray(filtered_array)
            
            result_data = ImageData(
                image=result_image,
                metadata={
                    **image_data.metadata,
                    'artistic_filter': filter_type,
                    'intensity': intensity,
                    'filter_params': kwargs
                }
            )
            
            return ProcessingResult(
                success=True,
                image=result_data,
                processing_time=0.0,
                metadata={'operation': 'artistic_filter', 'type': filter_type}
            )
            
        except Exception as e:
            logger.error(f"艺术滤镜应用失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )
    
    def add_noise(
        self, 
        image_data: ImageData, 
        noise_type: str = 'gaussian',
        intensity: float = 0.1,
        **kwargs
    ) -> ProcessingResult:
        """
        添加噪声
        
        Args:
            image_data: 图片数据
            noise_type: 噪声类型
            intensity: 噪声强度
            **kwargs: 其他参数
            
        Returns:
            ProcessingResult: 处理结果
        """
        try:
            image = image_data.image
            image_array = np.array(image).astype(np.float32) / 255.0
            
            if noise_type == 'gaussian':
                # 高斯噪声
                noise = np.random.normal(0, intensity, image_array.shape)
                noisy_array = image_array + noise
            elif noise_type == 'salt_pepper':
                # 椒盐噪声
                noisy_array = image_array.copy()
                salt_pepper_ratio = kwargs.get('salt_pepper_ratio', 0.5)
                
                # 盐噪声（白点）
                salt_coords = np.random.random(image_array.shape[:2]) < intensity * salt_pepper_ratio
                noisy_array[salt_coords] = 1.0
                
                # 椒噪声（黑点）
                pepper_coords = np.random.random(image_array.shape[:2]) < intensity * (1 - salt_pepper_ratio)
                noisy_array[pepper_coords] = 0.0
            elif noise_type == 'poisson':
                # 泊松噪声
                noisy_array = np.random.poisson(image_array * 255 * intensity) / (255 * intensity)
            else:
                raise ValueError(f"不支持的噪声类型: {noise_type}")
            
            # 限制值范围
            noisy_array = np.clip(noisy_array, 0, 1)
            noisy_array = (noisy_array * 255).astype(np.uint8)
            
            result_image = Image.fromarray(noisy_array)
            
            result_data = ImageData(
                image=result_image,
                metadata={
                    **image_data.metadata,
                    'noise_type': noise_type,
                    'noise_intensity': intensity,
                    'noise_params': kwargs
                }
            )
            
            return ProcessingResult(
                success=True,
                image=result_data,
                processing_time=0.0,
                metadata={'operation': 'add_noise', 'type': noise_type}
            )
            
        except Exception as e:
            logger.error(f"添加噪声失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )
    
    def edge_detection(
        self, 
        image_data: ImageData, 
        method: str = 'canny',
        **kwargs
    ) -> ProcessingResult:
        """
        边缘检测
        
        Args:
            image_data: 图片数据
            method: 检测方法
            **kwargs: 其他参数
            
        Returns:
            ProcessingResult: 处理结果
        """
        try:
            image = image_data.image
            
            # 转换为灰度图
            if image.mode != 'L':
                gray_image = image.convert('L')
            else:
                gray_image = image
            
            gray_array = np.array(gray_image)
            
            if method == 'canny':
                # Canny边缘检测
                low_threshold = kwargs.get('low_threshold', 50)
                high_threshold = kwargs.get('high_threshold', 150)
                edges = cv2.Canny(gray_array, low_threshold, high_threshold)
            elif method == 'sobel':
                # Sobel边缘检测
                sobel_x = cv2.Sobel(gray_array, cv2.CV_64F, 1, 0, ksize=3)
                sobel_y = cv2.Sobel(gray_array, cv2.CV_64F, 0, 1, ksize=3)
                edges = np.sqrt(sobel_x**2 + sobel_y**2)
                edges = np.uint8(edges / edges.max() * 255)
            elif method == 'laplacian':
                # 拉普拉斯边缘检测
                edges = cv2.Laplacian(gray_array, cv2.CV_64F)
                edges = np.uint8(np.absolute(edges))
            else:
                raise ValueError(f"不支持的边缘检测方法: {method}")
            
            # 转换回PIL图像
            if len(edges.shape) == 2:
                result_image = Image.fromarray(edges, mode='L')
            else:
                result_image = Image.fromarray(edges)
            
            result_data = ImageData(
                image=result_image,
                metadata={
                    **image_data.metadata,
                    'edge_method': method,
                    'edge_params': kwargs
                }
            )
            
            return ProcessingResult(
                success=True,
                image=result_data,
                processing_time=0.0,
                metadata={'operation': 'edge_detection', 'method': method}
            )
            
        except Exception as e:
            logger.error(f"边缘检测失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )
    
    def _oil_painting_effect(self, image_array: np.ndarray, intensity: float, **kwargs) -> np.ndarray:
        """油画效果"""
        radius = int(kwargs.get('radius', 7) * intensity)
        levels = int(kwargs.get('levels', 20) * intensity)
        
        # 使用OpenCV的油画效果
        if len(image_array.shape) == 3:
            result = cv2.xphoto.oilPainting(image_array, radius, levels)
        else:
            result = image_array
        
        return result
    
    def _watercolor_effect(self, image_array: np.ndarray, intensity: float, **kwargs) -> np.ndarray:
        """水彩效果"""
        # 模糊处理
        blur_radius = int(kwargs.get('blur_radius', 5) * intensity)
        blurred = cv2.bilateralFilter(image_array, 9, 75, 75)
        
        # 边缘保持
        edges = cv2.adaptiveThreshold(
            cv2.cvtColor(blurred, cv2.COLOR_RGB2GRAY),
            255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 7, 7
        )
        edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
        
        # 合并效果
        result = cv2.bitwise_and(blurred, edges)
        return result
    
    def _pencil_sketch_effect(self, image_array: np.ndarray, intensity: float, **kwargs) -> np.ndarray:
        """铅笔素描效果"""
        # 转换为灰度
        gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
        
        # 反转图像
        inverted = 255 - gray
        
        # 高斯模糊
        blur_radius = int(kwargs.get('blur_radius', 21) * intensity)
        blurred = cv2.GaussianBlur(inverted, (blur_radius, blur_radius), 0)
        
        # 颜色减淡混合
        sketch = cv2.divide(gray, 255 - blurred, scale=256)
        
        # 转换回RGB
        result = cv2.cvtColor(sketch, cv2.COLOR_GRAY2RGB)
        return result
    
    def _cartoon_effect(self, image_array: np.ndarray, intensity: float, **kwargs) -> np.ndarray:
        """卡通效果"""
        # 双边滤波
        bilateral = cv2.bilateralFilter(image_array, 15, 80, 80)
        
        # 边缘检测
        gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
        edges = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 7, 7)
        edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
        
        # 合并
        result = cv2.bitwise_and(bilateral, edges)
        return result
    
    def _vintage_effect(self, image_array: np.ndarray, intensity: float, **kwargs) -> np.ndarray:
        """复古效果"""
        # 调整色调
        result = image_array.copy().astype(np.float32)
        
        # 增加红色和黄色通道
        result[:, :, 0] = np.clip(result[:, :, 0] * (1 + 0.3 * intensity), 0, 255)  # 红色
        result[:, :, 1] = np.clip(result[:, :, 1] * (1 + 0.1 * intensity), 0, 255)  # 绿色
        result[:, :, 2] = np.clip(result[:, :, 2] * (1 - 0.2 * intensity), 0, 255)  # 蓝色
        
        return result.astype(np.uint8)
    
    def _sepia_effect(self, image_array: np.ndarray, intensity: float, **kwargs) -> np.ndarray:
        """棕褐色效果"""
        # 棕褐色变换矩阵
        sepia_matrix = np.array([
            [0.393, 0.769, 0.189],
            [0.349, 0.686, 0.168],
            [0.272, 0.534, 0.131]
        ])
        
        result = image_array.dot(sepia_matrix.T)
        result = np.clip(result, 0, 255)
        
        return result.astype(np.uint8)
    
    def _cross_process_effect(self, image_array: np.ndarray, intensity: float, **kwargs) -> np.ndarray:
        """交叉冲印效果"""
        result = image_array.copy().astype(np.float32)
        
        # 调整曲线
        result[:, :, 0] = np.power(result[:, :, 0] / 255.0, 0.8) * 255  # 红色
        result[:, :, 1] = np.power(result[:, :, 1] / 255.0, 1.2) * 255  # 绿色
        result[:, :, 2] = np.power(result[:, :, 2] / 255.0, 0.9) * 255  # 蓝色
        
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def _vignette_effect(self, image_array: np.ndarray, intensity: float, **kwargs) -> np.ndarray:
        """晕影效果"""
        h, w = image_array.shape[:2]
        
        # 创建径向渐变蒙版
        center_x, center_y = w // 2, h // 2
        max_radius = min(center_x, center_y)
        
        y, x = np.ogrid[:h, :w]
        distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        
        # 归一化距离
        distance = distance / max_radius
        
        # 创建晕影蒙版
        vignette_strength = kwargs.get('strength', 0.8) * intensity
        mask = 1 - np.clip(distance - (1 - vignette_strength), 0, vignette_strength) / vignette_strength
        
        # 应用晕影
        result = image_array.copy().astype(np.float32)
        for i in range(result.shape[2]):
            result[:, :, i] *= mask
        
        return np.clip(result, 0, 255).astype(np.uint8)
