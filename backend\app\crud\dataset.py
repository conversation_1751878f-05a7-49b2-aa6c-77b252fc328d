"""
图片数据CRUD操作
"""
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from datetime import datetime, timedelta
import hashlib
import os
from PIL import Image

from ..db.models import GeneratedImage, ImageAnnotation, Dataset
from ..core.config import get_settings


class ImageCRUD:
    """图片数据CRUD操作类"""

    @staticmethod
    def create_image(
        db: Session,
        filename: str,
        file_path: str,
        file_size: int,
        width: int,
        height: int,
        generation_type: str = "uploaded",
        **kwargs
    ) -> GeneratedImage:
        """创建图片记录"""
        # 计算文件哈希
        file_hash = ImageCRUD._calculate_file_hash(file_path)

        # 检查是否已存在相同哈希的图片
        existing = db.query(GeneratedImage).filter(
            GeneratedImage.file_hash == file_hash
        ).first()

        if existing:
            raise ValueError(f"图片已存在: {existing.filename}")

        image = GeneratedImage(
            filename=filename,
            file_path=file_path,
            file_size=file_size,
            file_hash=file_hash,
            width=width,
            height=height,
            generation_type=generation_type,
            **kwargs
        )

        db.add(image)
        db.commit()
        db.refresh(image)
        return image

    @staticmethod
    def get_image(db: Session, image_id: int) -> Optional[GeneratedImage]:
        """获取单个图片"""
        return db.query(GeneratedImage).filter(
            GeneratedImage.id == image_id
        ).first()

    @staticmethod
    def get_images(
        db: Session,
        skip: int = 0,
        limit: int = 50,
        generation_type: Optional[str] = None,
        military_target: Optional[str] = None,
        weather: Optional[str] = None,
        scene: Optional[str] = None,
        category: Optional[str] = None,
        is_favorite: Optional[bool] = None,
        tags: Optional[List[str]] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        search_text: Optional[str] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> Tuple[List[GeneratedImage], int]:
        """获取图片列表（带分页和筛选）"""
        query = db.query(GeneratedImage)

        # 应用筛选条件
        if generation_type:
            query = query.filter(GeneratedImage.generation_type == generation_type)

        if military_target:
            query = query.filter(GeneratedImage.military_target == military_target)

        if weather:
            query = query.filter(GeneratedImage.weather == weather)

        if scene:
            query = query.filter(GeneratedImage.scene == scene)

        if category:
            query = query.filter(GeneratedImage.category == category)

        if is_favorite is not None:
            query = query.filter(GeneratedImage.is_favorite == is_favorite)

        if tags:
            # 搜索包含任一标签的图片
            tag_conditions = []
            for tag in tags:
                tag_conditions.append(
                    func.json_extract(GeneratedImage.tags, f'$[*]').like(f'%{tag}%')
                )
            query = query.filter(or_(*tag_conditions))

        if date_from:
            query = query.filter(GeneratedImage.created_at >= date_from)

        if date_to:
            query = query.filter(GeneratedImage.created_at <= date_to)

        if search_text:
            # 在文件名、描述、提示词中搜索
            search_pattern = f"%{search_text}%"
            query = query.filter(
                or_(
                    GeneratedImage.filename.like(search_pattern),
                    GeneratedImage.description.like(search_pattern),
                    GeneratedImage.prompt.like(search_pattern)
                )
            )

        # 获取总数
        total = query.count()

        # 应用排序
        if sort_order.lower() == "desc":
            order_func = desc
        else:
            order_func = asc

        if hasattr(GeneratedImage, sort_by):
            query = query.order_by(order_func(getattr(GeneratedImage, sort_by)))
        else:
            query = query.order_by(desc(GeneratedImage.created_at))

        # 应用分页
        images = query.offset(skip).limit(limit).all()

        return images, total

    @staticmethod
    def update_image(
        db: Session,
        image_id: int,
        **kwargs
    ) -> Optional[GeneratedImage]:
        """更新图片信息"""
        image = db.query(GeneratedImage).filter(
            GeneratedImage.id == image_id
        ).first()

        if not image:
            return None

        # 更新字段
        for key, value in kwargs.items():
            if hasattr(image, key):
                setattr(image, key, value)

        image.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(image)
        return image

    @staticmethod
    def delete_image(db: Session, image_id: int) -> bool:
        """删除图片"""
        image = db.query(GeneratedImage).filter(
            GeneratedImage.id == image_id
        ).first()

        if not image:
            return False

        # 删除物理文件
        try:
            if os.path.exists(image.file_path):
                os.remove(image.file_path)
        except Exception as e:
            print(f"删除文件失败: {e}")

        # 删除数据库记录（级联删除标注）
        db.delete(image)
        db.commit()
        return True

    @staticmethod
    def delete_images_batch(db: Session, image_ids: List[int]) -> int:
        """批量删除图片"""
        images = db.query(GeneratedImage).filter(
            GeneratedImage.id.in_(image_ids)
        ).all()

        deleted_count = 0
        for image in images:
            try:
                # 删除物理文件
                if os.path.exists(image.file_path):
                    os.remove(image.file_path)

                # 删除数据库记录
                db.delete(image)
                deleted_count += 1
            except Exception as e:
                print(f"删除图片 {image.id} 失败: {e}")

        db.commit()
        return deleted_count

    @staticmethod
    def get_statistics(db: Session) -> Dict[str, Any]:
        """获取图片统计信息"""
        total_images = db.query(GeneratedImage).count()

        # 按生成类型统计
        type_stats = db.query(
            GeneratedImage.generation_type,
            func.count(GeneratedImage.id)
        ).group_by(GeneratedImage.generation_type).all()

        # 按军事目标统计
        target_stats = db.query(
            GeneratedImage.military_target,
            func.count(GeneratedImage.id)
        ).filter(
            GeneratedImage.military_target.isnot(None)
        ).group_by(GeneratedImage.military_target).all()

        # 最近7天的生成数量
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_count = db.query(GeneratedImage).filter(
            GeneratedImage.created_at >= week_ago
        ).count()

        return {
            "total_images": total_images,
            "generation_types": dict(type_stats),
            "military_targets": dict(target_stats),
            "recent_week_count": recent_count
        }

    @staticmethod
    def _calculate_file_hash(file_path: str) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""


class AnnotationCRUD:
    """标注数据CRUD操作类"""

    @staticmethod
    def create_annotation(
        db: Session,
        image_id: int,
        label: str,
        bbox_x: float,
        bbox_y: float,
        bbox_width: float,
        bbox_height: float,
        confidence: Optional[float] = None,
        source: str = "manual",
        **kwargs
    ) -> ImageAnnotation:
        """创建标注记录"""
        annotation = ImageAnnotation(
            image_id=image_id,
            label=label,
            bbox_x=bbox_x,
            bbox_y=bbox_y,
            bbox_width=bbox_width,
            bbox_height=bbox_height,
            confidence=confidence,
            source=source,
            **kwargs
        )

        db.add(annotation)
        db.commit()
        db.refresh(annotation)
        return annotation

    @staticmethod
    def get_annotations_by_image(db: Session, image_id: int) -> List[ImageAnnotation]:
        """获取图片的所有标注"""
        return db.query(ImageAnnotation).filter(
            ImageAnnotation.image_id == image_id
        ).all()

    @staticmethod
    def update_annotation(
        db: Session,
        annotation_id: int,
        **kwargs
    ) -> Optional[ImageAnnotation]:
        """更新标注信息"""
        annotation = db.query(ImageAnnotation).filter(
            ImageAnnotation.id == annotation_id
        ).first()

        if not annotation:
            return None

        # 更新字段
        for key, value in kwargs.items():
            if hasattr(annotation, key):
                setattr(annotation, key, value)

        annotation.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(annotation)
        return annotation

    @staticmethod
    def delete_annotation(db: Session, annotation_id: int) -> bool:
        """删除标注"""
        annotation = db.query(ImageAnnotation).filter(
            ImageAnnotation.id == annotation_id
        ).first()

        if not annotation:
            return False

        db.delete(annotation)
        db.commit()
        return True

    @staticmethod
    def delete_annotations_by_image(db: Session, image_id: int) -> int:
        """删除图片的所有标注"""
        annotations = db.query(ImageAnnotation).filter(
            ImageAnnotation.image_id == image_id
        ).all()

        count = len(annotations)
        for annotation in annotations:
            db.delete(annotation)

        db.commit()
        return count