"""
Stable Diffusion权重文件加载器
专门处理safetensors格式的SD模型权重文件
"""

import os
import logging
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime
import json

try:
    from safetensors import safe_open
    from safetensors.torch import load_file
    from diffusers import StableDiffusionPipeline
    import torch
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    DEPENDENCIES_AVAILABLE = False
    safe_open = None
    StableDiffusionPipeline = None
    torch = None

logger = logging.getLogger(__name__)


class SDWeightLoader:
    """Stable Diffusion权重文件加载器"""
    
    def __init__(self, cache_dir: str = "data/cache/sd_weights"):
        """
        初始化SD权重加载器
        
        Args:
            cache_dir: 缓存目录
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 缓存配置文件
        self.cache_config_file = self.cache_dir / "weight_cache.json"
        
        # 加载缓存配置
        self.cache_config = self._load_cache_config()
        
        # 当前加载的模型缓存
        self.loaded_models = {}
        
        logger.info(f"SD权重加载器初始化完成，缓存目录: {self.cache_dir}")
    
    def _load_cache_config(self) -> Dict[str, Any]:
        """加载缓存配置"""
        if self.cache_config_file.exists():
            try:
                with open(self.cache_config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载缓存配置失败: {str(e)}")
                return {}
        return {}
    
    def _save_cache_config(self):
        """保存缓存配置"""
        try:
            with open(self.cache_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存缓存配置失败: {str(e)}")
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                # 只读取文件的前1MB和最后1MB来计算哈希，提高速度
                chunk_size = 1024 * 1024  # 1MB
                hash_md5.update(f.read(chunk_size))
                
                # 跳到文件末尾前1MB
                file_size = os.path.getsize(file_path)
                if file_size > chunk_size * 2:
                    f.seek(-chunk_size, 2)
                    hash_md5.update(f.read(chunk_size))
                    
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {str(e)}")
            return ""
    
    def validate_safetensors_file(self, file_path: str) -> Tuple[bool, str, Dict[str, Any]]:
        """
        验证safetensors文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[bool, str, Dict]: (是否有效, 消息, 文件信息)
        """
        if not DEPENDENCIES_AVAILABLE:
            return False, "依赖库未安装，请安装safetensors和torch", {}
        
        try:
            # 规范化文件路径 - 支持相对路径和绝对路径
            original_path = file_path
            file_path_obj = Path(file_path)
            
            # 如果是相对路径，尝试相对于项目根目录解析
            if not file_path_obj.is_absolute():
                # 获取项目根目录（从当前文件位置推算）
                current_file = Path(__file__)
                project_root = current_file.parent.parent.parent.parent.parent
                file_path_obj = project_root / file_path_obj
            
            file_path_obj = file_path_obj.resolve()
            
            logger.info(f"验证safetensors文件: 原始路径={original_path}, 解析路径={file_path_obj}")
            
            if not file_path_obj.exists():
                # 提供更详细的错误信息
                parent_dir = file_path_obj.parent
                if parent_dir.exists():
                    available_files = [f.name for f in parent_dir.glob("*.safetensors")]
                    if available_files:
                        return False, f"文件不存在: {file_path_obj}\n目录中可用的safetensors文件: {', '.join(available_files[:5])}", {}
                    else:
                        return False, f"文件不存在: {file_path_obj}\n目录中没有找到safetensors文件", {}
                else:
                    return False, f"文件和目录都不存在: {file_path_obj}", {}
            
            if not file_path_obj.suffix.lower() == '.safetensors':
                return False, f"不是safetensors文件: {file_path_obj} (扩展名: {file_path_obj.suffix})", {}
            
            # 获取文件信息
            file_stat = file_path_obj.stat()
            file_info = {
                "path": str(file_path_obj),
                "size": file_stat.st_size,
                "modified_time": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                "hash": self._calculate_file_hash(str(file_path_obj))
            }
            
            # 进一步降低文件大小检查阈值，支持更小的模型文件和权重文件
            min_size = 10 * 1024  # 10KB，非常宽松的阈值，支持小的权重文件
            if file_info["size"] < min_size:
                return False, f"文件大小异常: {file_info['size']} bytes (最小要求: {min_size} bytes)", file_info
            
            # 验证safetensors格式
            if safe_open is None:
                return False, "safetensors库未正确导入", file_info
            
            with safe_open(str(file_path_obj), framework="pt", device="cpu") as f:
                keys = list(f.keys())
                if not keys:
                    return False, "safetensors文件为空", file_info
                
                file_info["weight_keys"] = keys[:10]  # 只保存前10个键名作为示例
                file_info["total_weights"] = len(keys)
                
                # 改进SD模型检测逻辑，支持更多格式和微调权重
                sd_indicators = [
                    # 原始SD格式
                    "model.diffusion_model",  # UNet
                    "first_stage_model",      # VAE
                    "cond_stage_model",       # Text Encoder
                    # Diffusers格式
                    "unet",
                    "vae",
                    "text_encoder",
                    # 其他常见格式
                    "model_ema",
                    "state_dict",
                    # UNet组件（微调权重常见）
                    "down_blocks",
                    "up_blocks",
                    "mid_block",
                    "conv_in",
                    "conv_out",
                    "time_embedding",
                    # LoRA和微调权重
                    "lora",
                    "adapter",
                    "delta",
                    # 其他神经网络组件
                    "attention",
                    "transformer",
                    "resnet",
                    "norm"
                ]
                
                # 检查是否包含任何SD相关组件
                found_indicators = []
                for key in keys:
                    key_lower = key.lower()
                    for indicator in sd_indicators:
                        if indicator in key_lower:
                            found_indicators.append(indicator)
                            break
                
                is_sd_model = len(found_indicators) > 0
                
                # 即使不是完整的SD模型，也可能是有用的权重文件
                if not is_sd_model:
                    # 检查是否是其他类型的神经网络权重
                    nn_indicators = ["weight", "bias", "running_mean", "running_var", "num_batches_tracked"]
                    has_nn_weights = any(
                        any(indicator in key.lower() for indicator in nn_indicators)
                        for key in keys[:20]  # 只检查前20个键
                    )
                    
                    if has_nn_weights:
                        file_info["is_sd_model"] = False
                        file_info["model_type"] = "neural_network"
                        file_info["found_indicators"] = found_indicators
                        return True, f"有效的神经网络权重文件，包含 {len(keys)} 个权重（可能不是标准SD模型）", file_info
                
                file_info["is_sd_model"] = is_sd_model
                file_info["model_type"] = "stable_diffusion" if is_sd_model else "unknown"
                file_info["found_indicators"] = found_indicators
                
                if is_sd_model:
                    return True, f"有效的SD模型文件，包含 {len(keys)} 个权重，检测到组件: {', '.join(found_indicators[:3])}", file_info
                else:
                    return True, f"有效的safetensors文件，包含 {len(keys)} 个权重（未检测到SD组件，但可以尝试加载）", file_info
                
        except Exception as e:
            logger.error(f"验证safetensors文件失败: {str(e)}")
            return False, f"验证失败: {str(e)}", {}
    
    def load_safetensors_model(
        self,
        file_path: str,
        model_key: str,
        device: str = "auto",
        torch_dtype: str = "auto"
    ) -> Tuple[str, str, Optional[Any]]:
        """
        加载safetensors模型
        
        Args:
            file_path: safetensors文件路径
            model_key: 模型键名
            device: 设备
            torch_dtype: 数据类型
            
        Returns:
            Tuple[str, str, Optional[Pipeline]]: (状态, 消息, 模型管道)
            状态可以是: 'success', 'missing_components', 'error'
        """
        if not DEPENDENCIES_AVAILABLE:
            return 'error', "依赖库未安装", None
        
        try:
            # 验证文件
            is_valid, validation_msg, file_info = self.validate_safetensors_file(file_path)
            if not is_valid:
                return 'error', f"文件验证失败: {validation_msg}", None
            
            # 检查缓存
            file_hash = file_info.get("hash", "")
            if file_hash and model_key in self.loaded_models:
                cached_info = self.cache_config.get(model_key, {})
                if cached_info.get("hash") == file_hash:
                    logger.info(f"使用缓存的模型: {model_key}")
                    return 'success', "从缓存加载成功", self.loaded_models[model_key]
            
            # 确定设备和数据类型
            if torch is None:
                return 'error', "torch库未正确导入", None
                
            if device == "auto":
                device = "cuda" if torch.cuda.is_available() else "cpu"
            
            if torch_dtype == "auto":
                torch_dtype = torch.float16 if device == "cuda" else torch.float32
            elif torch_dtype == "float16":
                torch_dtype = torch.float16
            elif torch_dtype == "float32":
                torch_dtype = torch.float32
            
            logger.info(f"开始加载safetensors模型: {file_path}")

            # 尝试加载模型，如果缺少组件则返回错误信息
            if StableDiffusionPipeline is None:
                return 'error', "diffusers库未正确导入", None
                
            try:
                # 首先尝试直接加载
                if StableDiffusionPipeline is None:
                    return 'error', "diffusers库未正确导入", None

                pipeline = StableDiffusionPipeline.from_single_file(
                    file_path,
                    torch_dtype=torch_dtype,
                    safety_checker=None,
                    requires_safety_checker=False,
                    use_safetensors=True,
                    load_safety_checker=False,
                    local_files_only=False  # 允许下载缺失组件
                )
            except Exception as e:
                error_str = str(e)
                # 更详细的错误分类
                if any(keyword in error_str for keyword in ["CLIPTextModel", "text_encoder", "tokenizer"]):
                    logger.warning(f"safetensors文件缺少文本编码器组件，将尝试作为UNet权重加载: {error_str}")
                    return 'missing_components', "文件缺少文本编码器，可能是一个UNet权重文件。", None
                elif any(keyword in error_str for keyword in ["VAE", "vae", "AutoencoderKL"]):
                    logger.warning(f"safetensors文件缺少VAE组件: {error_str}")
                    return 'missing_components', "文件缺少VAE组件，可能是一个部分权重文件。", None
                elif any(keyword in error_str for keyword in ["scheduler", "Scheduler"]):
                    logger.warning(f"safetensors文件缺少调度器组件: {error_str}")
                    return 'missing_components', "文件缺少调度器组件，可能是一个部分权重文件。", None
                elif "Connection" in error_str or "network" in error_str.lower():
                    logger.error(f"网络连接问题: {error_str}")
                    return 'error', "网络连接失败，请检查网络设置或使用本地模型文件", None
                else:
                    # 其他错误
                    logger.error(f"加载safetensors模型时发生未知错误: {error_str}")
                    return 'error', f"加载失败: {self._simplify_error_message(error_str)}", None
            
            # 移动到指定设备
            pipeline = pipeline.to(device)
            
            # 启用内存优化
            if device == "cuda":
                try:
                    pipeline.enable_model_cpu_offload()
                    pipeline.enable_xformers_memory_efficient_attention()
                except Exception as opt_e:
                    logger.warning(f"内存优化启用失败: {str(opt_e)}")
            
            # 缓存模型
            self.loaded_models[model_key] = pipeline
            
            # 更新缓存配置
            self.cache_config[model_key] = {
                "file_path": file_path,
                "hash": file_hash,
                "loaded_time": datetime.now().isoformat(),
                "device": device,
                "torch_dtype": str(torch_dtype),
                "file_info": file_info
            }
            self._save_cache_config()
            
            logger.info(f"safetensors模型加载成功: {model_key}")
            return 'success', "模型加载成功", pipeline
            
        except Exception as e:
            logger.error(f"加载safetensors模型失败: {str(e)}")
            return 'error', f"加载失败: {str(e)}", None
    
    def get_cached_models(self) -> Dict[str, Dict[str, Any]]:
        """获取缓存的模型信息"""
        return self.cache_config.copy()
    
    def clear_cache(self, model_key: Optional[str] = None):
        """清理缓存"""
        try:
            if model_key:
                # 清理指定模型
                if model_key in self.loaded_models:
                    del self.loaded_models[model_key]
                if model_key in self.cache_config:
                    del self.cache_config[model_key]
            else:
                # 清理所有缓存
                self.loaded_models.clear()
                self.cache_config.clear()
            
            self._save_cache_config()
            logger.info(f"缓存清理完成: {model_key or '全部'}")
            
        except Exception as e:
            logger.error(f"清理缓存失败: {str(e)}")
    
    def get_model_info(self, model_key: str) -> Optional[Dict[str, Any]]:
        """获取模型信息"""
        return self.cache_config.get(model_key)
    
    def is_model_loaded(self, model_key: str) -> bool:
        """检查模型是否已加载"""
        return model_key in self.loaded_models
    
    def get_loaded_model(self, model_key: str) -> Optional[Any]:
        """获取已加载的模型"""
        return self.loaded_models.get(model_key)

    def load_unet_weights(
        self,
        pipeline: Any,  # StableDiffusionPipeline
        file_path: str,
        device: str,
        torch_dtype: Any
    ) -> Tuple[bool, str]:
        """
        将UNet权重从safetensors文件加载到现有管道中
        
        Args:
            pipeline: 目标pipeline
            file_path: safetensors文件路径
            device: 设备
            torch_dtype: 数据类型
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            logger.info(f"开始将UNet权重从 {file_path} 应用到现有管道")
            
            # 加载safetensors文件中的状态字典
            state_dict = load_file(file_path, device="cpu")
            
            # 检查是否存在UNet权重
            unet_keys = [k for k in state_dict.keys() if k.startswith("unet.")]
            if not unet_keys:
                 # 尝试没有 "unet." 前缀的键
                unet_keys = [k for k in state_dict.keys() if any(prefix in k for prefix in ["down_blocks", "up_blocks", "mid_block"])]
                if not unet_keys:
                    return False, "文件中未找到UNet权重"
                
                # 为没有 "unet." 前缀的键添加前缀
                state_dict = {f"unet.{k}": v for k, v in state_dict.items()}


            # 加载UNet权重
            # pipeline.unet.load_state_dict(state_dict, strict=False)
            
            # 创建一个新的UNet模型并加载权重
            try:
                from diffusers.models import UNet2DConditionModel
            except ImportError:
                try:
                    from diffusers import UNet2DConditionModel
                except ImportError:
                    return False, "无法导入UNet2DConditionModel，请检查diffusers版本"
            
            # 从pipeline中获取UNet配置
            unet_config = pipeline.unet.config
            
            new_unet = UNet2DConditionModel(**unet_config)
            
            # 加载权重，忽略不匹配的键
            mismatched_keys = new_unet.load_state_dict(state_dict, strict=False)
            logger.warning(f"UNet权重加载不匹配的键: {mismatched_keys}")

            # 替换pipeline中的UNet
            pipeline.unet = new_unet

            # 移动到设备并设置数据类型
            pipeline.to(device=device, dtype=torch_dtype)
            
            logger.info("UNet权重应用成功")
            return True, "UNet权重应用成功"
            
        except Exception as e:
            logger.error(f"应用UNet权重失败: {str(e)}")
            return False, f"应用UNet权重失败: {str(e)}"

    def _simplify_error_message(self, error_message: str) -> str:
        """
        简化错误信息，使其更用户友好

        Args:
            error_message: 原始错误信息

        Returns:
            str: 简化后的错误信息
        """
        # 移除技术性的堆栈跟踪信息
        if "Traceback" in error_message:
            lines = error_message.split('\n')
            # 只保留最后的错误信息
            for line in reversed(lines):
                if line.strip() and not line.startswith(' '):
                    error_message = line.strip()
                    break

        # 简化常见错误
        simplifications = {
            "FileNotFoundError": "文件未找到",
            "PermissionError": "文件访问权限不足",
            "OSError": "系统错误",
            "RuntimeError": "运行时错误",
            "ValueError": "参数值错误",
            "TypeError": "类型错误",
            "ImportError": "模块导入错误",
            "ConnectionError": "网络连接错误",
            "TimeoutError": "操作超时"
        }

        for tech_term, simple_term in simplifications.items():
            if tech_term in error_message:
                error_message = error_message.replace(tech_term, simple_term)

        # 限制错误信息长度
        if len(error_message) > 200:
            error_message = error_message[:200] + "..."

        return error_message
