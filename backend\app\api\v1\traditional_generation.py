"""
传统图像生成API路由
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from ...services.traditional_generation.traditional_service import TraditionalGenerationService
from ...services.traditional.pic_resource_service import get_pic_resource_service
from ...core.dependencies import get_traditional_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/traditional", tags=["传统图像生成"])

# Pydantic模型
class TraditionalGenerationRequest(BaseModel):
    """传统生成请求"""
    military_target: str = Field(..., description="军事目标类型")
    weather: str = Field(..., description="天气条件")
    scene: str = Field(..., description="场景环境")
    num_images: int = Field(default=1, ge=1, le=10, description="生成图像数量")
    
    # 抠图参数
    matting_method: str = Field(default="grabcut", description="抠图方法")
    iterations: int = Field(default=5, ge=1, le=20, description="迭代次数")
    feather_radius: int = Field(default=3, ge=0, le=20, description="羽化半径")
    edge_smooth: bool = Field(default=True, description="边缘平滑")
    
    # 混合参数
    blend_mode: str = Field(default="normal", description="混合模式")
    opacity: float = Field(default=1.0, ge=0.0, le=1.0, description="透明度")
    color_match: bool = Field(default=True, description="色彩匹配")
    lighting_adjust: bool = Field(default=True, description="光照调整")
    shadow_generate: bool = Field(default=True, description="生成阴影")
    
    # 生成参数
    target_width: int = Field(default=512, description="目标宽度")
    target_height: int = Field(default=512, description="目标高度")

class TraditionalGenerationResponse(BaseModel):
    """传统生成响应"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

@router.post("/generate", response_model=TraditionalGenerationResponse)
async def generate_traditional_images(
    request: TraditionalGenerationRequest,
    service: TraditionalGenerationService = Depends(get_traditional_service)
):
    """
    生成传统合成图像
    """
    try:
        logger.info(f"收到传统生成请求: {request.military_target}, {request.weather}, {request.scene}")
        
        # 检查服务是否可用
        if not service.is_initialized:
            logger.warning("传统生成服务未初始化，尝试重新初始化...")
            try:
                await service.initialize()
            except Exception as e:
                logger.error(f"传统生成服务初始化失败: {str(e)}")
                raise HTTPException(
                    status_code=503, 
                    detail="传统生成服务暂时不可用，请检查配置"
                )
        
        # 准备生成参数
        generation_params = {
            "military_target": request.military_target,
            "weather": request.weather,
            "scene": request.scene,
            "num_images": request.num_images,
            
            # 抠图参数
            "matting_method": request.matting_method,
            "iterations": request.iterations,
            "feather_radius": request.feather_radius,
            "edge_smooth": request.edge_smooth,
            
            # 混合参数
            "blend_mode": request.blend_mode,
            "opacity": request.opacity,
            "color_match": request.color_match,
            "lighting_adjust": request.lighting_adjust,
            "shadow_generate": request.shadow_generate,
            
            # 生成参数
            "target_size": (request.target_width, request.target_height)
        }
        
        # 执行生成
        result = await service.generate_images(**generation_params)
        
        if result.get("success"):
            return TraditionalGenerationResponse(
                success=True,
                message=result.get("message", "生成成功"),
                data={
                    "results": result.get("results", []),
                    "total_generated": result.get("total_generated", 0),
                    "generation_params": generation_params
                }
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=result.get("message", "生成失败")
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"传统图像生成失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"传统图像生成失败: {str(e)}"
        )

@router.get("/status")
async def get_traditional_service_status(
    service: TraditionalGenerationService = Depends(get_traditional_service)
):
    """获取传统生成服务状态"""
    try:
        return {
            "success": True,
            "data": {
                "service_status": "running" if service.is_initialized else "initializing",
                "is_initialized": service.is_initialized,
                "message": "服务运行正常" if service.is_initialized else "服务正在初始化"
            }
        }

    except Exception as e:
        logger.error(f"获取传统生成服务状态失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取服务状态失败: {str(e)}"
        }

@router.get("/config")
async def get_traditional_generation_config():
    """获取传统生成配置选项"""
    try:
        return {
            "success": True,
            "data": {
                "military_targets": ["坦克", "战机", "舰艇"],
                "weather_conditions": ["晴天", "雨天", "雪天", "大雾", "夜间"],
                "scenes": ["城市", "岛屿", "乡村", "沙漠", "海洋"],
                "matting_methods": ["grabcut", "watershed", "rembg", "threshold"],
                "blend_modes": ["normal", "multiply", "screen", "overlay", "soft_light", "hard_light"],
                "target_sizes": [
                    {"name": "512x512", "width": 512, "height": 512},
                    {"name": "768x768", "width": 768, "height": 768},
                    {"name": "1024x1024", "width": 1024, "height": 1024}
                ]
            }
        }
    except Exception as e:
        logger.error(f"获取传统生成配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

# 初始化功能已通过依赖注入自动处理

@router.get("/available-assets")
async def get_available_assets():
    """获取可用的素材资源"""
    try:
        # 这里可以扫描raw_assets目录，返回可用的目标和背景图片
        import os
        from pathlib import Path
        
        assets_dir = Path("data/raw_assets")
        targets_dir = assets_dir / "targets"
        backgrounds_dir = assets_dir / "backgrounds"
        
        assets = {
            "targets": {},
            "backgrounds": {}
        }
        
        # 扫描目标图片
        if targets_dir.exists():
            for category in ["tanks", "aircraft", "ships"]:
                category_dir = targets_dir / category
                if category_dir.exists():
                    assets["targets"][category] = [
                        f.name for f in category_dir.glob("*.png") + category_dir.glob("*.jpg")
                    ]
        
        # 扫描背景图片
        if backgrounds_dir.exists():
            for scene in ["urban", "island", "rural", "desert", "ocean"]:
                scene_dir = backgrounds_dir / scene
                if scene_dir.exists():
                    assets["backgrounds"][scene] = [
                        f.name for f in scene_dir.glob("*.png") + scene_dir.glob("*.jpg")
                    ]
        
        return {
            "success": True,
            "data": assets
        }
        
    except Exception as e:
        logger.error(f"获取可用素材失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取素材失败: {str(e)}")


@router.get("/pic-resources")
async def get_pic_resources():
    """获取 pic_resource 目录中的可用图片资源"""
    try:
        pic_service = get_pic_resource_service()
        resources = pic_service.get_available_resources()

        return {
            "success": True,
            "message": "获取图片资源成功",
            "data": {
                "resources": resources,
                "total_categories": len(resources),
                "supported_formats": list(pic_service.supported_formats)
            }
        }

    except Exception as e:
        logger.error(f"获取图片资源失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取图片资源失败: {str(e)}")


@router.post("/refresh-pic-resources")
async def refresh_pic_resources():
    """刷新 pic_resource 图片资源缓存"""
    try:
        pic_service = get_pic_resource_service()
        pic_service.refresh_cache()

        return {
            "success": True,
            "message": "图片资源缓存刷新成功"
        }

    except Exception as e:
        logger.error(f"刷新图片资源缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"刷新缓存失败: {str(e)}")
