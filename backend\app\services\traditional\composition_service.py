"""
传统图像合成服务
"""
import os
import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from PIL import Image
import numpy as np
import cv2
import time
import random
from pathlib import Path

from ..common.annotation_generator import AnnotationGenerator
from .core_types import (
    ImageData, CompositionRequest, ProcessingResult, MattingConfig,
    BlendConfig, CompositionConfig, WeatherEffect, MattingMethod, BlendMode,
    BoundingBox, load_image, save_image, create_bbox_from_mask
)
from .mask_compositor import MattingFactory, auto_detect_best_method
from .image_processor import AdvancedBlendProcessor, BlendModeFactory
from .lighting_processor import LightingProcessor, analyze_scene_lighting
from .pic_resource_service import get_pic_resource_service

logger = logging.getLogger(__name__)


class TraditionalCompositionService:
    """传统图像合成服务"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化传统合成服务
        
        Args:
            config: 配置字典
        """
        self.config = config or {
            "output_dir": "data/generated/traditional",
            "assets_dir": "data/raw_assets",
            "quality": 95,
            "max_workers": 4,
            "default_size": (512, 512)
        }
        
        # 确保输出目录存在
        os.makedirs(self.config["output_dir"], exist_ok=True)
        
        # 初始化处理器
        self.annotation_generator = AnnotationGenerator()
        self.lighting_processor = LightingProcessor()

        # 初始化图片资源服务
        self.pic_resource_service = get_pic_resource_service()

        # 素材路径
        self.backgrounds_dir = os.path.join(self.config["assets_dir"], "backgrounds")
        # 使用pic_resource目录中的目标图片，确保使用正确的路径分隔符
        # 从项目根目录开始的绝对路径
        # __file__ 是 backend/app/services/traditional/composition_service.py
        # 需要向上4级到达项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
        self.targets_dir = os.path.join(project_root, "pic_resource", "targets")

        # 服务状态
        self.is_initialized = False

        logger.info("传统合成服务初始化完成")
    
    async def initialize(self) -> bool:
        """
        初始化服务
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 检查素材目录
            if not os.path.exists(self.backgrounds_dir):
                logger.warning(f"背景素材目录不存在: {self.backgrounds_dir}")
                os.makedirs(self.backgrounds_dir, exist_ok=True)
            
            if not os.path.exists(self.targets_dir):
                logger.warning(f"目标素材目录不存在: {self.targets_dir}")
                os.makedirs(self.targets_dir, exist_ok=True)
            
            self.is_initialized = True
            logger.info("传统合成服务初始化成功")
            return True
        except Exception as e:
            logger.error(f"传统合成服务初始化失败: {str(e)}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        self.is_initialized = False
        logger.info("传统合成服务资源清理完成")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            Dict: 服务状态信息
        """
        return {
            "initialized": self.is_initialized,
            "output_dir": self.config["output_dir"],
            "quality": self.config["quality"],
            "available_backgrounds": self._count_assets("backgrounds"),
            "available_targets": self._count_assets("targets")
        }
    
    async def compose_image(self, request: CompositionRequest) -> ProcessingResult:
        """
        合成单张图像
        
        Args:
            request: 合成请求
            
        Returns:
            ProcessingResult: 合成结果
        """
        start_time = time.time()
        
        try:
            # 加载图像
            background_data = load_image(request.background_path)
            target_data = load_image(request.target_path)
            
            # 执行抠图（带超时保护）
            try:
                matting_processor = MattingFactory.create_matting_processor(request.matting_config)

                # 为了提高处理速度，生成一个合理的边界框
                bbox = self._generate_auto_bbox(target_data)

                # 使用线程超时机制
                import threading
                import queue

                result_queue = queue.Queue()

                def matting_worker():
                    try:
                        result = matting_processor.extract_foreground(target_data, bbox)
                        result_queue.put(result)
                    except Exception as e:
                        result_queue.put(ProcessingResult(
                            success=False,
                            error_message=str(e),
                            processing_time=0.0
                        ))

                # 启动抠图线程
                matting_thread = threading.Thread(target=matting_worker)
                matting_thread.daemon = True
                matting_thread.start()

                # 等待结果，最多30秒
                try:
                    matting_result = result_queue.get(timeout=30)
                except queue.Empty:
                    logger.error("抠图处理超时，使用简单蒙版")
                    # 创建一个简单的蒙版作为备选
                    if isinstance(target_data.image, Image.Image):
                        width, height = target_data.image.size
                    else:
                        height, width = target_data.image.shape[:2]

                    simple_mask = np.ones((height, width), dtype=np.uint8) * 255
                    matting_result = ProcessingResult(
                        success=True,
                        mask=simple_mask,
                        processing_time=30.0,
                        metadata={'method': 'timeout_fallback'}
                    )

            except Exception as e:
                logger.error(f"抠图处理异常: {str(e)}")
                # 创建备用蒙版
                if isinstance(target_data.image, Image.Image):
                    width, height = target_data.image.size
                else:
                    height, width = target_data.image.shape[:2]

                simple_mask = np.ones((height, width), dtype=np.uint8) * 255
                matting_result = ProcessingResult(
                    success=True,
                    mask=simple_mask,
                    processing_time=0.0,
                    metadata={'method': 'error_fallback'}
                )
            
            if not matting_result.success:
                return ProcessingResult(
                    success=False,
                    error_message=f"抠图失败: {matting_result.error_message}",
                    processing_time=time.time() - start_time
                )
            
            # 调整目标图像尺寸和位置
            target_data, mask = self._adjust_target_transform(
                target_data, matting_result.mask, request.composition_config
            )
            
            # 分析场景光照
            lighting_info = analyze_scene_lighting(np.array(background_data.image))
            
            # 应用天气效果
            weather_result = self.lighting_processor.apply_weather_effect(
                background_data, request.weather_effect
            )
            if weather_result.success and weather_result.image:
                background_data = weather_result.image
            
            # 执行图层混合
            blend_processor = AdvancedBlendProcessor(request.blend_config)
            blend_result = blend_processor.blend_layers(background_data, target_data, mask)

            if not blend_result.success:
                return ProcessingResult(
                    success=False,
                    error_message=f"图层混合失败: {blend_result.error_message}",
                    processing_time=time.time() - start_time
                )

            # 保存结果
            if request.output_path and blend_result.image:
                save_image(blend_result.image, request.output_path, self.config["quality"])

                # 保存到数据库
                self._save_to_database(
                    blend_result.image,
                    request.output_path,
                    request.military_target,
                    request.weather_effect.value if request.weather_effect else "unknown",
                    request.scene_type,
                    time.time() - start_time,
                    blend_result.metadata
                )
            
            processing_time = time.time() - start_time
            
            return ProcessingResult(
                success=True,
                image=blend_result.image,
                processing_time=processing_time,
                metadata={
                    "matting_time": matting_result.processing_time,
                    "blend_time": blend_result.processing_time,
                    "lighting_info": lighting_info,
                    "weather_effect": request.weather_effect.value
                }
            )
            
        except Exception as e:
            logger.error(f"图像合成失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    async def generate_images(
        self,
        military_target: str,
        weather: str,
        scene: str,
        num_images: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成图像（兼容原接口）
        
        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            num_images: 生成图像数量
            **kwargs: 其他参数
            
        Returns:
            Dict: 生成结果
        """
        if not self.is_initialized:
            raise RuntimeError("服务未初始化")
        
        try:
            logger.info(f"开始传统合成: {military_target} + {weather} + {scene}")
            
            # 获取合成配置
            matting_config = self._create_matting_config(kwargs)
            blend_config = self._create_blend_config(weather, scene, kwargs)
            composition_config = self._create_composition_config(kwargs)
            
            results = []
            for i in range(num_images):
                result = await self._compose_single_image(
                    military_target, weather, scene, i,
                    matting_config, blend_config, composition_config
                )
                if result:
                    results.append(result)
            
            return {
                "success": True,
                "message": f"成功生成 {len(results)} 张图像",
                "results": results,
                "total_generated": len(results)
            }
            
        except Exception as e:
            logger.error(f"传统合成失败: {str(e)}")
            return {
                "success": False,
                "message": f"生成失败: {str(e)}",
                "results": [],
                "total_generated": 0
            }
    
    def _create_matting_config(self, kwargs: Dict[str, Any]) -> MattingConfig:
        """创建抠图配置"""
        method_str = kwargs.get('matting_method', 'threshold')  # 默认使用更快的阈值方法
        try:
            method = MattingMethod(method_str.lower())
        except ValueError:
            method = MattingMethod.THRESHOLD  # 使用更快的默认方法

        # 为了提高速度，减少迭代次数
        iterations = kwargs.get('matting_iterations', 3)  # 从5减少到3

        return MattingConfig(
            method=method,
            iterations=iterations,
            margin=kwargs.get('matting_margin', 10),
            feather_radius=kwargs.get('feather_radius', 2),  # 减少羽化半径
            edge_smooth=kwargs.get('edge_smooth', False),  # 关闭边缘平滑以提高速度
            auto_refine=kwargs.get('auto_refine', False),  # 关闭自动优化以提高速度
            confidence_threshold=kwargs.get('confidence_threshold', 0.8)
        )
    
    def _create_blend_config(self, weather: str, scene: str, kwargs: Dict[str, Any]) -> BlendConfig:
        """创建混合配置"""
        # 根据天气和场景推荐混合模式
        recommended_mode = BlendModeFactory.recommend_mode_for_scene(scene, weather)
        
        mode_str = kwargs.get('blend_mode', recommended_mode.value)
        try:
            mode = BlendMode(mode_str.lower())
        except ValueError:
            mode = recommended_mode
        
        # 调整默认透明度，确保背景可见
        default_opacity = 0.85 if mode == BlendMode.NORMAL else 0.9

        return BlendConfig(
            mode=mode,
            opacity=kwargs.get('opacity', default_opacity),  # 降低默认不透明度
            color_match=kwargs.get('color_match', True),
            lighting_adjust=kwargs.get('lighting_adjust', True),
            shadow_generate=kwargs.get('shadow_generate', True),
            shadow_opacity=kwargs.get('shadow_opacity', 0.2),  # 降低阴影不透明度
            shadow_blur=kwargs.get('shadow_blur', 8)  # 增加阴影模糊，更自然
        )
    
    def _create_composition_config(self, kwargs: Dict[str, Any]) -> CompositionConfig:
        """创建合成配置"""
        return CompositionConfig(
            target_size=kwargs.get('target_size', self.config['default_size']),
            target_position=kwargs.get('target_position'),
            target_scale=kwargs.get('target_scale', 1.0),
            rotation_angle=kwargs.get('rotation_angle', 0.0),
            flip_horizontal=kwargs.get('flip_horizontal', False),
            flip_vertical=kwargs.get('flip_vertical', False)
        )

    async def _compose_single_image(
        self,
        military_target: str,
        weather: str,
        scene: str,
        index: int,
        matting_config: MattingConfig,
        blend_config: BlendConfig,
        composition_config: CompositionConfig
    ) -> Optional[Dict[str, Any]]:
        """
        合成单张图像（内部方法）
        """
        try:
            # 选择素材
            background_path = self._select_background_from_pic_resource(weather, scene)
            target_path = self._select_target(military_target)

            if not background_path or not target_path:
                logger.warning(f"无法找到合适的素材: bg={background_path}, target={target_path}")
                return None

            # 创建合成请求
            weather_effect = WeatherEffect(weather.lower()) if weather.lower() in [e.value for e in WeatherEffect] else WeatherEffect.SUNNY

            filename = f"{military_target}_{weather}_{scene}_{index:03d}.jpg"
            output_path = os.path.join(self.config["output_dir"], filename)

            request = CompositionRequest(
                background_path=background_path,
                target_path=target_path,
                weather_effect=weather_effect,
                military_target=military_target,
                scene_type=scene,
                matting_config=matting_config,
                blend_config=blend_config,
                composition_config=composition_config,
                output_path=output_path
            )

            # 执行合成
            result = await self.compose_image(request)

            if not result.success:
                logger.error(f"合成失败: {result.error_message}")
                return None

            # 生成标注
            if result.image and result.image.mask is not None:
                bbox = create_bbox_from_mask(result.image.mask)
                bbox_coords = [bbox.x, bbox.y, bbox.width, bbox.height]
                segmentation = result.image.mask.tolist()
            else:
                # 使用默认标注生成器
                bbox_coords, segmentation = self.annotation_generator.generate_auto_annotation(
                    output_path, military_target
                )

            # 保存到数据库
            if result.image:
                try:
                    await self._save_to_database(
                        result.image, output_path, military_target, weather, scene,
                        result.processing_time, result.metadata
                    )
                    logger.info(f"传统合成图片已保存到数据库: {filename}")
                except Exception as e:
                    logger.error(f"保存到数据库失败: {str(e)}")
                    # 不影响图片生成流程，继续返回结果

            return {
                "image_path": output_path,
                "filename": filename,
                "target_type": military_target,
                "weather": weather,
                "scene": scene,
                "bbox": bbox_coords,
                "segmentation": segmentation,
                "size": composition_config.target_size,
                "processing_time": result.processing_time,
                "metadata": result.metadata
            }

        except Exception as e:
            logger.error(f"合成单张图像失败: {str(e)}")
            return None

    def _select_background_from_pic_resource(self, weather: str, scene: str) -> Optional[str]:
        """从 pic_resource 目录选择背景图像"""
        try:
            # 首先尝试从 pic_resource 获取图片
            background_path = self.pic_resource_service.get_random_image(weather, scene)

            if background_path and os.path.exists(background_path):
                logger.info(f"从 pic_resource 选择背景图片: {background_path}")
                return background_path
            else:
                logger.warning(f"pic_resource 中未找到匹配图片: {weather}/{scene}")
                # 降级到原有的背景选择逻辑
                return self._select_background(scene)

        except Exception as e:
            logger.error(f"从 pic_resource 选择背景失败: {str(e)}")
            # 降级到原有的背景选择逻辑
            return self._select_background(scene)

    def _select_background(self, scene: str) -> Optional[str]:
        """选择背景图像"""
        try:
            if not os.path.exists(self.backgrounds_dir):
                return None

            # 查找匹配的背景文件
            scene_patterns = {
                'urban': ['city', 'urban', 'street', 'building'],
                'desert': ['desert', 'sand', 'dune'],
                'ocean': ['ocean', 'sea', 'water', 'naval'],
                'forest': ['forest', 'tree', 'wood'],
                'mountain': ['mountain', 'hill', 'peak']
            }

            patterns = scene_patterns.get(scene.lower(), [scene.lower()])

            # 搜索匹配的文件
            for pattern in patterns:
                for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                    for file in os.listdir(self.backgrounds_dir):
                        if pattern in file.lower() and file.lower().endswith(ext):
                            return os.path.join(self.backgrounds_dir, file)

            # 如果没有找到匹配的，随机选择一个
            files = [f for f in os.listdir(self.backgrounds_dir)
                    if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
            if files:
                return os.path.join(self.backgrounds_dir, random.choice(files))

            return None

        except Exception as e:
            logger.error(f"选择背景图像失败: {str(e)}")
            return None

    def _select_target(self, military_target: str) -> Optional[str]:
        """选择目标图像"""
        try:
            logger.info(f"正在选择目标图片: {military_target}, 目标目录: {self.targets_dir}")

            if not os.path.exists(self.targets_dir):
                logger.error(f"目标目录不存在: {self.targets_dir}")
                return None

            # 中文到英文的映射
            target_mapping = {
                "坦克": "tanks",
                "战机": "aircraft",
                "舰艇": "ships"
            }

            # 获取对应的英文目录名
            target_dir_name = target_mapping.get(military_target, military_target.lower())
            target_subdir = os.path.join(self.targets_dir, target_dir_name)

            logger.info(f"映射目标类型: {military_target} -> {target_dir_name}")
            logger.info(f"目标子目录: {target_subdir}")

            # 首先尝试在对应的子目录中查找
            if os.path.exists(target_subdir):
                files = [f for f in os.listdir(target_subdir)
                        if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
                logger.info(f"在 {target_subdir} 中找到 {len(files)} 个图片文件: {files}")
                if files:
                    selected_file = os.path.join(target_subdir, random.choice(files))
                    logger.info(f"从 {target_subdir} 选择目标图片: {selected_file}")
                    return selected_file
            else:
                logger.warning(f"目标子目录不存在: {target_subdir}")

            # 查找匹配的目标文件（原有逻辑作为备选）
            target_patterns = {
                'tanks': ['tank', 'armor', 'vehicle'],
                'aircraft': ['aircraft', 'plane', 'jet', 'fighter'],
                'ships': ['ship', 'warship', 'naval', 'vessel']
            }

            # 使用映射后的英文目录名
            patterns = target_patterns.get(target_dir_name, ['tank', 'armor', 'vehicle'])

            # 搜索匹配的文件
            for pattern in patterns:
                for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                    for file in os.listdir(self.targets_dir):
                        if pattern in file.lower() and file.lower().endswith(ext):
                            return os.path.join(self.targets_dir, file)

            # 如果没有找到匹配的，随机选择一个
            files = [f for f in os.listdir(self.targets_dir)
                    if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
            if files:
                return os.path.join(self.targets_dir, random.choice(files))

            return None

        except Exception as e:
            logger.error(f"选择目标图像失败: {str(e)}")
            return None

    def _generate_auto_bbox(self, image_data: ImageData) -> BoundingBox:
        """
        自动生成边界框以提高抠图速度

        Args:
            image_data: 图像数据

        Returns:
            BoundingBox: 自动生成的边界框
        """
        try:
            # 获取图像尺寸
            if isinstance(image_data.image, Image.Image):
                width, height = image_data.image.size
            else:
                height, width = image_data.image.shape[:2]

            # 使用图像中心的80%区域作为边界框，这样可以避免边缘噪声
            margin_x = int(width * 0.1)  # 10%边距
            margin_y = int(height * 0.1)  # 10%边距

            bbox_width = width - 2 * margin_x
            bbox_height = height - 2 * margin_y

            # 确保边界框不会太小
            min_size = 50
            if bbox_width < min_size:
                margin_x = max(0, (width - min_size) // 2)
                bbox_width = width - 2 * margin_x

            if bbox_height < min_size:
                margin_y = max(0, (height - min_size) // 2)
                bbox_height = height - 2 * margin_y

            return BoundingBox(
                x=margin_x,
                y=margin_y,
                width=bbox_width,
                height=bbox_height
            )

        except Exception as e:
            logger.warning(f"生成自动边界框失败: {str(e)}, 使用默认边界框")
            # 返回一个安全的默认边界框
            return BoundingBox(x=10, y=10, width=100, height=100)



    async def _save_to_database(
        self,
        image_data: ImageData,
        file_path: str,
        military_target: str,
        weather: str,
        scene: str,
        processing_time: float,
        metadata: dict
    ):
        """
        保存传统合成图片到数据库

        Args:
            image_data: 图片数据
            file_path: 文件路径
            military_target: 军事目标
            weather: 天气
            scene: 场景
            processing_time: 处理时间
            metadata: 元数据
        """
        try:
            from ...crud.dataset import ImageCRUD
            from ...db.session import get_db
            import os

            # 获取图片信息
            if isinstance(image_data.image, Image.Image):
                width, height = image_data.image.size
                format_name = image_data.image.format or "JPEG"
            else:
                # 如果是numpy数组，转换为PIL图像获取信息
                pil_image = Image.fromarray(image_data.image)
                width, height = pil_image.size
                format_name = "JPEG"

            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            filename = os.path.basename(file_path)

            # 获取数据库会话
            db_gen = get_db()
            db = next(db_gen)

            try:
                # 创建图片记录
                image_record = ImageCRUD.create_image(
                    db=db,
                    filename=filename,
                    file_path=file_path,
                    file_size=file_size,
                    width=width,
                    height=height,
                    format=format_name,
                    generation_type="traditional",  # 标记为传统生成
                    military_target=military_target,
                    weather=weather,
                    scene=scene,
                    generation_id=f"traditional_{int(time.time())}",
                    description=f"传统合成图片: {military_target} in {weather} {scene}",
                    tags=[military_target, weather, scene, "traditional"],
                    category="traditional_generation"
                )

                logger.info(f"传统合成图片已保存到数据库: {filename} (ID: {image_record.id})")

            finally:
                db.close()

        except Exception as e:
            logger.error(f"保存传统合成图片到数据库失败: {str(e)}")
            # 不抛出异常，避免影响图片生成流程

    def _count_assets(self, asset_type: str) -> int:
        """统计素材数量"""
        try:
            asset_dir = os.path.join(self.config["assets_dir"], asset_type)
            if not os.path.exists(asset_dir):
                return 0

            files = [f for f in os.listdir(asset_dir)
                    if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
            return len(files)

        except Exception:
            return 0

    def _adjust_target_transform(
        self,
        target_data: ImageData,
        mask: np.ndarray,
        config: CompositionConfig
    ) -> Tuple[ImageData, np.ndarray]:
        """调整目标图像变换"""
        try:
            image = np.array(target_data.image)

            # 缩放
            if config.target_scale != 1.0:
                height, width = image.shape[:2]
                new_height = int(height * config.target_scale)
                new_width = int(width * config.target_scale)
                image = cv2.resize(image, (new_width, new_height))
                mask = cv2.resize(mask, (new_width, new_height))

            # 旋转
            if config.rotation_angle != 0.0:
                height, width = image.shape[:2]
                center = (width // 2, height // 2)
                rotation_matrix = cv2.getRotationMatrix2D(center, config.rotation_angle, 1.0)
                image = cv2.warpAffine(image, rotation_matrix, (width, height))
                mask = cv2.warpAffine(mask, rotation_matrix, (width, height))

            # 翻转
            if config.flip_horizontal:
                image = cv2.flip(image, 1)
                mask = cv2.flip(mask, 1)

            if config.flip_vertical:
                image = cv2.flip(image, 0)
                mask = cv2.flip(mask, 0)

            # 调整到目标尺寸
            if image.shape[:2] != config.target_size:
                image = cv2.resize(image, config.target_size)
                mask = cv2.resize(mask, config.target_size)

            return ImageData(image=Image.fromarray(image)), mask

        except Exception as e:
            logger.error(f"目标变换失败: {str(e)}")
            return target_data, mask
