"""
自定义模型管理器
支持用户添加和管理自定义训练的模型
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime

try:
    from safetensors import safe_open
    SAFETENSORS_AVAILABLE = True
except ImportError:
    SAFETENSORS_AVAILABLE = False
    safe_open = None

logger = logging.getLogger(__name__)

class CustomModelManager:
    """自定义模型管理器"""
    
    def __init__(self, models_dir: str = "models/custom"):
        """
        初始化自定义模型管理器
        
        Args:
            models_dir: 自定义模型存储目录
        """
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # 自定义模型配置文件
        self.config_file = self.models_dir / "custom_models.json"
        
        # 加载现有配置
        self.custom_models = self._load_custom_models()
        
        logger.info(f"自定义模型管理器初始化完成，目录: {self.models_dir}")
    
    def _load_custom_models(self) -> Dict[str, Dict[str, Any]]:
        """加载自定义模型配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载自定义模型配置失败: {str(e)}")
                return {}
        return {}
    
    def _save_custom_models(self):
        """保存自定义模型配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.custom_models, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存自定义模型配置失败: {str(e)}")
    
    def add_custom_model(
        self,
        model_key: str,
        model_name: str,
        model_path: str,
        description: str = "",
        model_type: str = "diffusers",
        overwrite: bool = False, # 新增参数
        **kwargs
    ) -> Tuple[bool, str]:
        """
        添加自定义模型
        
        Args:
            model_key: 模型唯一标识符
            model_name: 模型显示名称
            model_path: 模型文件路径（可以是本地路径或HuggingFace模型ID）
            description: 模型描述
            model_type: 模型类型 (diffusers, safetensors, checkpoint)
            overwrite: 如果模型键已存在，是否覆盖
            **kwargs: 其他模型参数
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 检查模型键是否已存在，如果允许覆盖则跳过此检查
            if model_key in self.custom_models and not overwrite:
                return False, f"模型键 '{model_key}' 已存在"
            
            # 验证模型路径
            is_local_path = os.path.exists(model_path)
            is_huggingface_id = "/" in model_path and not os.path.exists(model_path)
            
            if not is_local_path and not is_huggingface_id:
                return False, f"无效的模型路径: {model_path}"
            
            # 创建模型配置
            model_config = {
                "model_key": model_key,
                "model_name": model_name,
                "model_path": model_path,
                "description": description,
                "model_type": model_type,
                "is_local": is_local_path,
                "added_at": datetime.now().isoformat(),
                "enabled": True,
                
                # 默认生成参数
                "cfg_scale_range": kwargs.get("cfg_scale_range", [7.0, 12.0]),
                "steps_range": kwargs.get("steps_range", [20, 50]),
                "target_size_bias": kwargs.get("target_size_bias", "medium"),
                "recommended_for": kwargs.get("recommended_for", ["自定义场景"]),
                
                # 其他参数
                **{k: v for k, v in kwargs.items() if k not in [
                    "cfg_scale_range", "steps_range", "target_size_bias", "recommended_for"
                ]}
            }
            
            # 如果是本地模型，尝试复制到自定义模型目录
            if is_local_path and model_type in ["safetensors", "checkpoint"]:
                target_dir = self.models_dir / model_key
                target_dir.mkdir(exist_ok=True)
                
                source_path = Path(model_path)
                if source_path.is_file():
                    # 单文件模型
                    target_file = target_dir / source_path.name
                    shutil.copy2(source_path, target_file)
                    model_config["local_path"] = str(target_file)
                elif source_path.is_dir():
                    # 目录模型
                    target_model_dir = target_dir / "model"
                    if target_model_dir.exists():
                        shutil.rmtree(target_model_dir)
                    shutil.copytree(source_path, target_model_dir)
                    model_config["local_path"] = str(target_model_dir)
            
            # 添加或更新到配置
            self.custom_models[model_key] = model_config
            self._save_custom_models()
            
            logger.info(f"成功添加/更新自定义模型: {model_key}")
            return True, "自定义模型添加/更新成功"
            
        except Exception as e:
            logger.error(f"添加/更新自定义模型失败: {str(e)}")
            return False, f"添加/更新失败: {str(e)}"
    
    def remove_custom_model(self, model_key: str, delete_files: bool = False) -> Tuple[bool, str]:
        """
        移除自定义模型
        
        Args:
            model_key: 模型键
            delete_files: 是否删除模型文件
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            if model_key not in self.custom_models:
                return False, f"模型 '{model_key}' 不存在"
            
            model_config = self.custom_models[model_key]
            
            # 删除模型文件
            if delete_files and "local_path" in model_config:
                local_path = Path(model_config["local_path"])
                if local_path.exists():
                    if local_path.is_file():
                        local_path.unlink()
                    elif local_path.is_dir():
                        shutil.rmtree(local_path)
                
                # 删除模型目录
                model_dir = self.models_dir / model_key
                if model_dir.exists():
                    shutil.rmtree(model_dir)
            
            # 从配置中移除
            del self.custom_models[model_key]
            self._save_custom_models()
            
            logger.info(f"成功移除自定义模型: {model_key}")
            return True, "自定义模型移除成功"
            
        except Exception as e:
            logger.error(f"移除自定义模型失败: {str(e)}")
            return False, f"移除失败: {str(e)}"
    
    def update_custom_model(self, model_key: str, **updates) -> Tuple[bool, str]:
        """
        更新自定义模型配置
        
        Args:
            model_key: 模型键
            **updates: 要更新的字段
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            if model_key not in self.custom_models:
                return False, f"模型 '{model_key}' 不存在"
            
            # 更新配置
            self.custom_models[model_key].update(updates)
            self.custom_models[model_key]["updated_at"] = datetime.now().isoformat()
            
            self._save_custom_models()
            
            logger.info(f"成功更新自定义模型: {model_key}")
            return True, "自定义模型更新成功"
            
        except Exception as e:
            logger.error(f"更新自定义模型失败: {str(e)}")
            return False, f"更新失败: {str(e)}"
    
    def get_custom_models(self) -> Dict[str, Dict[str, Any]]:
        """获取所有自定义模型"""
        return self.custom_models.copy()
    
    def get_enabled_custom_models(self) -> Dict[str, Dict[str, Any]]:
        """获取启用的自定义模型"""
        return {
            key: config for key, config in self.custom_models.items()
            if config.get("enabled", True)
        }
    
    def get_custom_model(self, model_key: str) -> Optional[Dict[str, Any]]:
        """获取指定的自定义模型配置"""
        return self.custom_models.get(model_key)
    
    def enable_custom_model(self, model_key: str) -> Tuple[bool, str]:
        """启用自定义模型"""
        return self.update_custom_model(model_key, enabled=True)
    
    def disable_custom_model(self, model_key: str) -> Tuple[bool, str]:
        """禁用自定义模型"""
        return self.update_custom_model(model_key, enabled=False)
    
    def validate_custom_model(self, model_key: str) -> Tuple[bool, str]:
        """
        验证自定义模型是否可用

        Args:
            model_key: 模型键

        Returns:
            Tuple[bool, str]: (是否可用, 消息)
        """
        try:
            if model_key not in self.custom_models:
                return False, f"模型 '{model_key}' 不存在"

            model_config = self.custom_models[model_key]

            # 检查本地路径
            if model_config.get("is_local", False):
                local_path = model_config.get("local_path") or model_config.get("model_path")
                if local_path and not os.path.exists(local_path):
                    return False, f"模型文件不存在: {local_path}"

                # 如果是safetensors文件，进行额外验证
                if local_path and local_path.endswith('.safetensors'):
                    is_valid, msg = self._validate_safetensors_file(local_path)
                    if not is_valid:
                        return False, f"safetensors文件验证失败: {msg}"

            # 检查HuggingFace模型ID格式
            model_path = model_config.get("model_path", "")
            if "/" in model_path and not os.path.exists(model_path):
                # 简单的HuggingFace ID格式检查
                if not model_path.count("/") >= 1:
                    return False, f"无效的HuggingFace模型ID: {model_path}"

            return True, "模型验证通过"

        except Exception as e:
            logger.error(f"验证自定义模型失败: {str(e)}")
            return False, f"验证失败: {str(e)}"
    
    def import_model_from_directory(self, source_dir: str, model_key: str, model_name: str) -> Tuple[bool, str]:
        """
        从目录导入模型
        
        Args:
            source_dir: 源目录路径
            model_key: 模型键
            model_name: 模型名称
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            source_path = Path(source_dir)
            if not source_path.exists() or not source_path.is_dir():
                return False, f"源目录不存在: {source_dir}"
            
            # 检测模型类型
            model_type = "diffusers"
            if any(f.suffix == ".safetensors" for f in source_path.glob("*.safetensors")):
                model_type = "safetensors"
            elif any(f.suffix in [".ckpt", ".pt", ".pth"] for f in source_path.glob("*")):
                model_type = "checkpoint"
            
            # 添加模型
            return self.add_custom_model(
                model_key=model_key,
                model_name=model_name,
                model_path=str(source_path),
                description=f"从 {source_dir} 导入的模型",
                model_type=model_type
            )
            
        except Exception as e:
            logger.error(f"从目录导入模型失败: {str(e)}")
            return False, f"导入失败: {str(e)}"

    def _validate_safetensors_file(self, file_path: str) -> Tuple[bool, str]:
        """
        验证safetensors文件是否有效

        Args:
            file_path: safetensors文件路径

        Returns:
            Tuple[bool, str]: (是否有效, 消息)
        """
        if not SAFETENSORS_AVAILABLE:
            return False, "safetensors库未安装"

        try:
            # 支持相对路径和绝对路径
            file_path_obj = Path(file_path)
            
            # 如果是相对路径，尝试相对于项目根目录解析
            if not file_path_obj.is_absolute():
                # 获取项目根目录（从当前文件位置推算）
                current_file = Path(__file__)
                project_root = current_file.parent.parent.parent.parent.parent
                file_path_obj = project_root / file_path_obj
            
            file_path_obj = file_path_obj.resolve()
            
            if not file_path_obj.exists():
                return False, f"文件不存在: {file_path_obj}"

            if not file_path_obj.suffix.lower() == '.safetensors':
                return False, f"不是safetensors文件: {file_path_obj}"

            # 降低文件大小检查阈值，支持更小的模型文件
            file_size = file_path_obj.stat().st_size
            min_size = 100 * 1024  # 100KB，更宽松的阈值
            if file_size < min_size:
                return False, f"文件大小异常: {file_size} bytes (最小要求: {min_size} bytes)"

            # 尝试打开safetensors文件验证格式
            if safe_open is None:
                return False, "safetensors库未正确导入"
                
            with safe_open(str(file_path_obj), framework="pt", device="cpu") as f:
                # 检查是否包含基本的模型权重键
                keys = list(f.keys())
                if not keys:
                    return False, "safetensors文件为空"

                # 改进SD模型检测逻辑，支持更多格式
                sd_indicators = [
                    # 原始SD格式
                    "model.diffusion_model",  # UNet
                    "first_stage_model",      # VAE
                    "cond_stage_model",       # Text Encoder
                    # Diffusers格式
                    "unet",
                    "vae", 
                    "text_encoder",
                    # 其他常见格式
                    "model_ema",
                    "state_dict",
                    # 部分模型权重
                    "down_blocks",
                    "up_blocks",
                    "mid_block"
                ]

                # 检查是否包含任何SD相关组件
                found_indicators = []
                for key in keys:
                    key_lower = key.lower()
                    for indicator in sd_indicators:
                        if indicator in key_lower:
                            found_indicators.append(indicator)
                            break

                has_sd_components = len(found_indicators) > 0

                if not has_sd_components:
                    # 检查是否是其他类型的神经网络权重
                    nn_indicators = ["weight", "bias", "running_mean", "running_var", "num_batches_tracked"]
                    has_nn_weights = any(
                        any(indicator in key.lower() for indicator in nn_indicators)
                        for key in keys[:20]  # 只检查前20个键
                    )
                    
                    if has_nn_weights:
                        logger.warning(f"safetensors文件可能不是标准SD模型: {file_path_obj}")
                        return True, f"有效的神经网络权重文件，包含 {len(keys)} 个权重（可能不是标准SD模型）"
                    else:
                        logger.warning(f"safetensors文件格式未知: {file_path_obj}")
                        return True, f"有效的safetensors文件，包含 {len(keys)} 个权重（未检测到已知组件，但可以尝试使用）"

                logger.info(f"safetensors文件验证通过: {file_path_obj}, 包含 {len(keys)} 个权重键，检测到组件: {', '.join(found_indicators[:3])}")
                return True, f"有效的SD模型文件，包含 {len(keys)} 个权重键，检测到组件: {', '.join(found_indicators[:3])}"

        except Exception as e:
            logger.error(f"验证safetensors文件失败: {str(e)}")
            return False, f"验证失败: {str(e)}"

    def add_safetensors_model(
        self,
        model_key: str,
        model_name: str,
        safetensors_path: str,
        description: str = "",
        overwrite: bool = False, # 新增参数
        **kwargs
    ) -> Tuple[bool, str]:
        """
        添加safetensors格式的模型

        Args:
            model_key: 模型唯一标识符
            model_name: 模型显示名称
            safetensors_path: safetensors文件路径
            description: 模型描述
            overwrite: 如果模型键已存在，是否覆盖
            **kwargs: 其他模型参数

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 验证safetensors文件
            is_valid, validation_msg = self._validate_safetensors_file(safetensors_path)
            if not is_valid:
                return False, f"safetensors文件验证失败: {validation_msg}"

            # 使用通用的添加方法，并传递overwrite参数
            return self.add_custom_model(
                model_key=model_key,
                model_name=model_name,
                model_path=safetensors_path,
                description=description or f"safetensors模型: {model_name}",
                model_type="safetensors",
                overwrite=overwrite, # 传递overwrite参数
                **kwargs
            )

        except Exception as e:
            logger.error(f"添加safetensors模型失败: {str(e)}")
            return False, f"添加失败: {str(e)}"
    
    def get_model_statistics(self) -> Dict[str, Any]:
        """获取自定义模型统计信息"""
        try:
            total_models = len(self.custom_models)
            enabled_models = len(self.get_enabled_custom_models())
            
            # 按类型统计
            type_stats = {}
            for config in self.custom_models.values():
                model_type = config.get("model_type", "unknown")
                type_stats[model_type] = type_stats.get(model_type, 0) + 1
            
            # 计算总大小
            total_size = 0
            for config in self.custom_models.values():
                if "local_path" in config:
                    local_path = Path(config["local_path"])
                    if local_path.exists():
                        if local_path.is_file():
                            total_size += local_path.stat().st_size
                        elif local_path.is_dir():
                            total_size += sum(f.stat().st_size for f in local_path.rglob("*") if f.is_file())
            
            return {
                "total_models": total_models,
                "enabled_models": enabled_models,
                "disabled_models": total_models - enabled_models,
                "type_statistics": type_stats,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "models_directory": str(self.models_dir)
            }
            
        except Exception as e:
            logger.error(f"获取模型统计信息失败: {str(e)}")
            return {
                "total_models": 0,
                "enabled_models": 0,
                "disabled_models": 0,
                "type_statistics": {},
                "total_size_bytes": 0,
                "total_size_mb": 0,
                "models_directory": str(self.models_dir)
            }
