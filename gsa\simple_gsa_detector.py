"""
简化的GSA检测器
直接调用官方grounding_dino_demo.py的功能，保持原始项目结构不变
"""

import os
import sys
import cv2
import requests
import numpy as np
import torch
from typing import List, Dict, Any, Optional
from gsa_config import *

def download_model_with_proxy():
    """使用代理下载模型"""
    if os.path.exists(CHECKPOINT_PATH):
        print(f"模型已存在: {CHECKPOINT_PATH}")
        return True
    
    print(f"正在下载模型: {CHECKPOINT_URL}")
    try:
        response = requests.get(CHECKPOINT_URL, proxies=PROXIES, stream=True)
        response.raise_for_status()
        
        with open(CHECKPOINT_PATH, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"模型下载完成: {CHECKPOINT_PATH}")
        return True
    except Exception as e:
        print(f"模型下载失败: {e}")
        return False

def load_gsa_model():
    """加载GSA模型"""
    try:
        # 设置代理环境变量
        import os
        os.environ['HTTP_PROXY'] = PROXIES['http']
        os.environ['HTTPS_PROXY'] = PROXIES['https']

        # 添加GroundingDINO到Python路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        grounding_dino_path = os.path.join(current_dir, "GroundingDINO")
        if grounding_dino_path not in sys.path:
            sys.path.insert(0, grounding_dino_path)

        from groundingdino.util.inference import load_model

        # 确保模型文件存在
        if not os.path.exists(CHECKPOINT_PATH):
            if not download_model_with_proxy():
                raise RuntimeError("模型下载失败")

        print("正在加载GSA模型...")
        model = load_model(CONFIG_PATH, CHECKPOINT_PATH, device=DEVICE)

        # 智能FP16处理 - 检查GPU内存和兼容性
        if FP16_INFERENCE and DEVICE == "cuda":
            try:
                import torch
                # 检查GPU是否支持FP16
                if torch.cuda.is_available() and torch.cuda.get_device_capability()[0] >= 7:
                    model = model.half()
                    print("✅ 启用FP16推理模式")
                else:
                    print("⚠️ GPU不支持FP16，使用FP32模式")
            except Exception as e:
                print(f"⚠️ FP16设置失败，使用FP32模式: {e}")

        print("GSA模型加载成功")
        return model
    except Exception as e:
        print(f"GSA模型加载失败: {e}")
        print("提示：如果是网络连接问题，请检查代理设置")
        return None

def detect_objects(
    image_path: str,
    prompt: str = DEFAULT_PROMPT,
    confidence_threshold: float = CONFIDENCE_THRESHOLD,
    text_threshold: float = TEXT_THRESHOLD,
    box_threshold: float = BOX_THRESHOLD,
    output_dir: str = OUTPUT_DIR,
    save_annotated: bool = None,
    save_original: bool = None
) -> List[Dict[str, Any]]:
    """
    使用GSA检测图像中的目标
    
    Args:
        image_path: 输入图像路径
        prompt: 检测提示词
        confidence_threshold: 置信度阈值
        text_threshold: 文本阈值
        box_threshold: 边界框阈值
        output_dir: 输出目录
        save_annotated: 是否保存标注图片，None时使用全局配置
        save_original: 是否保存原始图片，None时使用全局配置
    
    Returns:
        检测结果列表
    """
    try:
        # 添加GroundingDINO到Python路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        grounding_dino_path = os.path.join(current_dir, "GroundingDINO")
        if grounding_dino_path not in sys.path:
            sys.path.insert(0, grounding_dino_path)

        from groundingdino.util.inference import load_image, predict, annotate

        # 加载模型
        model = load_gsa_model()
        if model is None:
            return []
        
        # 加载图像
        image_source, image = load_image(image_path)

        # 智能数据类型处理 - 确保模型和图像类型匹配
        try:
            model_dtype = next(model.parameters()).dtype
            device_type = next(model.parameters()).device

            # 确保图像在正确的设备上
            if image.device != device_type:
                image = image.to(device_type)

            # 处理数据类型匹配
            if model_dtype == torch.float16:  # 模型是FP16
                if image.dtype != torch.float16:
                    image = image.half()
                    print("🔄 转换图像为FP16格式")
            else:  # 模型是FP32
                if image.dtype != torch.float32:
                    image = image.float()
                    print("🔄 转换图像为FP32格式")

        except Exception as e:
            print(f"⚠️ 数据类型处理警告: {e}")
            # 回退到安全的FP32模式
            image = image.float()

        # 执行检测
        boxes, logits, phrases = predict(
            model=model,
            image=image,
            caption=prompt,
            box_threshold=box_threshold,
            text_threshold=text_threshold,
            device=DEVICE,
        )
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 确定保存设置（优先使用参数，否则使用全局配置）
        should_save_annotated = save_annotated if save_annotated is not None else SAVE_ANNOTATED
        should_save_original = save_original if save_original is not None else SAVE_ORIGINAL
        
        # 保存标注图像
        if should_save_annotated:
            try:
                annotated_frame = annotate(
                    image_source=image_source,
                    boxes=boxes,
                    logits=logits,
                    phrases=phrases
                )

                output_path = os.path.join(output_dir, f"annotated_{os.path.basename(image_path)}")

                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                # 保存标注图像
                success = cv2.imwrite(output_path, annotated_frame)
                if success:
                    print(f"✅ 标注图像已保存: {output_path}")
                    # 验证文件是否真的存在
                    if os.path.exists(output_path):
                        file_size = os.path.getsize(output_path)
                        print(f"   文件大小: {file_size} 字节")
                    else:
                        print(f"❌ 警告：文件保存后不存在: {output_path}")
                else:
                    print(f"❌ 保存标注图像失败: {output_path}")
            except Exception as e:
                print(f"❌ 保存标注图像时出错: {e}")
                print(f"   输出路径: {output_path}")
                print(f"   输出目录: {output_dir}")
                print(f"   图像形状: {annotated_frame.shape if 'annotated_frame' in locals() else 'N/A'}")
        
        # 保存原始图像副本
        if should_save_original:
            try:
                original_path = os.path.join(output_dir, f"original_{os.path.basename(image_path)}")

                # 确保输出目录存在
                os.makedirs(os.path.dirname(original_path), exist_ok=True)

                # 保存原始图像
                success = cv2.imwrite(original_path, image_source)
                if success:
                    print(f"✅ 原始图像已保存: {original_path}")
                    # 验证文件是否真的存在
                    if os.path.exists(original_path):
                        file_size = os.path.getsize(original_path)
                        print(f"   文件大小: {file_size} 字节")
                    else:
                        print(f"❌ 警告：文件保存后不存在: {original_path}")
                else:
                    print(f"❌ 保存原始图像失败: {original_path}")
            except Exception as e:
                print(f"❌ 保存原始图像时出错: {e}")
                print(f"   输出路径: {original_path}")
                print(f"   输出目录: {output_dir}")
        
        # 转换检测结果为标准格式
        detections = []
        if len(boxes) > 0:
            # 获取图像尺寸
            image_height, image_width = image_source.shape[:2]
            
            for i, (box, logit, phrase) in enumerate(zip(boxes, logits, phrases)):
                # 获取归一化的cxcywh坐标
                cx, cy, w, h = box.tolist()
                
                # 转换为像素坐标
                cx_pixel = cx * image_width
                cy_pixel = cy * image_height
                w_pixel = w * image_width
                h_pixel = h * image_height
                
                # 转换为xyxy格式
                x1 = cx_pixel - w_pixel / 2
                y1 = cy_pixel - h_pixel / 2
                x2 = cx_pixel + w_pixel / 2
                y2 = cy_pixel + h_pixel / 2
                
                # 确保坐标在图像范围内
                x1 = max(0, min(x1, image_width))
                y1 = max(0, min(y1, image_height))
                x2 = max(0, min(x2, image_width))
                y2 = max(0, min(y2, image_height))
                
                # 获取中文标签
                chinese_label = LABEL_MAPPING.get(phrase.lower(), phrase)
                
                detection = {
                    "id": i,
                    "bbox": [x1, y1, x2, y2],
                    "confidence": float(logit),
                    "class": phrase,
                    "chinese_label": chinese_label,
                    "area": (x2 - x1) * (y2 - y1)
                }
                detections.append(detection)
        
        print(f"检测完成，发现 {len(detections)} 个目标")
        return detections
        
    except Exception as e:
        print(f"检测失败: {e}")
        return []

def main():
    """主函数 - 命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="简化的GSA目标检测")
    parser.add_argument("--image", required=True, help="输入图像路径")
    parser.add_argument("--prompt", default=DEFAULT_PROMPT, help="检测提示词")
    parser.add_argument("--confidence", type=float, default=CONFIDENCE_THRESHOLD, help="置信度阈值")
    parser.add_argument("--text_threshold", type=float, default=TEXT_THRESHOLD, help="文本阈值")
    parser.add_argument("--box_threshold", type=float, default=BOX_THRESHOLD, help="边界框阈值")
    parser.add_argument("--output", default=OUTPUT_DIR, help="输出目录")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image):
        print(f"图像文件不存在: {args.image}")
        return
    
    print(f"开始检测图像: {args.image}")
    print(f"使用提示词: {args.prompt}")
    print(f"置信度阈值: {args.confidence}")
    
    detections = detect_objects(
        image_path=args.image,
        prompt=args.prompt,
        confidence_threshold=args.confidence,
        text_threshold=args.text_threshold,
        box_threshold=args.box_threshold,
        output_dir=args.output
    )
    
    print(f"\n检测结果:")
    for detection in detections:
        print(f"- {detection['chinese_label']}: 置信度 {detection['confidence']:.3f}")

if __name__ == "__main__":
    main()
