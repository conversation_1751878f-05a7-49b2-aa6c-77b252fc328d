"""
AI生成服务
整合SD生成器和提示词构建器，提供统一的AI生成接口
"""

import asyncio
import logging
import os
import sys
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import uuid
from datetime import datetime
from PIL import Image
import json
import io

# 尝试导入GSA检测器，优先使用安全版本
try:
    # 添加GSA目录到Python路径
    # ai_service.py位于: backend/app/services/ai_generation/ai_service.py
    # 需要向上5级到达项目根目录: ai_generation -> services -> app -> backend -> 项目根
    gsa_path = Path(__file__).parent.parent.parent.parent.parent / "gsa"
    if gsa_path.exists() and str(gsa_path) not in sys.path:
        sys.path.insert(0, str(gsa_path))

    # 优先尝试安全版本的GSA检测器
    try:
        from safe_gsa_detector import safe_detect_objects as detect_objects
        GSA_AVAILABLE = True
        GSA_VERSION = "safe"
        logger = logging.getLogger(__name__)
        logger.info("安全GSA检测器导入成功")
    except ImportError:
        # 回退到原版本
        from simple_gsa_detector import detect_objects
        GSA_AVAILABLE = True
        GSA_VERSION = "standard"
        logger = logging.getLogger(__name__)
        logger.info("标准GSA检测器导入成功")

except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.warning(f"GSA检测器导入失败: {e}，将使用估算标注")
    GSA_AVAILABLE = False
    GSA_VERSION = "none"

    # 提供一个回退函数
    def detect_objects(*args, **kwargs):
        """GSA不可用时的回退函数"""
        return []

from .sd_generator import SDGenerator
from .fixed_prompt_service import FixedPromptService
from .model_manager import ModelManager
from .auto_dataset_manager import AutoDatasetManager
from ..common.annotation_generator import AnnotationGenerator
from ..common.file_manager import FileManager
from ...core.config import get_settings

logger = logging.getLogger(__name__)

class AIGenerationService:
    """AI生成服务"""
    
    def __init__(self, model_path: Optional[str] = None, model_manager: Optional[ModelManager] = None):
        """
        初始化AI生成服务
        
        Args:
            model_path: SD模型路径，如果为None则使用配置文件中的路径
            model_manager: 共享的模型管理器实例
        """
        settings = get_settings()
        
        # 使用配置文件中的模型路径
        if model_path is None:
            model_path = settings.get_ai_model_path()
        
        # 如果配置为使用本地模型，优先检查本地路径
        if settings.ai_use_local_model:
            # 尝试多个可能的路径
            possible_paths = [
                Path(settings.ai_local_models_dir) / "stable-diffusion-v1-5",
                Path.cwd() / settings.ai_local_models_dir / "stable-diffusion-v1-5",
                Path.cwd().parent / settings.ai_local_models_dir / "stable-diffusion-v1-5",  # 从backend目录向上一级
                Path(__file__).parent.parent.parent.parent / settings.ai_local_models_dir / "stable-diffusion-v1-5"
            ]
            
            for local_path in possible_paths:
                if local_path.exists() and local_path.is_dir():
                    model_path = str(local_path.resolve())
                    logger.info(f"使用本地模型路径: {model_path}")
                    break
            else:
                logger.warning(f"未找到本地模型，尝试的路径: {[str(p) for p in possible_paths]}")
        
        device = settings.get_ai_device()
        
        self.sd_generator = SDGenerator(model_path, device)
        # 使用新的固定提示词服务（启用随机对象功能）
        self.fixed_prompt_service = FixedPromptService(
            enable_random_objects=True,
            random_object_seed=None  # 使用系统时间作为默认种子
        )
        
        if model_manager:
            self.model_manager = model_manager
            logger.info("使用共享的ModelManager实例")
        else:
            logger.warning("未提供共享的ModelManager，创建一个新的实例")
            self.model_manager = ModelManager(device, proxy_port=7890)

        # 旧的构建器已移至legacy目录，现在主要使用固定提示词服务
        # 保留引用以防需要回退（但实际不再使用）
        self.prompt_builder = None
        self.advanced_prompt_builder = None
        self.annotation_generator = AnnotationGenerator()
        self.file_manager = FileManager()

        # 初始化自动数据集管理器
        self.auto_dataset_manager = AutoDatasetManager()

        self.is_model_loaded = False
        self.generation_history = []
        
        # GSA模型缓存
        self.gsa_model = None

        # 在初始化后同步模型状态
        self._sync_model_managers()
        
        logger.info(f"AI生成服务初始化完成，模型路径: {model_path}, 设备: {device}")
    
    def _sync_model_managers(self):
        """同步新旧模型管理器的状态"""
        try:
            # 如果SD生成器已经加载了模型，将其同步到模型管理器
            if hasattr(self.sd_generator, 'pipe') and self.sd_generator.pipe is not None:
                logger.info("检测到SD生成器已加载模型，同步到模型管理器")
                
                # 假设加载的是stable-diffusion-v1-5模型
                model_key = "stable-diffusion-v1-5"
                self.model_manager.current_model = self.sd_generator.pipe
                self.model_manager.current_model_id = model_key
                self.model_manager.loaded_models[model_key] = self.sd_generator.pipe
                
                logger.info(f"模型同步完成: {model_key}")
        except Exception as e:
            logger.warning(f"模型同步失败: {str(e)}")
    
    async def initialize(self) -> bool:
        """
        异步初始化服务
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("开始初始化AI生成服务...")
            
            # 在线程池中加载模型以避免阻塞
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(None, self.sd_generator.load_model)
            
            if success:
                self.is_model_loaded = True
                # 模型加载成功后同步状态
                self._sync_model_managers()
                logger.info("AI生成服务初始化成功")
                return True
            else:
                logger.error("模型加载失败")
                return False
                
        except Exception as e:
            logger.error(f"AI生成服务初始化失败: {str(e)}")
            return False
    
    def build_prompt_from_selection(
        self,
        military_target: str,
        weather: str,
        scene: str,
        custom_prompt: str = "",
        style_strength: float = 0.7,
        technical_detail: float = 0.8,
        target_size_ratio: float = 0.1,  # 目标面积占比参数（保留兼容性，但不再使用）
        enable_random_objects: Optional[bool] = None,
        max_random_objects: Optional[int] = None,
        random_object_seed: Optional[int] = None
    ) -> Tuple[str, str]:
        """
        根据用户选择构建提示词（使用固定提示词矩阵，支持随机对象）

        Args:
            military_target: 军事目标
            weather: 天气条件
            scene: 场景环境
            custom_prompt: 自定义提示词
            style_strength: 风格强度（保留兼容性，但不再使用）
            technical_detail: 技术细节程度（保留兼容性，但不再使用）
            target_size_ratio: 目标面积占比（保留兼容性，但不再使用）
            enable_random_objects: 是否启用随机对象
            max_random_objects: 最大随机对象数量
            random_object_seed: 随机对象种子

        Returns:
            Tuple[str, str]: (正面提示词, 负面提示词)
        """
        try:
            # 使用新的固定提示词服务（支持随机对象）
            return self.fixed_prompt_service.get_prompt(
                military_target=military_target,
                weather=weather,
                scene=scene,
                custom_prompt=custom_prompt,
                enable_random_objects=enable_random_objects,
                max_random_objects=max_random_objects,
                random_object_seed=random_object_seed
            )
        except Exception as e:
            logger.error(f"固定提示词服务失败: {str(e)}")
            # 返回基本的错误提示词
            if custom_prompt.strip():
                return custom_prompt, "low quality, blurry"
            else:
                return f"{military_target}, {weather}, {scene}", "low quality, blurry"
    
    async def generate_images(
        self,
        military_target: str,
        weather: str,
        scene: str,
        num_images: int = 1,
        steps: int = 30,
        cfg_scale: float = 7.5,
        seed: int = -1,
        width: int = 512,
        height: int = 512,
        scheduler_name: str = "DPM++ 2M Karras",
        custom_prompt: str = "",
        style_strength: float = 0.7,
        technical_detail: float = 0.8,
        target_size_ratio: float = 0.10,
        save_images: bool = True,
        generate_annotations: bool = True,
        enable_gsa_detection: bool = False,
        gsa_confidence: float = 0.25,
        gsa_nms_threshold: float = 0.4,
        gsa_save_original: bool = True,
        gsa_save_annotated: bool = True,
        # 新增随机对象相关参数
        enable_random_objects: Optional[bool] = None,
        max_random_objects: Optional[int] = None,
        random_object_seed: Optional[int] = None,
        # 新增优化参数
        gsa_enable_multi_scale: bool = True,
        gsa_enable_enhancement: bool = True,
        gsa_preset: str = "enhanced_detection",
        # 模型信息参数（可选，用于日志记录）
        model_info: Optional[Dict[str, Any]] = None,
        **kwargs  # 接受其他可能的参数
    ) -> Dict[str, Any]:
        """
        生成图像
        
        Args:
            military_target: 军事目标
            weather: 天气条件
            scene: 场景环境
            num_images: 生成图像数量
            steps: 采样步数
            cfg_scale: CFG引导强度
            seed: 随机种子
            width: 图像宽度
            height: 图像高度
            scheduler_name: 采样器名称
            custom_prompt: 自定义提示词
            style_strength: 风格强度
            technical_detail: 技术细节程度
            target_size_ratio: 目标尺寸比例
            save_images: 是否保存图像
            generate_annotations: 是否生成标注
            enable_gsa_detection: 是否启用GSA检测
            gsa_confidence: GSA检测置信度
            gsa_nms_threshold: GSA NMS阈值
            gsa_save_original: 是否保存GSA原始图
            gsa_save_annotated: 是否保存GSA标注图
            enable_random_objects: 是否启用随机对象
            max_random_objects: 最大随机对象数量
            random_object_seed: 随机对象种子
            gsa_enable_multi_scale: 是否启用多尺度检测
            gsa_enable_enhancement: 是否启用图像增强
            gsa_preset: GSA预设配置
            model_info: 模型信息（用于日志记录）

        Returns:
            Dict[str, Any]: 生成结果
        """
        if not self.is_model_loaded:
            raise RuntimeError("模型未加载，请先初始化服务")

        # 详细记录生成参数
        logger.info(f"开始生成图像 - 目标:{military_target}, 天气:{weather}, 场景:{scene}, 种子:{seed}, 数量:{num_images}")

        try:
            # 1. 构建提示词（使用固定提示词矩阵）
            # 计算目标面积比例（保留用于标注生成）
            if target_size_ratio is None or target_size_ratio <= 0:
                # 默认目标面积占比，优化为约1/10图片大小（10%面积）
                default_ratios = {
                    "坦克": 0.10,    # 10%面积
                    "战机": 0.10,    # 10%面积
                    "舰艇": 0.10     # 10%面积
                }
                target_area_ratio = default_ratios.get(military_target, 0.10)
            else:
                target_area_ratio = target_size_ratio

            # 使用固定提示词服务构建提示词（包含随机对象）
            positive_prompt, negative_prompt = self.build_prompt_from_selection(
                military_target=military_target,
                weather=weather,
                scene=scene,
                custom_prompt=custom_prompt,
                style_strength=style_strength,
                technical_detail=technical_detail,
                target_size_ratio=target_area_ratio,
                enable_random_objects=enable_random_objects,
                max_random_objects=max_random_objects,
                random_object_seed=random_object_seed
            )
            
            logger.info(f"构建的提示词: {positive_prompt[:100]}...")
            
            # 2. 在线程池中生成图像
            loop = asyncio.get_event_loop()
            generated_images = await loop.run_in_executor(
                None,
                self.sd_generator.generate,
                positive_prompt,
                negative_prompt,
                num_images,
                steps,
                cfg_scale,
                seed,
                width,
                height,
                scheduler_name
            )
            
            generation_id = str(uuid.uuid4())
            timestamp = datetime.now()
            
            result = {
                "generation_id": generation_id,
                "timestamp": timestamp.isoformat(),
                "device_used": self.sd_generator.device if self.sd_generator else "unknown",
                "parameters": {
                    "military_target": military_target,
                    "weather": weather,
                    "scene": scene,
                    "num_images": num_images,
                    "steps": steps,
                    "cfg_scale": cfg_scale,
                    "seed": seed,
                    "width": width,
                    "height": height,
                    "scheduler_name": scheduler_name,
                    "custom_prompt": custom_prompt,
                    "style_strength": style_strength,
                    "technical_detail": technical_detail
                },
                "prompts": {
                    "positive": positive_prompt,
                    "negative": negative_prompt
                },
                "images": [],
                "annotations": []
            }
            
            processed_images_info = []
            if save_images or generate_annotations:
                # 构建完整的生成参数
                full_generation_params = {
                    "military_target": military_target,
                    "weather": weather,
                    "scene": scene,
                    "num_images": num_images,
                    "steps": steps,
                    "cfg_scale": cfg_scale,
                    "seed": seed,
                    "width": width,
                    "height": height,
                    "scheduler_name": scheduler_name,
                    "custom_prompt": custom_prompt,
                    "style_strength": style_strength,
                    "technical_detail": technical_detail,
                    "target_size_ratio": target_size_ratio,
                    "prompt": result["parameters"].get("prompt", ""),
                    "negative_prompt": result["parameters"].get("negative_prompt", ""),
                    "model": "stable-diffusion-v1-5"  # 可以从模型管理器获取
                }

                for i, image in enumerate(generated_images):
                    image_info = await self._process_generated_image(
                        image=image,
                        generation_id=generation_id,
                        image_index=i,
                        military_target=military_target,
                        weather=weather,
                        scene=scene,
                        target_size_ratio=target_area_ratio,
                        save_image=save_images,
                        generate_annotation=generate_annotations,
                        enable_gsa_detection=enable_gsa_detection,
                        gsa_confidence=gsa_confidence,
                        gsa_nms_threshold=gsa_nms_threshold,
                        gsa_save_annotated=gsa_save_annotated,
                        generation_params=full_generation_params
                    )
                    processed_images_info.append(image_info)
            
            if save_images:
                result["images"] = processed_images_info
            else:
                # 只返回图像基本信息，不保存文件
                result["images"] = [
                    {
                        "index": i,
                        "width": img.width,
                        "height": img.height,
                        "format": img.format or "PNG"
                    }
                    for i, img in enumerate(generated_images)
                ]
            
            # 5. 记录生成历史
            self.generation_history.append({
                "generation_id": generation_id,
                "timestamp": timestamp,
                "parameters": result["parameters"],
                "num_images": len(processed_images_info),
                "success": True
            })
            
            logger.info(f"成功生成 {len(processed_images_info)} 张图像，ID: {generation_id}")
            return result
            
        except Exception as e:
            logger.error(f"图像生成失败: {str(e)}")
            
            # 记录失败历史
            self.generation_history.append({
                "generation_id": str(uuid.uuid4()),
                "timestamp": datetime.now(),
                "parameters": locals(),
                "num_images": 0,
                "success": False,
                "error": str(e)
            })
            
            raise
    
    async def _process_generated_image(
        self,
        image: Image.Image,
        generation_id: str,
        image_index: int,
        military_target: str,
        weather: str,
        scene: str,
        target_size_ratio: float,
        save_image: bool,
        generate_annotation: bool,
        enable_gsa_detection: bool,
        gsa_confidence: float,
        gsa_nms_threshold: float,
        gsa_save_annotated: bool = True,
        generation_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        处理单张生成的图像（保存、标注等）

        Args:
            image: PIL Image对象
            generation_id: 本次生成的ID
            image_index: 图像在本批次中的索引
            military_target: 军事目标
            weather: 天气
            scene: 场景
            target_size_ratio: 目标尺寸比例 (用于估算)
            save_image: 是否保存图像
            generate_annotation: 是否生成标注
            enable_gsa_detection: 是否启用GSA检测
            gsa_confidence: GSA置信度阈值
            gsa_nms_threshold: GSA NMS阈值
        """
        image_info = {
            "index": image_index,
            "width": image.width,
            "height": image.height,
            "format": image.format or "PNG"
        }

        temp_image_path = None
        try:
            if generate_annotation and enable_gsa_detection:
                temp_dir = self.file_manager.get_temp_dir()
                temp_image_path = temp_dir / f"{generation_id}_{image_index:03d}_temp.png"
                image.save(temp_image_path)

            image_path_for_annotation = None
            if save_image:
                image_bytes = io.BytesIO()
                image.save(image_bytes, format='PNG')
                image_data = image_bytes.getvalue()

                image_path, file_info = self.file_manager.save_generated_image(
                    image_data=image_data,
                    filename=f"{generation_id}_{image_index:03d}.png",
                    generation_type="ai",
                    metadata={"military_target": military_target, "weather": weather, "scene": scene}
                )
                image_info["file_path"] = str(image_path)
                image_info["filename"] = os.path.basename(image_path)
                image_path_for_annotation = image_path

            if generate_annotation:
                annotation = None
                if enable_gsa_detection and temp_image_path and GSA_AVAILABLE:
                    try:
                        logger.info(f"执行GSA检测: {temp_image_path}, 提示词: {military_target}, 版本: {GSA_VERSION}")

                        # 设置GSA输出目录 - 使用与生成图片相同的目录
                        gsa_output_dir = None
                        if gsa_save_annotated and image_path_for_annotation:
                            gsa_output_dir = str(Path(image_path_for_annotation).parent)
                        else:
                            # 如果没有保存原图，使用临时目录
                            gsa_output_dir = str(self.file_manager.get_temp_dir())

                        # 根据GSA版本调用不同的参数
                        if GSA_VERSION == "safe":
                            detections = detect_objects(
                                image_path=str(temp_image_path),
                                prompt=military_target,
                                box_threshold=gsa_confidence,
                                text_threshold=gsa_nms_threshold,
                                output_dir=gsa_output_dir,
                                save_annotated=gsa_save_annotated,
                                save_original=True
                            )
                        else:
                            detections = detect_objects(
                                image_path=str(temp_image_path),
                                prompt=military_target,
                                box_threshold=gsa_confidence,
                                # text_threshold=gsa_nms_threshold # 假设 text_threshold 对应 nms
                                output_dir=gsa_output_dir,
                                save_annotated=gsa_save_annotated,
                                save_original=True
                            )

                        logger.info(f"GSA检测调用完成，输出目录: {gsa_output_dir}")

                        if detections:
                            logger.info(f"✅ 检测完成，找到 {len(detections)} 个目标")
                            logger.debug(f"检测结果样本: {detections[0] if detections else 'None'}")
                            annotation = self._convert_gsa_to_coco(detections, image_index, image.width, image.height, military_target, weather, scene)
                            image_info["annotation_source"] = f"gsa_{GSA_VERSION}"
                            logger.info(f"GSA标注转换成功")
                            
                            # 记录标注图片保存位置
                            if gsa_save_annotated:
                                # GSA检测器保存的文件名格式是 annotated_{原始文件名}
                                temp_filename = f"{generation_id}_{image_index:03d}_temp.png"
                                annotated_filename = f"annotated_{temp_filename}"
                                annotated_path = Path(gsa_output_dir) / annotated_filename

                                # 检查标注图片是否存在，如果不存在则检查其他可能的文件名
                                if annotated_path.exists():
                                    logger.info(f"📸 标注图片已保存: {annotated_path}")
                                    image_info["annotated_image_path"] = str(annotated_path)
                                else:
                                    # 尝试查找其他可能的标注文件
                                    output_dir_path = Path(gsa_output_dir)
                                    if output_dir_path.exists():
                                        # 查找所有以 annotated_ 开头的文件
                                        annotated_files = list(output_dir_path.glob("annotated_*.png"))
                                        if annotated_files:
                                            # 使用最新的标注文件
                                            latest_annotated = max(annotated_files, key=lambda x: x.stat().st_mtime)
                                            logger.info(f"📸 找到标注图片: {latest_annotated}")
                                            image_info["annotated_image_path"] = str(latest_annotated)
                                        else:
                                            logger.warning(f"标注图片未找到: {annotated_path}")
                                            logger.info(f"输出目录内容: {list(output_dir_path.iterdir())}")
                                    else:
                                        logger.warning(f"输出目录不存在: {gsa_output_dir}")
                        else:
                            logger.info("GSA检测完成，未找到目标")

                    except Exception as e:
                        logger.error(f"GSA检测失败: {e}，回退到估算标注")
                        # 如果是数据类型错误，记录详细信息
                        if "expected scalar type" in str(e):
                            logger.error("检测到数据类型不匹配错误，建议使用安全GSA检测器")
                            logger.error("解决方案：在gsa_config.py中设置 FP16_INFERENCE = False")
                elif enable_gsa_detection and not GSA_AVAILABLE:
                    logger.warning("GSA检测被启用但GSA模块不可用，使用估算标注")
                
                if annotation is None:
                    annotation = await self._generate_annotation_for_image(image, military_target, weather, scene, target_size_ratio)
                    image_info["annotation_source"] = "estimated"

                image_info["annotation"] = annotation
                if image_path_for_annotation:
                    annotation_path = self.file_manager.save_annotation(annotation, str(image_path_for_annotation))
                    image_info["annotation_path"] = str(annotation_path)

        finally:
            if temp_image_path and os.path.exists(temp_image_path):
                os.remove(temp_image_path)

        # 自动添加到数据集（如果保存了图片）
        if save_image and image_info.get("file_path"):
            try:
                # 使用传入的生成参数，如果没有则构建基本参数
                if generation_params is None:
                    generation_params = {
                        "military_target": military_target,
                        "weather": weather,
                        "scene": scene,
                        "target_size_ratio": target_size_ratio,
                        "generation_id": generation_id,
                        "image_index": image_index
                    }

                # 从file_manager保存的元数据中获取信息
                metadata_path = Path(image_info["file_path"]).with_suffix('.json')
                generation_metadata = {}
                if metadata_path.exists():
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        generation_metadata = json.load(f)

                # 调用自动数据集管理器
                self.auto_dataset_manager.process_generated_image(
                    image_path=image_info["file_path"],
                    generation_metadata=generation_metadata,
                    generation_params=generation_params
                )

                logger.debug(f"图片已自动处理到数据集: {image_info['filename']}")

            except Exception as e:
                logger.warning(f"自动添加图片到数据集失败: {str(e)}")
                # 不影响主要的图片生成流程

        return image_info

    def _convert_gsa_to_coco(self, detections, image_id, width, height, category_name, weather, scene):
        """将GSA检测结果转换为COCO格式."""
        coco_annotations = []
        for i, det in enumerate(detections):
            try:
                # 兼容不同的边界框键名
                bbox = None
                if 'bbox' in det:
                    bbox = det['bbox']
                elif 'box' in det:
                    bbox = det['box']
                else:
                    logger.warning(f"检测结果缺少bbox/box键: {det}")
                    continue
                
                if len(bbox) != 4:
                    logger.warning(f"边界框格式错误: {bbox}")
                    continue
                
                # 确保坐标是数值类型
                coords = [float(coord) for coord in bbox]

                # 处理不同的边界框格式
                if all(0 <= coord <= 1 for coord in coords):
                    # 归一化坐标
                    if GSA_VERSION == "safe":
                        # 安全版本返回的是归一化的cxcywh格式
                        cx, cy, bw, bh = coords
                        logger.info(f"检测到安全版本归一化cxcywh坐标: {coords}")

                        # 转换为像素坐标
                        cx_pixel = cx * width
                        cy_pixel = cy * height
                        bw_pixel = bw * width
                        bh_pixel = bh * height

                        # 转换为xyxy格式
                        x1 = cx_pixel - bw_pixel / 2
                        y1 = cy_pixel - bh_pixel / 2
                        x2 = cx_pixel + bw_pixel / 2
                        y2 = cy_pixel + bh_pixel / 2
                    else:
                        # 标准版本返回的是归一化的xyxy格式
                        x1, y1, x2, y2 = coords
                        logger.info(f"检测到标准版本归一化xyxy坐标: {coords}")
                        x1 = x1 * width
                        y1 = y1 * height
                        x2 = x2 * width
                        y2 = y2 * height
                else:
                    # 像素坐标，直接使用
                    x1, y1, x2, y2 = coords

                # 确保坐标在图像范围内
                x1 = max(0, min(x1, width))
                y1 = max(0, min(y1, height))
                x2 = max(0, min(x2, width))
                y2 = max(0, min(y2, height))

                # 转换为COCO格式 (x, y, width, height)
                coco_bbox = [x1, y1, x2 - x1, y2 - y1]
                
                ann = {
                    "id": i + 1,
                    "image_id": image_id,
                    "category_id": self.annotation_generator.categories.get(category_name, {}).get("id", 0),
                    "bbox": coco_bbox,
                    "area": (x2 - x1) * (y2 - y1),
                    "iscrowd": 0,
                    "attributes": {
                        "gsa_confidence": float(det.get('confidence', 0.0)),
                        "gsa_class": str(det.get('class', det.get('label', 'unknown'))),
                        "gsa_chinese_label": str(det.get('chinese_label', ''))
                    }
                }
                coco_annotations.append(ann)
                
            except Exception as e:
                logger.error(f"处理检测结果时出错: {e}, 检测数据: {det}")
                continue

        return self.annotation_generator.create_coco_annotation(
            image_id=image_id,
            category_name=category_name,
            bbox=None,
            image_width=width,
            image_height=height,
            additional_info={
                "weather": weather,
                "scene": scene,
                "generation_method": "ai_generated_gsa",
            },
            annotations_list=coco_annotations
        )

    async def _generate_annotation_for_image(
        self,
        image: Image.Image,
        military_target: str,
        weather: str,
        scene: str,
        target_size_ratio: float = 0.10
    ) -> Dict[str, Any]:
        """
        为生成的图像创建标注
        
        Args:
            image: 图像对象
            military_target: 军事目标
            weather: 天气条件
            scene: 场景环境
            
        Returns:
            Dict[str, Any]: COCO格式标注
        """
        # 创建基础标注信息
        image_width, image_height = image.size
        
        # 估算目标位置（简化版本，实际应用中可能需要目标检测）
        # 这里假设目标位于图像中心区域
        center_x = image_width // 2
        center_y = image_height // 2
        
        # 根据目标类型和传入的尺寸比例计算边界框大小
        # 不同军事目标的长宽比例
        aspect_ratios = {
            "坦克": (1.0, 1.0),     # 长宽比约1:1
            "战机": (1.6, 1.0),     # 长宽比约1.6:1
            "舰艇": (2.0, 1.0)      # 长宽比约2:1
        }

        aspect_w, aspect_h = aspect_ratios.get(military_target, (1.0, 1.0))

        # 根据目标面积比例和长宽比计算实际的宽高比例
        # 面积 = width_ratio * height_ratio = target_size_ratio
        # width_ratio / height_ratio = aspect_w / aspect_h
        height_ratio = (target_size_ratio / (aspect_w / aspect_h)) ** 0.5
        width_ratio = height_ratio * (aspect_w / aspect_h)
        bbox_width = int(image_width * width_ratio)
        bbox_height = int(image_height * height_ratio)
        
        bbox_x = center_x - bbox_width // 2
        bbox_y = center_y - bbox_height // 2
        
        # 确保边界框在图像范围内
        bbox_x = max(0, min(bbox_x, image_width - bbox_width))
        bbox_y = max(0, min(bbox_y, image_height - bbox_height))
        
        # 创建COCO格式标注
        annotation = self.annotation_generator.create_coco_annotation(
            image_id=1,
            category_name=military_target,
            bbox=[bbox_x, bbox_y, bbox_width, bbox_height],
            image_width=image_width,
            image_height=image_height,
            additional_info={
                "weather": weather,
                "scene": scene,
                "generation_method": "ai_generated",
                "confidence": 0.95  # AI生成的图像假设有高置信度
            }
        )
        
        return annotation
    
    def get_prompt_suggestions(self, military_target: str) -> Dict[str, List[str]]:
        """
        获取提示词建议（使用固定提示词服务）

        Args:
            military_target: 军事目标类型

        Returns:
            Dict[str, List[str]]: 提示词建议
        """
        try:
            return self.fixed_prompt_service.get_prompt_suggestions(military_target)
        except Exception as e:
            logger.warning(f"固定提示词服务获取建议失败: {str(e)}")
            return {"error": ["固定提示词服务不可用"]}

    def set_random_object_seed(self, seed: Optional[int]):
        """设置随机对象生成器的种子"""
        try:
            self.fixed_prompt_service.set_random_object_seed(seed)
            logger.info(f"随机对象种子已设置: {seed}")
        except Exception as e:
            logger.error(f"设置随机对象种子失败: {str(e)}")

    def toggle_random_objects(self, enable: bool):
        """启用或禁用随机对象功能"""
        try:
            self.fixed_prompt_service.toggle_random_objects(enable)
            logger.info(f"随机对象功能已{'启用' if enable else '禁用'}")
        except Exception as e:
            logger.error(f"切换随机对象功能失败: {str(e)}")

    def get_random_object_statistics(self) -> Dict[str, Any]:
        """获取随机对象统计信息"""
        try:
            return self.fixed_prompt_service.get_random_object_statistics()
        except Exception as e:
            logger.error(f"获取随机对象统计信息失败: {str(e)}")
            return {"error": str(e)}

    def get_random_objects_for_context(
        self,
        military_target: str,
        weather: str,
        scene: str,
        include_weights: bool = False
    ) -> List[Dict[str, Any]]:
        """获取特定上下文下的可用随机对象"""
        try:
            return self.fixed_prompt_service.get_random_objects_for_context(
                military_target=military_target,
                weather=weather,
                scene=scene,
                include_weights=include_weights
            )
        except Exception as e:
            logger.error(f"获取上下文随机对象失败: {str(e)}")
            return []

    def preview_prompts_with_random_objects(
        self,
        military_target: str,
        weather: str,
        scene: str,
        num_previews: int = 3
    ) -> List[Dict[str, str]]:
        """生成包含随机对象的提示词预览"""
        try:
            return self.fixed_prompt_service.preview_with_random_objects(
                military_target=military_target,
                weather=weather,
                scene=scene,
                num_previews=num_previews
            )
        except Exception as e:
            logger.error(f"生成随机对象预览失败: {str(e)}")
            return [{"error": str(e)}]

    def optimize_prompt(self, prompt: str) -> str:
        """
        优化提示词（使用固定提示词服务）

        Args:
            prompt: 原始提示词

        Returns:
            str: 优化后的提示词
        """
        try:
            return self.fixed_prompt_service.optimize_prompt(prompt)
        except Exception as e:
            logger.warning(f"固定提示词服务优化失败: {str(e)}")
            return prompt  # 直接返回原始提示词

    def get_supported_options(self) -> Dict[str, List[str]]:
        """
        获取支持的选项（载具、天气、场景）

        Returns:
            Dict[str, List[str]]: 支持的选项
        """
        try:
            return self.fixed_prompt_service.get_supported_options()
        except Exception as e:
            logger.warning(f"获取支持选项失败: {str(e)}")
            # 返回默认选项
            return {
                "military_targets": ["坦克", "战机", "舰艇"],
                "weather_conditions": ["雨天", "雪天", "大雾", "夜间"],
                "scene_environments": ["城市", "岛屿", "乡村"]
            }

    def validate_combination(self, military_target: str, weather: str, scene: str) -> bool:
        """
        验证载具×天气×场景组合是否有效

        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境

        Returns:
            bool: 组合是否有效
        """
        try:
            return self.fixed_prompt_service.validate_combination(military_target, weather, scene)
        except Exception as e:
            logger.warning(f"验证组合失败: {str(e)}")
            return False

    def get_prompt_preview(
        self,
        military_target: str,
        weather: str,
        scene: str,
        max_length: int = 100
    ) -> Dict[str, Any]:
        """
        获取提示词预览

        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            max_length: 预览最大长度

        Returns:
            Dict[str, str]: 提示词预览
        """
        try:
            return self.fixed_prompt_service.get_prompt_preview(
                military_target, weather, scene, max_length
            )
        except Exception as e:
            logger.warning(f"获取提示词预览失败: {str(e)}")
            return {
                "positive_preview": "错误",
                "negative_preview": "错误",
                "positive_length": 0,
                "negative_length": 0
            }

    def get_matrix_statistics(self) -> Dict[str, Any]:
        """
        获取提示词矩阵统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            return self.fixed_prompt_service.get_matrix_statistics()
        except Exception as e:
            logger.warning(f"获取矩阵统计失败: {str(e)}")
            return {
                "total_combinations": 0,
                "is_complete": False,
                "error": str(e)
            }

    def get_available_models(self) -> Dict[str, Any]:
        """
        获取可用的AI模型列表

        Returns:
            Dict[str, Any]: 可用模型信息
        """
        return {
            "models": self.model_manager.get_available_models(),
            "current_model": self.model_manager.get_current_model_info(),
            "recommendations": self.model_manager.get_model_recommendations("small")
        }

    def switch_model(self, model_key: str) -> bool:
        """
        切换AI模型

        Args:
            model_key: 模型键名

        Returns:
            bool: 切换是否成功
        """
        try:
            success = self.model_manager.load_model(model_key)
            if success:
                # 更新SD生成器使用新模型
                new_model = self.model_manager.get_current_model()
                if new_model:
                    self.sd_generator.pipe = new_model
                    logger.info(f"成功切换到模型: {model_key}")
                    return True
            return False
        except Exception as e:
            logger.error(f"模型切换失败: {str(e)}")
            return False

    def preload_model(self, model_key: str) -> bool:
        """
        预加载AI模型到缓存中，不切换当前模型

        Args:
            model_key: 模型键名

        Returns:
            bool: 预加载是否成功
        """
        try:
            success = self.model_manager.preload_model(model_key)
            if success:
                logger.info(f"成功预加载模型: {model_key}")
                return True
            return False
        except Exception as e:
            logger.error(f"模型预加载失败: {str(e)}")
            return False

    def get_cached_models(self) -> List[str]:
        """
        获取已缓存的模型列表

        Returns:
            List[str]: 已缓存的模型键名列表
        """
        return self.model_manager.get_cached_models()

    def set_proxy_port(self, port: int) -> bool:
        """
        设置模型下载代理端口

        Args:
            port: 代理端口号，设置为0表示不使用代理

        Returns:
            bool: 设置是否成功
        """
        try:
            self.model_manager.set_proxy_port(port)
            logger.info(f"代理端口已设置为: {port}")
            return True
        except Exception as e:
            logger.error(f"设置代理端口失败: {str(e)}")
            return False

    def get_proxy_status(self) -> Dict[str, Any]:
        """
        获取当前代理配置状态

        Returns:
            Dict[str, Any]: 代理状态信息
        """
        import os

        proxy_port = getattr(self.model_manager, 'proxy_port', 0)
        proxy_enabled = proxy_port > 0

        # 检查环境变量
        http_proxy = os.environ.get('HTTP_PROXY', '')
        https_proxy = os.environ.get('HTTPS_PROXY', '')

        return {
            "proxy_port": proxy_port,
            "proxy_enabled": proxy_enabled,
            "proxy_url": f"http://127.0.0.1:{proxy_port}" if proxy_enabled else "",
            "environment_variables": {
                "HTTP_PROXY": http_proxy,
                "HTTPS_PROXY": https_proxy,
                "NO_PROXY": os.environ.get('NO_PROXY', '')
            },
            "status": "enabled" if proxy_enabled else "disabled"
        }

    def build_advanced_prompt(
        self,
        military_target: str,
        weather: str,
        scene: str,
        target_size_ratio: float = 0.05,
        composition_strategy: str = "aerial",
        use_advanced_builder: bool = True
    ) -> Tuple[str, str]:
        """
        使用高级提示词构建器生成针对小目标优化的提示词

        Args:
            military_target: 军事目标
            weather: 天气条件
            scene: 场景环境
            target_size_ratio: 目标尺寸比例
            composition_strategy: 构图策略
            use_advanced_builder: 是否使用高级构建器

        Returns:
            Tuple[str, str]: (正面提示词, 负面提示词)
        """
        if use_advanced_builder and target_size_ratio <= 0.10:
            # 对于小目标使用高级构建器
            positive_prompt, negative_prompt = self.advanced_prompt_builder.build_small_target_prompt(
                military_target=military_target,
                weather=weather,
                scene=scene,
                target_size_ratio=target_size_ratio,
                composition_strategy=composition_strategy
            )

            # 根据当前模型优化提示词
            current_model_info = self.model_manager.get_current_model_info()
            if current_model_info.get("loaded"):
                model_key = current_model_info.get("model_key", "stable-diffusion-v1-5")
                positive_prompt = self.advanced_prompt_builder.optimize_for_model(
                    positive_prompt, model_key
                )

            return positive_prompt, negative_prompt
        else:
            # 使用标准构建器
            return self.build_prompt_from_selection(
                military_target=military_target,
                weather=weather,
                scene=scene,
                target_size_ratio=target_size_ratio
            )

    def analyze_prompt_for_small_targets(self, prompt: str) -> Dict[str, Any]:
        """
        分析提示词对小目标生成的有效性

        Args:
            prompt: 提示词

        Returns:
            Dict[str, Any]: 分析结果
        """
        return self.advanced_prompt_builder.analyze_prompt_effectiveness(prompt)

    def get_optimal_generation_params(self) -> Dict[str, Any]:
        """
        获取当前模型的最优生成参数

        Returns:
            Dict[str, Any]: 最优参数
        """
        return self.model_manager.get_optimal_generation_params()
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息

        Returns:
            Dict[str, Any]: 模型信息
        """
        info = self.sd_generator.get_model_info()

        # 使用固定提示词服务获取支持的选项
        try:
            supported_options = self.get_supported_options()
            available_targets = supported_options.get("military_targets", [])
            available_weather = supported_options.get("weather_conditions", [])
            available_scenes = supported_options.get("scene_environments", [])
        except Exception as e:
            logger.warning(f"获取支持选项失败: {str(e)}")
            # 使用默认值
            available_targets = ["坦克", "战机", "舰艇"]
            available_weather = ["雨天", "雪天", "大雾", "夜间"]
            available_scenes = ["城市", "岛屿", "乡村"]

        info.update({
            "service_initialized": self.is_model_loaded,
            "generation_count": len(self.generation_history),
            "available_targets": available_targets,
            "available_weather": available_weather,
            "available_scenes": available_scenes
        })
        return info
    
    def get_generation_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取生成历史
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List[Dict[str, Any]]: 生成历史记录
        """
        return self.generation_history[-limit:]
    
    def clear_generation_history(self):
        """清空生成历史"""
        self.generation_history.clear()
        logger.info("生成历史已清空")
    
    async def batch_generate(
        self,
        generation_configs: List[Dict[str, Any]],
        progress_callback: Optional[callable] = None
    ) -> List[Dict[str, Any]]:
        """
        批量生成图像
        
        Args:
            generation_configs: 生成配置列表
            progress_callback: 进度回调函数
            
        Returns:
            List[Dict[str, Any]]: 批量生成结果
        """
        results = []
        total_configs = len(generation_configs)
        
        for i, config in enumerate(generation_configs):
            try:
                # 详细记录每个配置的参数
                target = config.get('military_target', '未知')
                weather = config.get('weather', '未知')
                scene = config.get('scene', '未知')
                seed = config.get('seed', -1)

                logger.info(f"批量生成进度: {i+1}/{total_configs} - 目标:{target}, 天气:{weather}, 场景:{scene}, 种子:{seed}")

                result = await self.generate_images(**config)
                results.append(result)

                if progress_callback:
                    progress_callback(i + 1, total_configs, result)

            except Exception as e:
                logger.error(f"批量生成第 {i+1} 项失败: {str(e)}")
                results.append({
                    "error": str(e),
                    "config": config,
                    "success": False
                })
        
        logger.info(f"批量生成完成，成功: {sum(1 for r in results if r.get('success', True))}/{total_configs}")
        return results
    
    def cleanup(self):
        """清理资源"""
        if self.sd_generator:
            self.sd_generator.unload_model()
        self.is_model_loaded = False
        logger.info("AI生成服务资源已清理") 