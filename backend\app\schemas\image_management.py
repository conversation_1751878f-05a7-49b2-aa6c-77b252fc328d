"""
图片管理相关的Pydantic模式
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class ImageBase(BaseModel):
    """图片基础模式"""
    filename: str = Field(..., description="文件名")
    description: Optional[str] = Field(None, description="描述")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签列表")
    category: Optional[str] = Field(None, description="分类")
    is_favorite: bool = Field(False, description="是否收藏")


class ImageCreate(ImageBase):
    """创建图片请求模式"""
    generation_type: str = Field("uploaded", description="生成类型")
    military_target: Optional[str] = Field(None, description="军事目标")
    weather: Optional[str] = Field(None, description="天气")
    scene: Optional[str] = Field(None, description="场景")


class ImageUpdate(BaseModel):
    """更新图片请求模式"""
    description: Optional[str] = Field(None, description="描述")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    category: Optional[str] = Field(None, description="分类")
    is_favorite: Optional[bool] = Field(None, description="是否收藏")


class ImageResponse(ImageBase):
    """图片响应模式"""
    id: int
    file_path: str
    file_size: int
    file_hash: str
    width: int
    height: int
    format: str
    generation_type: str
    generation_id: Optional[str]
    military_target: Optional[str]
    weather: Optional[str]
    scene: Optional[str]
    prompt: Optional[str]
    negative_prompt: Optional[str]
    steps: Optional[int]
    cfg_scale: Optional[float]
    seed: Optional[int]
    scheduler_name: Optional[str]
    model_name: Optional[str]
    created_at: datetime
    updated_at: datetime
    annotation_count: int

    class Config:
        from_attributes = True


class ImageListRequest(BaseModel):
    """图片列表查询请求"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    generation_type: Optional[str] = Field(None, description="生成类型筛选")
    military_target: Optional[str] = Field(None, description="军事目标筛选")
    weather: Optional[str] = Field(None, description="天气筛选")
    scene: Optional[str] = Field(None, description="场景筛选")
    category: Optional[str] = Field(None, description="分类筛选")
    is_favorite: Optional[bool] = Field(None, description="收藏筛选")
    tags: Optional[List[str]] = Field(None, description="标签筛选")
    date_from: Optional[datetime] = Field(None, description="开始日期")
    date_to: Optional[datetime] = Field(None, description="结束日期")
    search_text: Optional[str] = Field(None, description="搜索文本")
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: str = Field("desc", description="排序方向")


class ImageListResponse(BaseModel):
    """图片列表响应"""
    images: List[ImageResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class BatchDeleteRequest(BaseModel):
    """批量删除请求"""
    image_ids: List[int] = Field(..., description="图片ID列表")


class BatchUpdateRequest(BaseModel):
    """批量更新请求"""
    image_ids: List[int] = Field(..., description="图片ID列表")
    updates: ImageUpdate = Field(..., description="更新内容")


class AnnotationBase(BaseModel):
    """标注基础模式"""
    label: str = Field(..., description="标签")
    bbox_x: float = Field(..., description="边界框X坐标")
    bbox_y: float = Field(..., description="边界框Y坐标")
    bbox_width: float = Field(..., description="边界框宽度")
    bbox_height: float = Field(..., description="边界框高度")
    confidence: Optional[float] = Field(None, description="置信度")


class AnnotationCreate(AnnotationBase):
    """创建标注请求"""
    source: str = Field("manual", description="标注来源")


class AnnotationResponse(AnnotationBase):
    """标注响应模式"""
    id: int
    image_id: int
    annotation_type: str
    format: str
    source: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ImageStatistics(BaseModel):
    """图片统计信息"""
    total_images: int
    generation_types: Dict[str, int]
    military_targets: Dict[str, int]
    recent_week_count: int


class UploadResponse(BaseModel):
    """上传响应"""
    success: bool
    message: str
    image: Optional[ImageResponse] = None


class APIResponse(BaseModel):
    """通用API响应"""
    success: bool
    message: str
    data: Optional[Any] = None
