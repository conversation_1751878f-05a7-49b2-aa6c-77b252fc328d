"""
图片上传对话框
"""
import os
from typing import List, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox, QCheckBox,
    QFileDialog, QMessageBox, QProgressBar, QFrame, QScrollArea,
    QGroupBox, QSpacerItem, QSizePolicy, QWidget
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSize
from PyQt6.QtGui import QPixmap, QFont

# 修复导入路径问题
import sys
import os

# 添加src目录到Python路径，确保可以找到services模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# 使用绝对导入，与main_window.py保持一致
from services.image_management_client import ImageManagementClient
from styles.button_styles import ButtonStyleManager


class UploadWorker(QThread):
    """上传工作线程"""
    upload_finished = pyqtSignal(dict)  # 上传结果
    upload_failed = pyqtSignal(str)  # 错误信息
    progress_updated = pyqtSignal(str)  # 进度更新
    
    def __init__(self, client: ImageManagementClient, file_path: str, metadata: dict):
        super().__init__()
        self.client = client
        self.file_path = file_path
        self.metadata = metadata
    
    def run(self):
        try:
            self.progress_updated.emit("正在上传图片...")
            result = self.client.upload_image(self.file_path, **self.metadata)
            
            if result.get('success'):
                self.upload_finished.emit(result)
            else:
                self.upload_failed.emit(result.get('message', '上传失败'))
        except Exception as e:
            self.upload_failed.emit(f"上传失败: {str(e)}")


class ImageUploadDialog(QDialog):
    """图片上传对话框"""
    upload_completed = pyqtSignal(dict)  # 上传完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.client = ImageManagementClient()
        self.upload_worker = None
        self.selected_file_path = None
        
        self.setWindowTitle("上传图片")
        self.setModal(True)
        self._setup_dialog_size()
        
        self._setup_ui()
        self._connect_signals()

    def _setup_dialog_size(self):
        """根据屏幕尺寸设置对话框大小"""
        from PyQt6.QtGui import QGuiApplication

        screen = QGuiApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # 根据屏幕尺寸调整对话框大小
        if screen_width <= 1280:
            # 小屏幕：使用更紧凑的尺寸
            dialog_width = min(500, int(screen_width * 0.8))
            dialog_height = min(600, int(screen_height * 0.8))
        else:
            # 大屏幕：使用标准尺寸
            dialog_width = 600
            dialog_height = 700

        self.resize(dialog_width, dialog_height)

        # 设置最小尺寸确保可用性
        self.setMinimumSize(450, 500)
    
    def _setup_ui(self):
        """设置UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 创建内容widget
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(12)  # 减少间距
        layout.setContentsMargins(15, 15, 15, 15)  # 减少边距
        
        # 标题
        title_label = QLabel("上传图片到数据库")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 文件选择区域
        file_group = QGroupBox("选择文件")
        file_layout = QVBoxLayout(file_group)

        # 文件选择按钮
        file_select_layout = QHBoxLayout()
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("请选择图片文件...")
        self.file_path_edit.setReadOnly(True)

        self.browse_btn = ButtonStyleManager.create_button("浏览...", "secondary", min_width=70)

        file_select_layout.addWidget(self.file_path_edit)
        file_select_layout.addWidget(self.browse_btn)
        file_layout.addLayout(file_select_layout)
        
        # 图片预览
        self.preview_label = QLabel()
        # 使用更小的预览尺寸以适应小屏幕
        preview_width = min(280, int(self.width() * 0.6))
        preview_height = min(180, int(preview_width * 0.67))
        self.preview_label.setFixedSize(preview_width, preview_height)
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #ced4da;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: #6c757d;
            }
        """)
        self.preview_label.setText("图片预览")
        
        preview_layout = QHBoxLayout()
        preview_layout.addStretch()
        preview_layout.addWidget(self.preview_label)
        preview_layout.addStretch()
        file_layout.addLayout(preview_layout)
        
        layout.addWidget(file_group)
        
        # 元数据编辑区域
        metadata_group = QGroupBox("图片信息")
        metadata_layout = QFormLayout(metadata_group)
        metadata_layout.setSpacing(10)
        
        # 描述
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("输入图片描述...")
        metadata_layout.addRow("描述:", self.description_edit)
        
        # 标签
        self.tags_edit = QLineEdit()
        self.tags_edit.setPlaceholderText("输入标签，用逗号分隔...")
        metadata_layout.addRow("标签:", self.tags_edit)
        
        # 分类
        self.category_combo = QComboBox()
        self.category_combo.addItems(["", "训练集", "验证集", "测试集", "其他"])
        metadata_layout.addRow("分类:", self.category_combo)
        
        # 军事目标
        self.military_target_combo = QComboBox()
        self.military_target_combo.addItems(["", "Tank", "Fighter Aircraft", "Warship"])
        metadata_layout.addRow("军事目标:", self.military_target_combo)
        
        # 天气
        self.weather_combo = QComboBox()
        self.weather_combo.addItems(["", "雨天", "雪天", "大雾", "夜间"])
        metadata_layout.addRow("天气:", self.weather_combo)
        
        # 场景
        self.scene_combo = QComboBox()
        self.scene_combo.addItems(["", "城市", "岛屿", "乡村"])
        metadata_layout.addRow("场景:", self.scene_combo)
        
        layout.addWidget(metadata_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        layout.addWidget(self.status_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.cancel_btn = ButtonStyleManager.create_button("取消", "secondary", min_width=70)
        self.upload_btn = ButtonStyleManager.create_button("上传", "primary", min_width=70)
        self.upload_btn.setEnabled(False)

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.upload_btn)

        layout.addLayout(button_layout)

        # 将内容widget放入滚动区域
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
    
    def _connect_signals(self):
        """连接信号"""
        self.browse_btn.clicked.connect(self._browse_file)
        self.upload_btn.clicked.connect(self._upload_image)
        self.cancel_btn.clicked.connect(self.reject)
        
        # 文件路径改变时更新上传按钮状态
        self.file_path_edit.textChanged.connect(self._update_upload_button)
    
    def _browse_file(self):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图片文件", "", 
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.tiff);;所有文件 (*)"
        )
        
        if file_path:
            self.selected_file_path = file_path
            self.file_path_edit.setText(file_path)
            self._load_preview(file_path)
            self._auto_fill_metadata(file_path)
    
    def _load_preview(self, file_path: str):
        """加载图片预览"""
        try:
            pixmap = QPixmap(file_path)
            if not pixmap.isNull():
                # 缩放图片以适应预览区域
                scaled_pixmap = pixmap.scaled(
                    self.preview_label.size(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                self.preview_label.setPixmap(scaled_pixmap)
                
                # 显示图片信息
                width = pixmap.width()
                height = pixmap.height()
                file_size = os.path.getsize(file_path)
                size_mb = file_size / (1024 * 1024)
                
                self.status_label.setText(
                    f"图片尺寸: {width}×{height} | 文件大小: {size_mb:.2f} MB"
                )
            else:
                self.preview_label.setText("无法预览图片")
                self.status_label.setText("图片格式不支持")
        except Exception as e:
            self.preview_label.setText("预览失败")
            self.status_label.setText(f"预览失败: {str(e)}")
    
    def _auto_fill_metadata(self, file_path: str):
        """根据文件名自动填充元数据"""
        filename = os.path.basename(file_path).lower()
        
        # 根据文件名推测军事目标
        if any(keyword in filename for keyword in ['tank', '坦克', 'armor']):
            self.military_target_combo.setCurrentText("Tank")
        elif any(keyword in filename for keyword in ['aircraft', 'fighter', '战机', 'plane']):
            self.military_target_combo.setCurrentText("Fighter Aircraft")
        elif any(keyword in filename for keyword in ['ship', 'warship', '舰艇', 'naval']):
            self.military_target_combo.setCurrentText("Warship")
        
        # 根据文件名推测天气
        if any(keyword in filename for keyword in ['rain', '雨', 'wet']):
            self.weather_combo.setCurrentText("雨天")
        elif any(keyword in filename for keyword in ['snow', '雪', 'winter']):
            self.weather_combo.setCurrentText("雪天")
        elif any(keyword in filename for keyword in ['fog', '雾', 'mist']):
            self.weather_combo.setCurrentText("大雾")
        elif any(keyword in filename for keyword in ['night', '夜', 'dark']):
            self.weather_combo.setCurrentText("夜间")
        
        # 根据文件名推测场景
        if any(keyword in filename for keyword in ['city', '城市', 'urban']):
            self.scene_combo.setCurrentText("城市")
        elif any(keyword in filename for keyword in ['island', '岛', 'sea']):
            self.scene_combo.setCurrentText("岛屿")
        elif any(keyword in filename for keyword in ['rural', '乡村', 'village']):
            self.scene_combo.setCurrentText("乡村")
    
    def _update_upload_button(self):
        """更新上传按钮状态"""
        has_file = bool(self.file_path_edit.text().strip())
        self.upload_btn.setEnabled(has_file and not (self.upload_worker and self.upload_worker.isRunning()))
    
    def _upload_image(self):
        """上传图片"""
        if not self.selected_file_path or not os.path.exists(self.selected_file_path):
            QMessageBox.warning(self, "警告", "请先选择有效的图片文件")
            return
        
        # 收集元数据
        metadata = {}
        
        description = self.description_edit.toPlainText().strip()
        if description:
            metadata['description'] = description
        
        tags_text = self.tags_edit.text().strip()
        if tags_text:
            tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()]
            metadata['tags'] = tags
        
        category = self.category_combo.currentText()
        if category:
            metadata['category'] = category
        
        military_target = self.military_target_combo.currentText()
        if military_target:
            metadata['military_target'] = military_target
        
        weather = self.weather_combo.currentText()
        if weather:
            metadata['weather'] = weather
        
        scene = self.scene_combo.currentText()
        if scene:
            metadata['scene'] = scene
        
        # 禁用按钮并显示进度
        self.upload_btn.setEnabled(False)
        self.upload_btn.setText("上传中...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        # 启动上传工作线程
        self.upload_worker = UploadWorker(self.client, self.selected_file_path, metadata)
        self.upload_worker.upload_finished.connect(self._on_upload_finished)
        self.upload_worker.upload_failed.connect(self._on_upload_failed)
        self.upload_worker.progress_updated.connect(self._on_progress_updated)
        self.upload_worker.start()
    
    def _on_upload_finished(self, result):
        """上传完成"""
        self.progress_bar.setVisible(False)
        self.upload_btn.setText("上传")
        self.upload_btn.setEnabled(True)
        
        image_data = result.get('image')
        if image_data:
            QMessageBox.information(
                self, "上传成功", 
                f"图片上传成功！\n文件名: {image_data.get('filename')}\nID: {image_data.get('id')}"
            )
            self.upload_completed.emit(result)
            self.accept()
        else:
            QMessageBox.information(self, "上传成功", "图片上传成功！")
            self.upload_completed.emit(result)
            self.accept()
    
    def _on_upload_failed(self, error_message):
        """上传失败"""
        self.progress_bar.setVisible(False)
        self.upload_btn.setText("上传")
        self.upload_btn.setEnabled(True)
        
        QMessageBox.critical(self, "上传失败", f"图片上传失败:\n{error_message}")
    
    def _on_progress_updated(self, message):
        """进度更新"""
        self.status_label.setText(message)
