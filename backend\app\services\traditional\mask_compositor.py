"""
智能抠图算法实现
"""
import cv2
import numpy as np
from PIL import Image
from typing import Optional, Tu<PERSON>, Dict, Any, List
import logging
from skimage import segmentation, filters, morphology
from skimage.feature import canny
from scipy import ndimage
import time

from .core_types import (
    ImageData, BoundingBox, MattingConfig, MattingMethod,
    ProcessingResult, MattingProcessor
)

logger = logging.getLogger(__name__)


class GrabCutMatting(MattingProcessor):
    """基于GrabCut的智能抠图"""

    def __init__(self, config: MattingConfig):
        super().__init__(config)

    def extract_foreground(self, image_data: ImageData, bbox: Optional[BoundingBox] = None) -> ProcessingResult:
        """使用GrabCut提取前景"""
        start_time = time.time()

        try:
            # 转换为OpenCV格式
            if isinstance(image_data.image, Image.Image):
                img = cv2.cvtColor(np.array(image_data.image), cv2.COLOR_RGB2BGR)
            else:
                img = image_data.image

            height, width = img.shape[:2]

            # 如果没有提供边界框，使用整个图像的中心区域
            if bbox is None:
                margin = self.config.margin
                bbox = BoundingBox(
                    x=margin,
                    y=margin,
                    width=width - 2 * margin,
                    height=height - 2 * margin
                )

            # 初始化蒙版
            mask = np.zeros((height, width), np.uint8)

            # 设置前景和背景区域
            # 0: 确定背景, 1: 确定前景, 2: 可能背景, 3: 可能前景
            mask[:, :] = cv2.GC_BGD  # 背景
            mask[bbox.y:bbox.y2, bbox.x:bbox.x2] = cv2.GC_PR_FGD  # 可能前景

            # 创建前景和背景模型
            bgd_model = np.zeros((1, 65), np.float64)
            fgd_model = np.zeros((1, 65), np.float64)

            # 执行GrabCut算法
            rect = (bbox.x, bbox.y, bbox.width, bbox.height)
            cv2.grabCut(img, mask, rect, bgd_model, fgd_model,
                       self.config.iterations, cv2.GC_INIT_WITH_RECT)

            # 生成最终蒙版
            final_mask = np.where((mask == cv2.GC_FGD) | (mask == cv2.GC_PR_FGD), 255, 0).astype(np.uint8)

            # 后处理优化
            if self.config.auto_refine:
                final_mask = self.refine_mask(final_mask, img)

            # 边缘平滑
            if self.config.edge_smooth:
                final_mask = self._smooth_edges(final_mask)

            # 羽化处理
            if self.config.feather_radius > 0:
                final_mask = self._feather_mask(final_mask, self.config.feather_radius)

            processing_time = time.time() - start_time

            return ProcessingResult(
                success=True,
                mask=final_mask,
                processing_time=processing_time,
                metadata={
                    'method': 'grabcut',
                    'iterations': self.config.iterations,
                    'bbox': bbox
                }
            )

        except Exception as e:
            logger.error(f"GrabCut抠图失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def refine_mask(self, mask: np.ndarray, image: np.ndarray) -> np.ndarray:
        """优化蒙版"""
        # 形态学操作去除噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

        # 填充小洞
        mask = ndimage.binary_fill_holes(mask > 0).astype(np.uint8) * 255

        return mask

    def _smooth_edges(self, mask: np.ndarray) -> np.ndarray:
        """平滑边缘"""
        # 高斯模糊
        blurred = cv2.GaussianBlur(mask, (5, 5), 1.0)
        return blurred

    def _feather_mask(self, mask: np.ndarray, radius: int) -> np.ndarray:
        """羽化蒙版"""
        # 距离变换
        dist = cv2.distanceTransform(mask, cv2.DIST_L2, 5)

        # 创建羽化效果
        feathered = np.clip(dist / radius * 255, 0, 255).astype(np.uint8)
        feathered = np.where(mask > 0, feathered, 0)

        return feathered


class WatershedMatting(MattingProcessor):
    """基于分水岭算法的抠图"""

    def extract_foreground(self, image_data: ImageData, bbox: Optional[BoundingBox] = None) -> ProcessingResult:
        """使用分水岭算法提取前景"""
        start_time = time.time()

        try:
            # 转换为灰度图
            if isinstance(image_data.image, Image.Image):
                img = np.array(image_data.image.convert('L'))
            else:
                img = cv2.cvtColor(image_data.image, cv2.COLOR_BGR2GRAY)

            # 边缘检测
            edges = canny(img, sigma=1.0, low_threshold=0.1, high_threshold=0.2)

            # 距离变换
            distance = ndimage.distance_transform_edt(~edges)

            # 寻找局部最大值作为种子点
            local_maxima = morphology.local_maxima(distance, min_distance=20)
            markers = ndimage.label(local_maxima)[0]

            # 分水岭分割
            labels = segmentation.watershed(-distance, markers, mask=~edges)

            # 选择最大的连通区域作为前景
            if bbox:
                center_x, center_y = bbox.center
                center_label = labels[center_y, center_x]
                mask = (labels == center_label).astype(np.uint8) * 255
            else:
                # 选择最大的区域
                unique, counts = np.unique(labels, return_counts=True)
                largest_label = unique[np.argmax(counts[1:]) + 1]  # 跳过背景标签0
                mask = (labels == largest_label).astype(np.uint8) * 255

            # 后处理
            if self.config.auto_refine:
                mask = self.refine_mask(mask, img)

            processing_time = time.time() - start_time

            return ProcessingResult(
                success=True,
                mask=mask,
                processing_time=processing_time,
                metadata={'method': 'watershed'}
            )

        except Exception as e:
            logger.error(f"分水岭抠图失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )


class RembgMatting(MattingProcessor):
    """基于rembg的AI抠图"""

    def __init__(self, config: MattingConfig):
        super().__init__(config)
        self._model = None

    def _load_model(self):
        """延迟加载模型"""
        if self._model is None:
            try:
                from rembg import remove, new_session
                self._model = new_session('u2net')
            except ImportError:
                raise ImportError("请安装rembg库: pip install rembg")

    def extract_foreground(self, image_data: ImageData, bbox: Optional[BoundingBox] = None) -> ProcessingResult:
        """使用AI模型提取前景"""
        start_time = time.time()

        try:
            self._load_model()

            # 转换为PIL图像
            if isinstance(image_data.image, np.ndarray):
                img = Image.fromarray(image_data.image)
            else:
                img = image_data.image

            # 使用rembg移除背景
            from rembg import remove
            result = remove(img, session=self._model)

            # 提取alpha通道作为蒙版
            if result.mode == 'RGBA':
                mask = np.array(result)[:, :, 3]
            else:
                # 如果没有alpha通道，创建简单的蒙版
                gray = np.array(result.convert('L'))
                mask = np.where(gray > 10, 255, 0).astype(np.uint8)

            # 后处理
            if self.config.auto_refine:
                mask = self.refine_mask(mask, np.array(img))

            processing_time = time.time() - start_time

            return ProcessingResult(
                success=True,
                mask=mask,
                processing_time=processing_time,
                metadata={'method': 'rembg'}
            )

        except Exception as e:
            logger.error(f"AI抠图失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )


class ThresholdMatting(MattingProcessor):
    """基于阈值的简单抠图"""

    def extract_foreground(self, image_data: ImageData, bbox: Optional[BoundingBox] = None) -> ProcessingResult:
        """使用阈值方法提取前景"""
        start_time = time.time()

        try:
            # 转换为灰度图
            if isinstance(image_data.image, Image.Image):
                img = np.array(image_data.image.convert('L'))
            else:
                img = cv2.cvtColor(image_data.image, cv2.COLOR_BGR2GRAY)

            # 自适应阈值
            mask = cv2.adaptiveThreshold(
                img, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 11, 2
            )

            # 反转蒙版（假设前景比背景暗）
            mask = cv2.bitwise_not(mask)

            # 形态学操作清理
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

            # 如果提供了边界框，只保留框内的前景
            if bbox:
                temp_mask = np.zeros_like(mask)
                temp_mask[bbox.y:bbox.y2, bbox.x:bbox.x2] = mask[bbox.y:bbox.y2, bbox.x:bbox.x2]
                mask = temp_mask

            processing_time = time.time() - start_time

            return ProcessingResult(
                success=True,
                mask=mask,
                processing_time=processing_time,
                metadata={'method': 'threshold'}
            )

        except Exception as e:
            logger.error(f"阈值抠图失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )


class MattingFactory:
    """抠图算法工厂类"""

    @staticmethod
    def create_matting_processor(config: MattingConfig) -> MattingProcessor:
        """创建抠图处理器"""
        if config.method == MattingMethod.GRABCUT:
            return GrabCutMatting(config)
        elif config.method == MattingMethod.WATERSHED:
            return WatershedMatting(config)
        elif config.method == MattingMethod.REMBG:
            return RembgMatting(config)
        elif config.method == MattingMethod.THRESHOLD:
            return ThresholdMatting(config)
        else:
            raise ValueError(f"不支持的抠图方法: {config.method}")

    @staticmethod
    def get_available_methods() -> List[MattingMethod]:
        """获取可用的抠图方法"""
        return [
            MattingMethod.GRABCUT,
            MattingMethod.WATERSHED,
            MattingMethod.THRESHOLD,
            MattingMethod.REMBG  # 需要额外安装rembg
        ]

    @staticmethod
    def get_method_description(method: MattingMethod) -> str:
        """获取方法描述"""
        descriptions = {
            MattingMethod.GRABCUT: "基于GrabCut的交互式抠图，适合复杂背景",
            MattingMethod.WATERSHED: "基于分水岭算法的自动分割，适合边界清晰的对象",
            MattingMethod.REMBG: "基于AI的自动抠图，效果最好但需要额外依赖",
            MattingMethod.THRESHOLD: "基于阈值的简单抠图，适合背景单一的情况"
        }
        return descriptions.get(method, "未知方法")


def auto_detect_best_method(image_data: ImageData) -> MattingMethod:
    """自动检测最适合的抠图方法"""
    try:
        # 转换为灰度图进行分析
        if isinstance(image_data.image, Image.Image):
            gray = np.array(image_data.image.convert('L'))
        else:
            gray = cv2.cvtColor(image_data.image, cv2.COLOR_BGR2GRAY)

        # 计算图像复杂度指标
        # 1. 边缘密度
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size

        # 2. 颜色复杂度（标准差）
        color_complexity = np.std(gray)

        # 3. 纹理复杂度（局部二值模式）
        from skimage.feature import local_binary_pattern
        lbp = local_binary_pattern(gray, 8, 1, method='uniform')
        texture_complexity = len(np.unique(lbp))

        # 根据复杂度选择方法
        if edge_density > 0.1 and color_complexity > 50:
            return MattingMethod.REMBG  # 复杂场景用AI
        elif edge_density > 0.05:
            return MattingMethod.GRABCUT  # 中等复杂度用GrabCut
        elif texture_complexity < 20:
            return MattingMethod.THRESHOLD  # 简单场景用阈值
        else:
            return MattingMethod.WATERSHED  # 默认用分水岭

    except Exception as e:
        logger.warning(f"自动检测抠图方法失败: {str(e)}, 使用默认方法")
        return MattingMethod.GRABCUT