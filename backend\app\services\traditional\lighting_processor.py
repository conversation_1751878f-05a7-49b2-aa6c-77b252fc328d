"""
光照和阴影效果处理器
"""
import cv2
import numpy as np
from PIL import Image
from typing import Optional, Tuple, Dict, Any, List
import logging
from skimage import filters, morphology
import time
import math

from .core_types import (
    ImageData, WeatherEffect, ProcessingResult, EffectProcessor
)

logger = logging.getLogger(__name__)


class LightingProcessor(EffectProcessor):
    """光照处理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        self.config = config or {}
    
    def apply_weather_effect(self, image_data: ImageData, weather: WeatherEffect) -> ProcessingResult:
        """应用天气效果"""
        start_time = time.time()
        
        try:
            image = self._to_numpy(image_data.image)
            
            if weather == WeatherEffect.SUNNY:
                result = self._apply_sunny_effect(image)
            elif weather == WeatherEffect.RAINY:
                result = self._apply_rainy_effect(image)
            elif weather == WeatherEffect.SNOWY:
                result = self._apply_snowy_effect(image)
            elif weather == WeatherEffect.FOGGY:
                result = self._apply_foggy_effect(image)
            elif weather == WeatherEffect.CLOUDY:
                result = self._apply_cloudy_effect(image)
            elif weather == WeatherEffect.NIGHT:
                result = self._apply_night_effect(image)
            else:
                result = image
            
            processing_time = time.time() - start_time
            
            return ProcessingResult(
                success=True,
                image=ImageData(image=result),
                processing_time=processing_time,
                metadata={'weather_effect': weather.value}
            )
            
        except Exception as e:
            logger.error(f"天气效果应用失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    def adjust_lighting(self, image_data: ImageData, lighting_params: Dict[str, float]) -> ProcessingResult:
        """调整光照"""
        start_time = time.time()
        
        try:
            image = self._to_numpy(image_data.image)
            
            # 提取参数
            brightness = lighting_params.get('brightness', 0.0)
            contrast = lighting_params.get('contrast', 1.0)
            gamma = lighting_params.get('gamma', 1.0)
            saturation = lighting_params.get('saturation', 1.0)
            
            # 应用亮度调整
            if brightness != 0.0:
                image = self._adjust_brightness(image, brightness)
            
            # 应用对比度调整
            if contrast != 1.0:
                image = self._adjust_contrast(image, contrast)
            
            # 应用伽马校正
            if gamma != 1.0:
                image = self._adjust_gamma(image, gamma)
            
            # 应用饱和度调整
            if saturation != 1.0:
                image = self._adjust_saturation(image, saturation)
            
            processing_time = time.time() - start_time
            
            return ProcessingResult(
                success=True,
                image=ImageData(image=image),
                processing_time=processing_time,
                metadata={'lighting_params': lighting_params}
            )
            
        except Exception as e:
            logger.error(f"光照调整失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    def generate_realistic_shadow(self, foreground: np.ndarray, mask: np.ndarray, 
                                 light_angle: float, light_elevation: float,
                                 ground_plane: Optional[np.ndarray] = None) -> np.ndarray:
        """生成真实阴影"""
        try:
            # 计算阴影投射方向
            shadow_dx = math.cos(math.radians(light_angle)) * math.cos(math.radians(light_elevation))
            shadow_dy = math.sin(math.radians(light_angle)) * math.cos(math.radians(light_elevation))
            
            # 阴影长度基于光照高度角
            shadow_length = 50 / (math.tan(math.radians(max(light_elevation, 10))) + 0.1)
            
            # 创建阴影变换矩阵
            shadow_transform = np.array([
                [1, 0, shadow_dx * shadow_length],
                [shadow_dy * 0.3, 0.7, shadow_dy * shadow_length],  # 透视变换
                [0, 0, 1]
            ], dtype=np.float32)
            
            # 应用透视变换
            height, width = mask.shape
            shadow_mask = cv2.warpPerspective(mask, shadow_transform, (width, height))
            
            # 模糊阴影边缘
            blur_size = max(5, int(shadow_length / 10))
            shadow_mask = cv2.GaussianBlur(shadow_mask, (blur_size*2+1, blur_size*2+1), 0)
            
            # 阴影强度随距离衰减
            shadow_intensity = 0.6 * (1 - min(shadow_length / 100, 0.8))
            shadow_mask = (shadow_mask * shadow_intensity).astype(np.uint8)
            
            # 确保阴影不与原对象重叠
            shadow_mask = np.where(mask > 0, 0, shadow_mask)
            
            return shadow_mask
            
        except Exception as e:
            logger.warning(f"真实阴影生成失败: {str(e)}")
            return np.zeros_like(mask)
    
    def _to_numpy(self, image: Any) -> np.ndarray:
        """转换为numpy数组"""
        if isinstance(image, Image.Image):
            return np.array(image)
        elif isinstance(image, np.ndarray):
            return image
        else:
            raise ValueError(f"不支持的图像类型: {type(image)}")
    
    def _apply_sunny_effect(self, image: np.ndarray) -> np.ndarray:
        """应用晴天效果"""
        # 增加亮度和对比度
        image = self._adjust_brightness(image, 10)
        image = self._adjust_contrast(image, 1.1)
        image = self._adjust_saturation(image, 1.2)
        return image
    
    def _apply_rainy_effect(self, image: np.ndarray) -> np.ndarray:
        """应用雨天效果"""
        # 降低亮度，增加蓝色调
        image = self._adjust_brightness(image, -15)
        image = self._adjust_contrast(image, 0.9)
        
        # 添加蓝色色调
        image_hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV).astype(np.float32)
        image_hsv[:, :, 0] = (image_hsv[:, :, 0] + 10) % 180  # 偏向蓝色
        image_hsv[:, :, 1] *= 1.1  # 增加饱和度
        image = cv2.cvtColor(image_hsv.astype(np.uint8), cv2.COLOR_HSV2RGB)
        
        return image
    
    def _apply_snowy_effect(self, image: np.ndarray) -> np.ndarray:
        """应用雪天效果"""
        # 增加亮度，降低对比度，偏向冷色调
        image = self._adjust_brightness(image, 20)
        image = self._adjust_contrast(image, 0.8)
        
        # 添加冷色调
        image_lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB).astype(np.float32)
        image_lab[:, :, 2] -= 5  # 减少黄色，增加蓝色
        image = cv2.cvtColor(np.clip(image_lab, 0, 255).astype(np.uint8), cv2.COLOR_LAB2RGB)
        
        return image
    
    def _apply_foggy_effect(self, image: np.ndarray) -> np.ndarray:
        """应用雾天效果"""
        # 降低对比度，增加灰度感
        image = self._adjust_contrast(image, 0.7)
        image = self._adjust_saturation(image, 0.6)
        
        # 添加雾气效果（高斯模糊 + 亮度提升）
        blurred = cv2.GaussianBlur(image, (3, 3), 0)
        image = cv2.addWeighted(image, 0.7, blurred, 0.3, 10)
        
        return image
    
    def _apply_cloudy_effect(self, image: np.ndarray) -> np.ndarray:
        """应用阴天效果"""
        # 轻微降低亮度和对比度
        image = self._adjust_brightness(image, -5)
        image = self._adjust_contrast(image, 0.95)
        image = self._adjust_saturation(image, 0.9)
        return image
    
    def _apply_night_effect(self, image: np.ndarray) -> np.ndarray:
        """应用夜间效果"""
        # 大幅降低亮度，增加蓝色调
        image = self._adjust_brightness(image, -40)
        image = self._adjust_contrast(image, 1.2)
        
        # 添加蓝色夜间色调
        image_hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV).astype(np.float32)
        image_hsv[:, :, 0] = (image_hsv[:, :, 0] + 20) % 180  # 偏向蓝色
        image_hsv[:, :, 1] *= 0.8  # 降低饱和度
        image = cv2.cvtColor(image_hsv.astype(np.uint8), cv2.COLOR_HSV2RGB)
        
        return image
    
    def _adjust_brightness(self, image: np.ndarray, value: float) -> np.ndarray:
        """调整亮度"""
        return np.clip(image.astype(np.float32) + value, 0, 255).astype(np.uint8)
    
    def _adjust_contrast(self, image: np.ndarray, factor: float) -> np.ndarray:
        """调整对比度"""
        mean = np.mean(image)
        return np.clip((image.astype(np.float32) - mean) * factor + mean, 0, 255).astype(np.uint8)
    
    def _adjust_gamma(self, image: np.ndarray, gamma: float) -> np.ndarray:
        """伽马校正"""
        inv_gamma = 1.0 / gamma
        table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in range(256)]).astype(np.uint8)
        return cv2.LUT(image, table)
    
    def _adjust_saturation(self, image: np.ndarray, factor: float) -> np.ndarray:
        """调整饱和度"""
        hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV).astype(np.float32)
        hsv[:, :, 1] *= factor
        hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 255)
        return cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2RGB)


def analyze_scene_lighting(image: np.ndarray) -> Dict[str, float]:
    """分析场景光照条件"""
    try:
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # 计算整体亮度
        overall_brightness = np.mean(gray)
        
        # 计算对比度
        contrast = np.std(gray)
        
        # 分析光照方向（基于梯度）
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        
        # 计算主要光照方向
        light_angle = np.degrees(np.arctan2(np.mean(grad_y), np.mean(grad_x)))
        
        # 估算光照高度角（基于阴影长度）
        light_elevation = min(90, max(10, overall_brightness / 255 * 90))
        
        return {
            'brightness': overall_brightness,
            'contrast': contrast,
            'light_angle': light_angle,
            'light_elevation': light_elevation
        }
        
    except Exception as e:
        logger.warning(f"场景光照分析失败: {str(e)}")
        return {
            'brightness': 128,
            'contrast': 50,
            'light_angle': 45,
            'light_elevation': 45
        }
