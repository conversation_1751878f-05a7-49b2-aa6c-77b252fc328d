"""
SD权重文件管理对话框
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, QFormLayout,
    QLineEdit, QTextEdit, QPushButton, QFileDialog, QMessageBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QProgressBar,
    QLabel, QComboBox, QCheckBox, QTabWidget, QWidget, QSplitter
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont

from styles.button_styles import ButtonStyleManager
from services.sd_model_service import SDModelService

logger = logging.getLogger(__name__)


class SDWeightLoadWorker(QThread):
    """SD权重加载工作线程"""
    progress = pyqtSignal(str)
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, sd_service: SDModelService, model_key: str, force_reload: bool = False):
        super().__init__()
        self.sd_service = sd_service
        self.model_key = model_key
        self.force_reload = force_reload
    
    def run(self):
        try:
            self.progress.emit(f"正在加载模型: {self.model_key}")
            result = self.sd_service.load_model(self.model_key, self.force_reload)
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))


class SDWeightsDialog(QDialog):
    """SD权重文件管理对话框"""
    
    def __init__(self, sd_service: SDModelService, parent=None):
        super().__init__(parent)
        self.sd_service = sd_service
        self.load_worker = None
        
        self.setWindowTitle("SD权重文件管理")
        self.setModal(True)
        self.resize(900, 700)
        
        self._setup_ui()
        self._load_models()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 模型列表标签页
        models_tab = self._create_models_tab()
        tab_widget.addTab(models_tab, "模型列表")
        
        # 添加模型标签页
        add_tab = self._create_add_tab()
        tab_widget.addTab(add_tab, "添加模型")
        
        # 自动检测标签页
        detect_tab = self._create_detect_tab()
        tab_widget.addTab(detect_tab, "自动检测")
        
        layout.addWidget(tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.refresh_btn = ButtonStyleManager.create_button("刷新", "secondary")
        self.refresh_btn.clicked.connect(self._load_models)
        
        self.close_btn = ButtonStyleManager.create_button("关闭", "secondary")
        self.close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def _create_models_tab(self) -> QWidget:
        """创建模型列表标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 模型表格
        self.models_table = QTableWidget()
        self.models_table.setColumnCount(6)
        self.models_table.setHorizontalHeaderLabels([
            "模型名称", "类型", "描述", "状态", "文件大小", "操作"
        ])
        
        # 设置表格属性
        header = self.models_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        self.models_table.setAlternatingRowColors(True)
        self.models_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.models_table)
        
        # 批量操作按钮
        batch_layout = QHBoxLayout()
        
        self.clear_cache_btn = ButtonStyleManager.create_button("清理缓存", "warning")
        self.clear_cache_btn.clicked.connect(self._clear_all_cache)
        
        batch_layout.addWidget(self.clear_cache_btn)
        batch_layout.addStretch()
        
        layout.addLayout(batch_layout)
        
        return tab
    
    def _create_add_tab(self) -> QWidget:
        """创建添加模型标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 添加模型表单
        form_group = QGroupBox("添加safetensors模型")
        form_layout = QFormLayout(form_group)
        
        self.model_key_edit = QLineEdit()
        self.model_key_edit.setPlaceholderText("模型唯一标识符，如：my_custom_model")
        form_layout.addRow("模型键名:", self.model_key_edit)
        
        self.model_name_edit = QLineEdit()
        self.model_name_edit.setPlaceholderText("模型显示名称")
        form_layout.addRow("模型名称:", self.model_name_edit)
        
        # 文件选择
        file_layout = QHBoxLayout()
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("选择safetensors文件...")
        
        self.browse_btn = ButtonStyleManager.create_button("浏览", "secondary")
        self.browse_btn.clicked.connect(self._browse_file)
        
        file_layout.addWidget(self.file_path_edit)
        file_layout.addWidget(self.browse_btn)
        form_layout.addRow("文件路径:", file_layout)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("模型描述（可选）")
        form_layout.addRow("描述:", self.description_edit)
        
        layout.addWidget(form_group)
        
        # 验证和添加按钮
        action_layout = QHBoxLayout()
        
        self.validate_btn = ButtonStyleManager.create_button("验证文件", "secondary")
        self.validate_btn.clicked.connect(self._validate_file)
        
        self.add_model_btn = ButtonStyleManager.create_button("添加模型", "success")
        self.add_model_btn.clicked.connect(self._add_model)
        self.add_model_btn.setEnabled(False)
        
        action_layout.addWidget(self.validate_btn)
        action_layout.addWidget(self.add_model_btn)
        action_layout.addStretch()
        
        layout.addLayout(action_layout)
        
        # 验证结果显示
        self.validation_result = QTextEdit()
        self.validation_result.setMaximumHeight(150)
        self.validation_result.setReadOnly(True)
        self.validation_result.setPlaceholderText("文件验证结果将显示在这里...")
        layout.addWidget(self.validation_result)
        
        layout.addStretch()
        
        return tab
    
    def _create_detect_tab(self) -> QWidget:
        """创建自动检测标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 检测设置
        detect_group = QGroupBox("自动检测设置")
        detect_layout = QVBoxLayout(detect_group)
        
        info_label = QLabel("自动扫描指定目录中的safetensors文件")
        info_label.setStyleSheet("color: #666; font-size: 12px;")
        detect_layout.addWidget(info_label)
        
        # 检测按钮
        detect_btn_layout = QHBoxLayout()
        
        self.detect_btn = ButtonStyleManager.create_button("开始检测", "primary")
        self.detect_btn.clicked.connect(self._detect_files)
        
        detect_btn_layout.addWidget(self.detect_btn)
        detect_btn_layout.addStretch()
        
        detect_layout.addLayout(detect_btn_layout)
        
        layout.addWidget(detect_group)
        
        # 检测结果表格
        result_group = QGroupBox("检测结果")
        result_layout = QVBoxLayout(result_group)
        
        self.detect_table = QTableWidget()
        self.detect_table.setColumnCount(5)
        self.detect_table.setHorizontalHeaderLabels([
            "文件名", "路径", "大小", "建议键名", "操作"
        ])
        
        # 设置表格属性
        detect_header = self.detect_table.horizontalHeader()
        detect_header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        detect_header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        detect_header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        detect_header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        detect_header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        self.detect_table.setAlternatingRowColors(True)
        
        result_layout.addWidget(self.detect_table)
        
        layout.addWidget(result_group)
        
        return tab
    
    def _browse_file(self):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择safetensors文件",
            "",
            "SafeTensors文件 (*.safetensors);;所有文件 (*)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
            
            # 自动填充模型名称
            if not self.model_name_edit.text():
                file_name = Path(file_path).stem
                self.model_name_edit.setText(file_name)
            
            # 自动填充模型键名
            if not self.model_key_edit.text():
                key_name = Path(file_path).stem.lower().replace(" ", "_").replace("-", "_")
                self.model_key_edit.setText(key_name)
    
    def _validate_file(self):
        """验证文件"""
        file_path = self.file_path_edit.text().strip()
        if not file_path:
            QMessageBox.warning(self, "警告", "请先选择文件")
            return
        
        if not os.path.exists(file_path):
            QMessageBox.warning(self, "警告", "文件不存在")
            return
        
        try:
            result = self.sd_service.validate_safetensors_file(file_path)
            
            if result.get("success"):
                file_info = result.get("data", {}).get("file_info", {})
                
                validation_text = f"✅ 验证成功\n\n"
                validation_text += f"文件大小: {file_info.get('size', 0) / (1024*1024):.1f} MB\n"
                validation_text += f"权重数量: {file_info.get('total_weights', 0)}\n"
                validation_text += f"模型类型: {file_info.get('model_type', 'unknown')}\n"
                validation_text += f"是否SD模型: {'是' if file_info.get('is_sd_model') else '否'}\n"
                
                if file_info.get('weight_keys'):
                    validation_text += f"\n示例权重键:\n"
                    for key in file_info['weight_keys'][:5]:
                        validation_text += f"  - {key}\n"
                
                self.validation_result.setText(validation_text)
                self.add_model_btn.setEnabled(True)
                
            else:
                validation_text = f"❌ 验证失败\n\n{result.get('message', '未知错误')}"
                self.validation_result.setText(validation_text)
                self.add_model_btn.setEnabled(False)
                
        except Exception as e:
            validation_text = f"❌ 验证异常\n\n{str(e)}"
            self.validation_result.setText(validation_text)
            self.add_model_btn.setEnabled(False)
    
    def _add_model(self):
        """添加模型"""
        model_key = self.model_key_edit.text().strip()
        model_name = self.model_name_edit.text().strip()
        file_path = self.file_path_edit.text().strip()
        description = self.description_edit.toPlainText().strip()
        
        if not all([model_key, model_name, file_path]):
            QMessageBox.warning(self, "警告", "请填写所有必填字段")
            return
        
        try:
            result = self.sd_service.add_safetensors_model(
                model_key=model_key,
                model_name=model_name,
                safetensors_path=file_path,
                description=description
            )
            
            if result.get("success"):
                QMessageBox.information(self, "成功", f"模型添加成功: {model_name}")
                
                # 清空表单
                self.model_key_edit.clear()
                self.model_name_edit.clear()
                self.file_path_edit.clear()
                self.description_edit.clear()
                self.validation_result.clear()
                self.add_model_btn.setEnabled(False)
                
                # 刷新模型列表
                self._load_models()
                
            else:
                QMessageBox.warning(self, "失败", f"添加失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加模型时发生错误: {str(e)}")
    
    def _detect_files(self):
        """检测文件"""
        try:
            detected_files = self.sd_service.auto_detect_safetensors_files()
            
            # 清空表格
            self.detect_table.setRowCount(0)
            
            # 填充检测结果
            for i, file_info in enumerate(detected_files):
                self.detect_table.insertRow(i)
                
                self.detect_table.setItem(i, 0, QTableWidgetItem(file_info["name"]))
                self.detect_table.setItem(i, 1, QTableWidgetItem(file_info["path"]))
                self.detect_table.setItem(i, 2, QTableWidgetItem(f"{file_info['size'] / (1024*1024):.1f} MB"))
                self.detect_table.setItem(i, 3, QTableWidgetItem(file_info["suggested_key"]))
                
                # 添加操作按钮
                add_btn = ButtonStyleManager.create_button("添加", "small")
                add_btn.clicked.connect(lambda checked, info=file_info: self._quick_add_detected(info))
                self.detect_table.setCellWidget(i, 4, add_btn)
            
            if not detected_files:
                QMessageBox.information(self, "检测完成", "未检测到safetensors文件")
            else:
                QMessageBox.information(self, "检测完成", f"检测到 {len(detected_files)} 个文件")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"检测文件时发生错误: {str(e)}")
    
    def _quick_add_detected(self, file_info: Dict[str, Any]):
        """快速添加检测到的文件"""
        try:
            result = self.sd_service.add_safetensors_model(
                model_key=file_info["suggested_key"],
                model_name=file_info["suggested_name"],
                safetensors_path=file_info["path"],
                description=f"自动检测添加: {file_info['name']}"
            )
            
            if result.get("success"):
                QMessageBox.information(self, "成功", f"模型添加成功: {file_info['suggested_name']}")
                self._load_models()
            else:
                QMessageBox.warning(self, "失败", f"添加失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加模型时发生错误: {str(e)}")
    
    def _load_models(self):
        """加载模型列表"""
        try:
            result = self.sd_service.get_available_models()
            
            if result.get("success"):
                models = result.get("data", {}).get("models", {})
                display_models = self.sd_service.get_model_display_info(models)
                
                # 清空表格
                self.models_table.setRowCount(0)
                
                # 填充模型数据
                for i, model in enumerate(display_models):
                    self.models_table.insertRow(i)
                    
                    self.models_table.setItem(i, 0, QTableWidgetItem(model["name"]))
                    self.models_table.setItem(i, 1, QTableWidgetItem(model["type"]))
                    self.models_table.setItem(i, 2, QTableWidgetItem(model["description"]))
                    
                    # 状态（简化显示）
                    status = "自定义" if model["is_custom"] else "预定义"
                    self.models_table.setItem(i, 3, QTableWidgetItem(status))
                    
                    # 文件大小（暂时显示为未知）
                    self.models_table.setItem(i, 4, QTableWidgetItem("未知"))
                    
                    # 操作按钮
                    action_layout = QHBoxLayout()
                    action_widget = QWidget()
                    
                    load_btn = ButtonStyleManager.create_button("加载", "small")
                    load_btn.clicked.connect(lambda checked, key=model["key"]: self._load_model(key))
                    
                    action_layout.addWidget(load_btn)
                    action_layout.setContentsMargins(2, 2, 2, 2)
                    action_widget.setLayout(action_layout)
                    
                    self.models_table.setCellWidget(i, 5, action_widget)
                    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载模型列表失败: {str(e)}")
    
    def _load_model(self, model_key: str):
        """加载模型"""
        if self.load_worker and self.load_worker.isRunning():
            QMessageBox.warning(self, "警告", "已有模型正在加载中，请稍候")
            return
        
        self.load_worker = SDWeightLoadWorker(self.sd_service, model_key)
        self.load_worker.progress.connect(self._on_load_progress)
        self.load_worker.finished.connect(self._on_load_finished)
        self.load_worker.error.connect(self._on_load_error)
        self.load_worker.start()
    
    def _on_load_progress(self, message: str):
        """加载进度"""
        # 可以在这里显示进度信息
        pass
    
    def _on_load_finished(self, result: Dict[str, Any]):
        """加载完成"""
        if result.get("success"):
            QMessageBox.information(self, "成功", "模型加载成功")
        else:
            error_message = result.get('message', '未知错误')

            # 检查是否是safetensors组件缺失错误
            if "文本编码器组件" in error_message:
                # 显示详细的错误信息和建议
                detailed_msg = f"模型加载失败:\n\n{error_message}"

                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("模型加载失败")
                msg_box.setIcon(QMessageBox.Icon.Warning)
                msg_box.setText("safetensors文件不完整")
                msg_box.setDetailedText(detailed_msg)
                msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                msg_box.exec()
            else:
                QMessageBox.warning(self, "失败", f"模型加载失败: {error_message}")
    
    def _on_load_error(self, error_msg: str):
        """加载错误"""
        QMessageBox.critical(self, "错误", f"模型加载时发生错误: {error_msg}")
    
    def _clear_all_cache(self):
        """清理所有缓存"""
        reply = QMessageBox.question(
            self, "确认", "确定要清理所有模型缓存吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                result = self.sd_service.clear_all_cache()
                if result.get("success"):
                    QMessageBox.information(self, "成功", "缓存清理完成")
                else:
                    QMessageBox.warning(self, "失败", f"清理失败: {result.get('message', '未知错误')}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"清理缓存时发生错误: {str(e)}")
