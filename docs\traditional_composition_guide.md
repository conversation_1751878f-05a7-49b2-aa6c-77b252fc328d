# 传统图像合成功能使用指南

## 概述

传统图像合成功能使用图像抠图和蒙版技术，将军事载具（坦克、战机、舰艇）自然融入到指定场景和天气环境中，生成高质量的合成图像。

## 功能特性

### 核心功能
- **智能抠图**: 支持GrabCut、分水岭、AI抠图、阈值等多种抠图算法
- **图层混合**: 提供10种混合模式，包括正常、正片叠底、滤色、叠加等
- **色彩匹配**: 自动调整前景与背景的色彩一致性
- **光照调整**: 根据场景光照条件自动调整载具的光照和色调
- **阴影生成**: 智能生成真实的阴影效果
- **天气效果**: 支持晴天、雨天、雪天、雾天、夜间等天气效果

### 技术特点
- **高质量合成**: 使用先进的图像处理算法确保合成效果自然
- **批量处理**: 支持批量图像合成，提高工作效率
- **性能优化**: 多线程处理、内存优化、智能缓存
- **参数可调**: 丰富的参数配置选项，满足不同需求

## 快速开始

### 1. 准备素材

在 `data/raw_assets/` 目录下准备以下素材：

```
data/raw_assets/
├── backgrounds/          # 背景场景图片
│   ├── city_01.jpg      # 城市场景
│   ├── desert_01.jpg    # 沙漠场景
│   └── ocean_01.jpg     # 海洋场景
└── targets/             # 军事载具图片
    ├── tank_01.jpg      # 坦克图片
    ├── aircraft_01.jpg  # 战机图片
    └── warship_01.jpg   # 舰艇图片
```

### 2. 基础使用

#### 前端界面操作

1. **选择目标与场景**
   - 军事目标：坦克、战机、舰艇
   - 天气条件：晴天、雨天、雪天、大雾、夜间
   - 地形场景：城市、岛屿、乡村、沙漠、海洋

2. **配置抠图参数**
   - 抠图方法：GrabCut（推荐）、分水岭、AI抠图、阈值
   - 迭代次数：1-20（默认5）
   - 羽化半径：0-20（默认3）
   - 边缘平滑：建议开启

3. **配置混合参数**
   - 混合模式：根据场景选择合适的混合模式
   - 透明度：0-100%（默认100%）
   - 色彩匹配：建议开启
   - 光照调整：建议开启
   - 生成阴影：建议开启

4. **生成配置**
   - 生成数量：1-50张
   - 图像尺寸：512x512、768x768、1024x1024

#### API调用

```python
from app.services.traditional.composition_service import TraditionalCompositionService

# 创建服务实例
service = TraditionalCompositionService()
await service.initialize()

# 生成图像
result = await service.generate_images(
    military_target="tank",
    weather="sunny",
    scene="urban",
    num_images=1,
    # 可选参数
    matting_method="grabcut",
    blend_mode="normal",
    opacity=1.0,
    target_size=(512, 512)
)

print(f"生成成功: {result['success']}")
print(f"生成数量: {result['total_generated']}")
```

## 高级配置

### 抠图算法选择

| 算法 | 适用场景 | 优点 | 缺点 |
|------|----------|------|------|
| GrabCut | 复杂背景 | 效果好，适应性强 | 处理时间较长 |
| 分水岭 | 边界清晰 | 速度快，边界精确 | 对噪声敏感 |
| AI抠图 | 所有场景 | 效果最好，全自动 | 需要额外依赖 |
| 阈值 | 简单背景 | 速度最快 | 效果有限 |

### 混合模式说明

| 模式 | 效果 | 适用场景 |
|------|------|----------|
| 正常 | 直接覆盖 | 通用场景 |
| 正片叠底 | 颜色变暗 | 夜间、阴天 |
| 滤色 | 颜色变亮 | 明亮天气 |
| 叠加 | 增强对比度 | 复杂场景 |
| 柔光 | 柔和对比度 | 自然光照 |

### 批量处理配置

```python
from app.services.traditional.batch_processor import BatchProcessor, BatchConfig

# 创建批量配置
config = BatchConfig(
    max_workers=4,              # 最大工作线程数
    use_multiprocessing=False,  # 是否使用多进程
    memory_limit_mb=2048,       # 内存限制
    chunk_size=10,              # 批次大小
    enable_memory_monitoring=True  # 启用内存监控
)

# 创建批量处理器
processor = BatchProcessor(config)
await processor.initialize()

# 批量处理
requests = [...]  # CompositionRequest列表
result = await processor.process_batch(requests)
```

## 性能优化

### 1. 硬件要求

**最低配置**
- CPU: 4核心
- 内存: 8GB
- 存储: 10GB可用空间

**推荐配置**
- CPU: 8核心或更多
- 内存: 16GB或更多
- 存储: SSD，20GB可用空间

### 2. 性能调优

#### 内存优化
```python
# 启用内存监控和垃圾回收
config = BatchConfig(
    memory_limit_mb=2048,
    enable_memory_monitoring=True,
    enable_gc=True
)
```

#### 并发优化
```python
# 根据硬件配置调整工作线程数
from app.services.traditional.batch_processor import get_optimal_worker_count

optimal_workers = get_optimal_worker_count()
config = BatchConfig(max_workers=optimal_workers)
```

#### 缓存优化
```python
# 使用优化的批量处理器
from app.services.traditional.batch_processor import OptimizedBatchProcessor

processor = OptimizedBatchProcessor(config)
# 自动预加载常用图像，使用智能缓存
```

## 故障排除

### 常见问题

1. **抠图效果不佳**
   - 尝试不同的抠图算法
   - 调整迭代次数和羽化半径
   - 检查输入图像质量

2. **合成效果不自然**
   - 启用色彩匹配和光照调整
   - 尝试不同的混合模式
   - 调整透明度

3. **处理速度慢**
   - 减少图像尺寸
   - 降低抠图迭代次数
   - 使用多线程处理

4. **内存不足**
   - 降低批次大小
   - 启用内存监控
   - 减少并发线程数

### 错误代码

| 错误代码 | 说明 | 解决方案 |
|----------|------|----------|
| MATTING_FAILED | 抠图失败 | 检查输入图像，尝试其他算法 |
| BLEND_FAILED | 混合失败 | 检查图像尺寸，调整混合参数 |
| MEMORY_EXCEEDED | 内存超限 | 降低批次大小，启用内存监控 |
| FILE_NOT_FOUND | 文件未找到 | 检查素材路径 |

## API参考

### 核心类

#### TraditionalCompositionService
主要的图像合成服务类。

```python
class TraditionalCompositionService:
    async def initialize() -> bool
    async def compose_image(request: CompositionRequest) -> ProcessingResult
    async def generate_images(military_target: str, weather: str, scene: str, **kwargs) -> Dict
    async def cleanup()
```

#### CompositionRequest
合成请求数据结构。

```python
@dataclass
class CompositionRequest:
    background_path: str
    target_path: str
    weather_effect: WeatherEffect
    military_target: str
    scene_type: str
    matting_config: MattingConfig
    blend_config: BlendConfig
    composition_config: CompositionConfig
    output_path: Optional[str] = None
```

### 配置类

#### MattingConfig
抠图配置参数。

```python
@dataclass
class MattingConfig:
    method: MattingMethod = MattingMethod.GRABCUT
    iterations: int = 5
    margin: int = 10
    feather_radius: int = 3
    edge_smooth: bool = True
    auto_refine: bool = True
    confidence_threshold: float = 0.8
```

#### BlendConfig
混合配置参数。

```python
@dataclass
class BlendConfig:
    mode: BlendMode = BlendMode.NORMAL
    opacity: float = 1.0
    color_match: bool = True
    lighting_adjust: bool = True
    shadow_generate: bool = True
    shadow_opacity: float = 0.3
    shadow_blur: int = 5
```

## 最佳实践

1. **素材准备**
   - 使用高质量的原始图像
   - 确保载具图像背景相对简单
   - 背景图像分辨率建议不低于512x512

2. **参数调优**
   - 根据图像复杂度选择合适的抠图算法
   - 根据场景特点选择合适的混合模式
   - 启用色彩匹配和光照调整以获得更好效果

3. **批量处理**
   - 合理设置批次大小，避免内存溢出
   - 使用多线程提高处理效率
   - 启用内存监控和垃圾回收

4. **质量控制**
   - 定期检查合成结果质量
   - 根据反馈调整参数配置
   - 建立质量评估标准

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础图像合成功能
- 提供多种抠图和混合算法
- 实现批量处理和性能优化
