"""
数据管理模块样式定义
与统一主题保持一致
"""

from .unified_theme import COLORS, FONTS, SPACING, RADIUS

# 数据集管理组件样式
DATASET_MANAGEMENT_STYLES = {
    # 统计信息标签样式
    "stats_label": f"""
        QLabel {{
            color: {COLORS['text_primary']};
            font-size: {FONTS['size_base']};
            padding: {SPACING['base']};
            background-color: {COLORS['bg_secondary']};
            border: 1px solid {COLORS['border_light']};
            border-radius: {RADIUS['md']};
            margin-top: {SPACING['sm']};
            font-family: {FONTS['family_primary']};
        }}
    """,
    
    # 状态指示器样式
    "status_loading": f"""
        QLabel {{
            color: {COLORS['primary']};
            font-size: {FONTS['size_sm']};
            padding: {SPACING['sm']} {SPACING['md']};
            background-color: {COLORS['primary_light']};
            border: 1px solid {COLORS['primary']};
            border-radius: {RADIUS['base']};
            margin-top: {SPACING['xs']};
            font-family: {FONTS['family_primary']};
        }}
    """,

    "status_success": f"""
        QLabel {{
            color: {COLORS['success']};
            font-size: {FONTS['size_sm']};
            padding: {SPACING['sm']} {SPACING['md']};
            background-color: {COLORS['success_light']};
            border: 1px solid {COLORS['success']};
            border-radius: {RADIUS['base']};
            margin-top: {SPACING['xs']};
            font-family: {FONTS['family_primary']};
        }}
    """,

    "status_error": f"""
        QLabel {{
            color: {COLORS['danger']};
            font-size: {FONTS['size_sm']};
            padding: {SPACING['sm']} {SPACING['md']};
            background-color: {COLORS['danger_light']};
            border: 1px solid {COLORS['danger']};
            border-radius: {RADIUS['base']};
            margin-top: {SPACING['xs']};
            font-family: {FONTS['family_primary']};
        }}
    """,

    "status_warning": f"""
        QLabel {{
            color: {COLORS['warning']};
            font-size: {FONTS['size_sm']};
            padding: {SPACING['sm']} {SPACING['md']};
            background-color: {COLORS['warning_light']};
            border: 1px solid {COLORS['warning']};
            border-radius: {RADIUS['base']};
            margin-top: {SPACING['xs']};
            font-family: {FONTS['family_primary']};
        }}
    """,

    "status_info": f"""
        QLabel {{
            color: {COLORS['info']};
            font-size: {FONTS['size_sm']};
            padding: {SPACING['sm']} {SPACING['md']};
            background-color: {COLORS['info_light']};
            border: 1px solid {COLORS['info']};
            border-radius: {RADIUS['base']};
            margin-top: {SPACING['xs']};
            font-family: {FONTS['family_primary']};
        }}
    """,
    
    # 图片详情组件样式
    "image_preview": f"""
        QLabel {{
            border: 2px dashed {COLORS['border_medium']};
            border-radius: {RADIUS['lg']};
            background-color: {COLORS['bg_secondary']};
            min-height: 200px;
            color: {COLORS['text_primary']};
            font-family: {FONTS['family_primary']};
        }}
    """,

    "detail_text_edit": f"""
        QTextEdit {{
            background-color: {COLORS['bg_primary']};
            border: 1px solid {COLORS['border_light']};
            border-radius: {RADIUS['md']};
            padding: {SPACING['base']};
            font-family: {FONTS['family_mono']};
            font-size: {FONTS['size_sm']};
            color: {COLORS['text_primary']};
        }}
    """,
    
    # 对话框样式
    "dialog_preview": f"""
        QLabel {{
            background-color: {COLORS['bg_secondary']};
            border: 1px solid {COLORS['border_light']};
            padding: {SPACING['base']};
            border-radius: {RADIUS['md']};
            font-family: {FONTS['family_mono']};
            color: {COLORS['text_primary']};
        }}
    """,

    "dialog_warning": f"""
        QLabel {{
            background-color: {COLORS['danger_light']};
            border: 1px solid {COLORS['danger']};
            border-radius: {RADIUS['md']};
            padding: {SPACING['lg']};
            color: {COLORS['danger']};
            font-weight: {FONTS['weight_bold']};
            font-family: {FONTS['family_primary']};
        }}
    """,

    "dialog_impact": f"""
        QLabel {{
            background-color: {COLORS['warning_light']};
            border: 1px solid {COLORS['warning']};
            border-radius: {RADIUS['md']};
            padding: {SPACING['md']};
            color: {COLORS['warning']};
            font-family: {FONTS['family_primary']};
        }}
    """,
    
    # 列表项样式
    "list_widget": f"""
        QListWidget {{
            background-color: {COLORS['bg_primary']};
            border: 1px solid {COLORS['border_light']};
            border-radius: {RADIUS['md']};
            padding: {SPACING['sm']};
            font-family: {FONTS['family_primary']};
        }}

        QListWidget::item {{
            background-color: {COLORS['bg_primary']};
            border: 1px solid {COLORS['border_light']};
            border-radius: {RADIUS['base']};
            padding: {SPACING['base']};
            margin: 2px;
            color: {COLORS['text_primary']};
        }}

        QListWidget::item:selected {{
            background-color: {COLORS['primary_light']};
            border-color: {COLORS['primary']};
            color: {COLORS['primary']};
        }}

        QListWidget::item:hover {{
            background-color: {COLORS['hover_bg']};
            border-color: {COLORS['border_medium']};
        }}
    """,
    
    # 表单输入样式
    "form_input": f"""
        QLineEdit {{
            background-color: {COLORS['bg_primary']};
            padding: {SPACING['md']};
            border: 1px solid {COLORS['border_light']};
            border-radius: {RADIUS['md']};
            font-size: {FONTS['size_base']};
            color: {COLORS['text_primary']};
            font-family: {FONTS['family_primary']};
        }}

        QLineEdit:focus {{
            border: 2px solid {COLORS['focus_border']};
        }}
    """,

    # 复选框样式
    "checkbox_danger": f"""
        QCheckBox {{
            spacing: {SPACING['base']};
            font-size: {FONTS['size_base']};
            padding: {SPACING['xs']};
            font-weight: {FONTS['weight_bold']};
            color: {COLORS['danger']};
            font-family: {FONTS['family_primary']};
        }}

        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {COLORS['danger']};
            border-radius: {RADIUS['sm']};
            background-color: {COLORS['bg_primary']};
        }}

        QCheckBox::indicator:checked {{
            background-color: {COLORS['danger']};
            border-color: {COLORS['danger']};
        }}
    """
}

def get_style(style_name):
    """获取指定的样式"""
    return DATASET_MANAGEMENT_STYLES.get(style_name, "")

def apply_dataset_styles(widget, style_name):
    """为组件应用数据集样式"""
    style = get_style(style_name)
    if style:
        widget.setStyleSheet(style)

# 状态样式映射
STATUS_STYLES = {
    "loading": "status_loading",
    "success": "status_success", 
    "error": "status_error",
    "warning": "status_warning",
    "info": "status_info"
}

def get_status_style(status_type):
    """获取状态样式"""
    style_name = STATUS_STYLES.get(status_type, "status_info")
    return get_style(style_name)

# 中文字体设置
CHINESE_FONT_CONFIG = {
    "family": "Microsoft YaHei",
    "fallback": ["Segoe UI", "sans-serif"],
    "size": 10  # 使用统一主题的基础字体大小
}

def setup_chinese_font(widget):
    """设置中文字体"""
    from PyQt6.QtGui import QFont

    font = QFont()
    font.setFamily(CHINESE_FONT_CONFIG["family"])
    font.setPointSize(CHINESE_FONT_CONFIG["size"])
    widget.setFont(font)
